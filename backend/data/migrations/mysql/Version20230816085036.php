<?php

declare(strict_types=1);

namespace Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230816085036 extends AbstractMigration {

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function up(Schema $schema): void
    {
        $table = $schema->getTable('users_companies_languages');
        $table->addForeignKeyConstraint('users_companies_roles', ['user_id', 'company_id'], ['user_id', 'company_id'], [
            'onUpdate' => 'CASCADE',
            'onDelete' => 'CASCADE',
        ], 'FK_33D60DB1A76ED395979B1AD6');
    }

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function down(Schema $schema): void
    {
        $table = $schema->getTable('users_companies_languages');
        $table->removeForeignKey('FK_33D60DB1A76ED395979B1AD6');
    }

}
