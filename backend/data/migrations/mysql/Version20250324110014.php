<?php

declare(strict_types=1);

namespace Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250324110014 extends AbstractMigration
{
    const string UNIQUE_INDEX_NAME = 'checklists_role_id_unqi';
    const string FOREIGN_INDEX_NAME = 'FK_B0839B3FD60322AC';

    /**
     *
     * @param Schema $schema
     * @return void
     */
    public function up(Schema $schema): void
    {
        $table = $schema->getTable('checklists');

        $table->dropIndex(self::UNIQUE_INDEX_NAME);
        $table->removeForeignKey(self::FOREIGN_INDEX_NAME);
        $table->dropColumn('role_id');
    }

    /**
     *
     * @param Schema $schema
     * @return void
     */
    public function down(Schema $schema): void
    {
        $table = $schema->getTable('checklists');

        $table->addColumn('role_id', 'bigint', [
            'notnull' => false,
            'unsigned' => true,
        ]);

        $table->addForeignKeyConstraint('roles', ['role_id'], ['role_id'], [
            'onUpdate' => 'CASCADE',
            'onDelete' => 'CASCADE',
        ]);

        $table->addUniqueIndex([
            'role_id',
        ], self::UNIQUE_INDEX_NAME);
    }
}
