<?php

declare(strict_types=1);

namespace Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230217120041 extends AbstractMigration {

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function up(Schema $schema): void
    {
        $table = $schema->createTable('events_hints');
        
        $table->addColumn('hint_id', 'integer', [
            'notnull' => true,
            'autoincrement' => true,
            'unsigned' => true,
        ]);
        
        $table->addColumn('algo_event', 'string', [
            'notnull' => true,
            'length' => 255,
        ]);

        $table->addColumn('algo_api_category_id', 'integer', [
            'notnull' => true,
            'unsigned' => true,
        ]);
        
        $table->addColumn('text', 'string', [
            'notnull' => true,
            'length' => 255,
        ]);

        $table->setPrimaryKey([
            'hint_id'
        ]);
    }

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function down(Schema $schema): void
    {
        $schema->dropTable('events_hints');
    }

}
