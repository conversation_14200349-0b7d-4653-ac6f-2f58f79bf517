<?php

declare(strict_types=1);

namespace Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20241204145609 extends AbstractMigration {

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function up(Schema $schema): void
    {
        $table = $schema->createTable('algo_apis_versions');

        $table->addColumn('version_id', 'bigint', [
            'notnull' => true,
            'autoincrement' => true,
            'unsigned' => true,
        ]);
        $table->setPrimaryKey(['version_id']);

        $table->addColumn('algo_api_id', 'integer', [
            'notnull' => true,
            'unsigned' => true,
        ]);
        $table->addForeignKeyConstraint('algo_apis', ['algo_api_id'], ['algo_api_id'], [
            'onUpdate' => 'CASCADE',
            'onDelete' => 'CASCADE',
        ]);

        $table->addColumn('version', 'string', ['notnull' => true, 'length' => 255]);

        $table->addColumn('is_actual', 'boolean', [
            'notnull' => true,
            'unsigned' => true,
            'default' => 0,
        ]);
        $table->addColumn('is_deleted', 'boolean', [
            'notnull' => true,
            'unsigned' => true,
            'default' => 0,
        ]);

        $table->addUniqueIndex([
            'algo_api_id',
            'version',
        ]);
    }

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function down(Schema $schema): void
    {
        $schema->dropTable('algo_apis_versions');
    }
}
