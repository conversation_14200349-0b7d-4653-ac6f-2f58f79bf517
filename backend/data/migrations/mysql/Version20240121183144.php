<?php

declare(strict_types=1);

namespace Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240121183144 extends AbstractMigration {

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function up(Schema $schema): void
    {
        $this->connection->executeQuery('
            ALTER TABLE
                fronts
            ADD COLUMN
                favicon text NOT NULL
            AFTER
                logo;
        ');
        $this->connection->executeQuery('
            UPDATE
                fronts
            SET
                favicon = "data:image/png;base64,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"
            ;
        ');
    }

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function down(Schema $schema): void
    {
        $table = $schema->getTable('fronts');
        $table->dropColumn('favicon');
    }

}
