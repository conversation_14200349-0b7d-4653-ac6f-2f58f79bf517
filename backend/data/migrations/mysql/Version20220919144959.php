<?php

declare(strict_types=1);

namespace Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20220919144959 extends AbstractMigration
{
    
    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function up(Schema $schema): void {
        $schema->dropTable('roles_reports');
    }

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function down(Schema $schema): void {
        $table = $schema->createTable('roles_reports');
        $table->addColumn('role_id', 'bigint', [
            'notnull' => true,
            'unsigned' => true,
        ]);
        $table->addColumn('report_id', 'smallint', [
            'notnull' => true,
            'unsigned' => true,
        ]);
        $table->setPrimaryKey([
            'role_id',
            'report_id',
        ]);

        $table->addForeignKeyConstraint('roles', ['role_id'], ['role_id'], [
            'onUpdate' => 'CASCADE',
            'onDelete' => 'CASCADE',
        ]);

        $table->addForeignKeyConstraint('reports', ['report_id'], ['report_id'], [
            'onUpdate' => 'CASCADE',
            'onDelete' => 'CASCADE',
        ]);

        $table->addOption('charset', 'utf8mb4');
        $table->addOption('collation', 'utf8mb4_unicode_ci');
        $table->addOption('engine', 'InnoDB');
    }
    
}
