<?php

declare(strict_types=1);

namespace Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230904160822 extends AbstractMigration {

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function up(Schema $schema): void
    {
        $table = $schema->getTable('users');
        $table->addColumn('is_global_admin', 'boolean', [
            'notnull' => true,
            'unsigned' => true,
            'default' => 0,
        ]);
    }
    
    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function postUp(Schema $schema): void {
        $this->connection->executeQuery('
            UPDATE
                users
            SET
                is_global_admin = 1
            WHERE
                user_email IN (
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>"
                )
        ');
    }

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function down(Schema $schema): void
    {
        $table = $schema->getTable('users');
        $table->dropColumn('is_global_admin');
    }

}
