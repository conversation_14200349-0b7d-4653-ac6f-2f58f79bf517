<?php

declare(strict_types=1);

namespace Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230221140612 extends AbstractMigration {

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function up(Schema $schema): void
    {
        $this->connection->executeQuery('
            INSERT INTO
                events_hints
                (algo_event, algo_api_category_id, text)
            VALUES
                ("misleading", 1, "Including promise of profit, timeframe of profit, exotic assets, withdrawal, no risk, safe assets/time, money not used, limit the risk, minimized the risk"),
                ("advice", 1, "Including advice for asset, volume, SL/TP, direction, keep trades running, plan, personal openinn, 3 party sources, options for margin level"),
                ("deposit", 1, "Agent and client are speaking about deposit money"),
                ("decoy", 1, "Wrong information about Department / Agent\'s profession / Location / Commission only on profits / Introduction"),
                ("unfit", 1, "Unprofessional/impolite behavior / Using clients account / Questionnaire help"),
                ("website", 1, "Client is on the website"),
                ("client is unhappy", 1, "Client complains about the customer support / GDPR / client claim he never been contacted before / clients complain about the retention agent / client said he will call his lawyer"),
                ("pressure", 1, "Push for deposit or Agents job is \"to push\" the client"),
                ("not interested", 1, "Client is not interested in trading"),
                ("risk disclaimer", 1, "Agent give risk disclaimer about trading"),
                ("copy trading", 1, "Agent give explanation about copy trading"),
                ("advice for tp or sl", 1, "Agent give an advice about take profit or stop loss"),
                ("advice for volume", 1, "Agents give advice about specific volumes for a trade."),
                ("advice of asset", 1, "An agent should not advise or show an asset in the light of an opportunity, agent should no encourage the client to trade on a specific asset"),
                ("advice of direction", 1, "Agent must not advise about the correct, needed, advised, direction of the specific asset like buy to go up, sell to go down"),
                ("being exposed to clients private information", 1, "Agent should not be exposed to client private information like : bank account, credit card, social security number, privet phone etc"),
                ("commission only on profits", 1, "Agents cannot explain the client the company/agent make his commission only on profit"),
                ("complaint", 1, "Includes any cases when client is complain or have some kind of concerns"),
                ("confirmation of adulthood", 1, "Agent must confirm that the customer is over 18"),
                ("direct order for closing trade or trading", 1, "Agent cannot give to a client orders to close trades, this applies both to positive and negative trades"),
                ("documents", 1, "will show al the times the agent speaks about document collection"),
                ("inappropriate behavior", 1, "Includes cases when rep humiliates Client in any way; Agent is rude and aggressive."),
                ("messengers", 1, "Agents shouldn\'t use any private communication with clients like Skype, Whatsapp, Telegram, private phone, or private mail"),
                ("plan", 1, "Agent cannot make a trading plan for a client"),
                ("pressuring the client", 1, "Agent pushes the client to deposit even when the client says that he doesn\'t want to/doesn\'t have money etc."),
                ("promise of profits", 1, "Agents misinform clients about potential of the market and that leads clients to believe that they will have profit."),
                ("timeframes for profits", 1, "Agents misinform clients about potential of the market and that leads clients to believe that they will have profit."),
                ("share screen", 1, "Agent should not share screen using anydesk or teamviewer"),
                ("wd procedure explanation", 1, "Agent provides incorrect info about withdrawal timeframes, fees etc."),
                ("wrong information", 1, "Agents should not give any misleading information about basic principles on how a company, Forex and CFD market work"),
                ("concerns", 3, "Include any kind of concern the client mention"),
                ("open account and activation", 3, "Client has activate his account or registered"),
                ("call back", 3, "Agent and client are schedule call back"),
                ("follow up call", 3, "Client confirm this is a follow up call"),
                ("postponed call", 3, "Client ask the agent to call him later"),
                ("client is available", 3, "Client confirm he is available to talk"),
                ("interest in assets", 3, "Mention specific assets he is interested to trade on"),
                ("interested", 3, "Client mention he is interested in trading"),
                ("not interested", 3, "Client said he is not interested in trading"),
                ("client\'s experience", 3, "Client mention his experience in trading"),
                ("marketing advertising", 3, "Where the client came from / what the client saw, which marketing materials did he got exposed to"),
                ("trading return", 3, "How much the client earn in the past from trading / how much he is gaining at the moment"),
                ("unemployed", 3, "Client said he is unemployed"),
                ("employed/retired", 3, "Client said he has job/ he retired"),
                ("income", 3, "Client mention his monthly salary, or yearly income"),
                ("age", 3, "Client mention his age"),
                ("occupation", 3, "Client mention what he is doing for living"),
                ("family status", 3, "Client mention if he is married or and have kids"),
                ("location", 3, "Client mention about where he is living or where he is originally from, or where he is at the moment"),
                ("credit card", 3, "Client mention which credit card he has, or the way he wants to make a deposit"),
                ("questionnaire", 3, "Client is answering the questionnaire"),
                ("decline deposit", 3, "Client mentioned a deposit fail, or something with the deposit was not right"),
                ("deposit", 3, "Client try to deposit money in his trading account"),
                ("objection", 3, "Client mention any kind of objection (don\'t have time, don\'t have money, need to ask my wife)")
        ');
    }

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function down(Schema $schema): void
    {
        $this->connection->executeQuery('
            TRUNCATE TABLE
                events_hints
        ');
    }

}
