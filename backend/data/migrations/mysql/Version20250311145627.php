<?php

declare(strict_types=1);

namespace Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250311145627 extends AbstractMigration {

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function up(Schema $schema): void
    {
        $table = $schema->getTable('checklists_points');
        $table->addColumn('is_optional', 'boolean', [
            'notnull' => true,
            'unsigned' => true,
            'default' => 0,
        ]);
        $table->addColumn('trigger_condition', 'string', [
            'notnull' => false,
            'length' => 1024,
        ]);
    }

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function down(Schema $schema): void
    {
        $table = $schema->getTable('checklists_points');
        $table->dropColumn('is_optional');
        $table->dropColumn('trigger_condition');
    }

}
