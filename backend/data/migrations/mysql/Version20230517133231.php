<?php

declare(strict_types=1);

namespace Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230517133231 extends AbstractMigration {

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function up(Schema $schema): void
    {
        $this->connection->executeQuery('
            UPDATE
                events_colors
            SET
                outline_hex = "D33333"
            WHERE
                name = "red"
        ');
        $this->connection->executeQuery('
            UPDATE
                events_colors
            SET
                outline_hex = "54AA48",
                fill_hex = "A5CF9F"
            WHERE
                name = "green"
        ');
        $this->connection->executeQuery('
            UPDATE
                events_colors
            SET
                outline_hex = "1E90FF"
            WHERE
                name = "blue"
        ');
        $this->connection->executeQuery('
            UPDATE
                events_colors
            SET
                outline_hex = "FFA905"
            WHERE
                name = "orange"
        ');
        $this->connection->executeQuery('
            UPDATE
                events_colors
            SET
                fill_hex = "C88D5A",
                outline_hex = "793A00"
            WHERE
                name = "brown"
        ');
        $this->connection->executeQuery('
            UPDATE
                events_colors
            SET
                outline_hex = "8D8D8D"
            WHERE
                name = "grey"
        ');
        $this->connection->executeQuery('
            UPDATE
                events_colors
            SET
                name = "dark orange",
                fill_hex = "FEAD61",
                outline_hex = "EA7000"
            WHERE
                name = "soft orange"
        ');
        $this->connection->executeQuery('
            UPDATE
                events_colors
            SET
                name = "orchid",
                fill_hex = "E6AAE3",
                outline_hex = "D65ED1"
            WHERE
                name = "yellow"
        ');
        $this->connection->executeQuery('
            INSERT INTO
                events_colors
            VALUES
                (9, "dark violet", "C57BE4", "9400D3", 9),
                (10, "dark cyan", "7BC2C7", "009099", 10),
                (11, "dark green", "4DD482", "00752F", 11)
        ');
    }

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function down(Schema $schema): void
    {
        $this->connection->executeQuery('
            UPDATE
                events_colors
            SET
                name = "yellow",
                fill_hex = "ECEEA2",
                outline_hex = NULL
            WHERE
                name = "orchid"
        ');
        $this->connection->executeQuery('
            UPDATE
                events_colors
            SET
                name = "soft orange",
                fill_hex = "FCE1C6",
                outline_hex = NULL
            WHERE
                name = "dark orange"
        ');
        $this->connection->executeQuery('
            UPDATE
                events_colors
            SET
                fill_hex = "B4D9B8"
            WHERE
                name = "green"
        ');
        $this->connection->executeQuery('
            UPDATE
                events_colors
            SET
                fill_hex = "CBC5BC"
            WHERE
                name = "brown"
        ');
        $this->connection->executeQuery('
            DELETE
            FROM
                events_colors
            WHERE
                name IN (
                    "dark violet",
                    "dark cyan",
                    "dark green"
                )
        ');
    }

}
