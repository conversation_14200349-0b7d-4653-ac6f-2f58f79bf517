<?php

declare(strict_types=1);

namespace Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20220929045724 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function up(Schema $schema): void
    {
        $table = $schema->getTable('companies');

        $table->modifyColumn('lower_threshold_bar', [
            'type' => new \Doctrine\DBAL\Types\DecimalType(),
            'notnull' => true,
            'precision' => 6,
            'scale' => 5,
            'default' => 0,
        ]);

        $table->modifyColumn('upper_threshold_bar', [
            'type' => new \Doctrine\DBAL\Types\DecimalType(),
            'notnull' => true,
            'precision' => 6,
            'scale' => 5,
            'default' => 0,
        ]);

    }

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function down(Schema $schema): void
    {
        $table = $schema->getTable('companies');

        $table->modifyColumn('lower_threshold_bar', [
            'type' => new \Doctrine\DBAL\Types\BooleanType(),
            'notnull' => true,
            'unsigned' => true,
            'default' => 30,
        ]);

        $table->modifyColumn('upper_threshold_bar', [
            'type' => new \Doctrine\DBAL\Types\BooleanType(),
            'notnull' => true,
            'unsigned' => true,
            'default' => 70,
        ]);
    }
}
