<?php

declare(strict_types=1);

namespace Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20221001170425 extends AbstractMigration {

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function up(Schema $schema): void
    {
        $table = $schema->getTable('users_companies_roles');
        $table->modifyColumn('role_id', [
            'type' => new \Doctrine\DBAL\Types\BigIntType(),
            'notnull' => false,
            'unsigned' => true,
        ]);
    }

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function down(Schema $schema): void
    {
        $table = $schema->getTable('users_companies_roles');
        $table->modifyColumn('role_id', [
            'type' => new \Doctrine\DBAL\Types\BigIntType(),
            'notnull' => true,
            'unsigned' => true,
        ]);
    }

}
