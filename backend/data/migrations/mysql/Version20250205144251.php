<?php

declare(strict_types=1);

namespace Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250205144251 extends AbstractMigration {

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function up(Schema $schema): void
    {
        $table = $schema->getTable('companies');
        $table->addColumn(
            'front_id',
            'bigint',
            ['notnull' => true, 'default' => 1, 'unsigned' => true]
        );
        $table->addForeignKeyConstraint(
            'fronts',
            ['front_id'],
            ['front_id'],
            [
                'onUpdate' => 'CASCADE',
                'onDelete' => 'CASCADE',
            ],
            'fk_company_front_id'
        );
    }

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function down(Schema $schema): void
    {
        $table = $schema->getTable('companies');
        $table->removeForeignKey('fk_company_front_id');
        $table->dropColumn('front_id');
    }

}
