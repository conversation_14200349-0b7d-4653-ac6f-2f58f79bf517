<?php

declare(strict_types=1);

namespace Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250128100035 extends AbstractMigration {

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function up(Schema $schema): void
    {
        $table = $schema->getTable('onboarding_forms');
        $table->addColumn('invite_link', 'string', ['notnull' => true, 'length' => 255]);

        $table->addColumn('company_id', 'bigint', [
            'notnull' => false,
            'unsigned' => true,
        ]);

        $table->addForeignKeyConstraint('companies', ['company_id'], ['company_id'], [
            'onUpdate' => 'CASCADE',
            'onDelete' => 'CASCADE',
        ]);
    }

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function down(Schema $schema): void
    {
        $table = $schema->getTable('onboarding_forms');
        $table->dropColumn('company_id');
        $table->dropColumn('invite_link');
    }
}
