<?php

declare(strict_types=1);

namespace Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230111114828 extends AbstractMigration {

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function up(Schema $schema): void
    {
        $table = $schema->getTable('users');

        $table->modifyColumn('user_avatar', [
            'type' => new \Doctrine\DBAL\Types\TextType(),
            'notnull' => false,
            'length' => 16777215,
        ]);
    }

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function down(Schema $schema): void
    {
        $table = $schema->getTable('users');

        $table->modifyColumn('user_avatar', [
            'type' => new \Doctrine\DBAL\Types\TextType(),
            'notnull' => false,
            'length' => 65535,
        ]);
    }

}
