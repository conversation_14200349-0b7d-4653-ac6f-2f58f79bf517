<?php

declare(strict_types=1);

namespace Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20231226113323 extends AbstractMigration {

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function up(Schema $schema): void
    {
        $table = $schema->getTable('users');
        $table->addColumn('front_id_global_admin', 'bigint', [
            'notnull' => false,
            'unsigned' => true,
            'default' => null,
        ]);
        
        $table->addForeignKeyConstraint('fronts', ['front_id_global_admin'], ['front_id'], [
            'onUpdate' => 'CASCADE',
            'onDelete' => 'SET NULL',
        ], 'FK_1483A5E96EFF14B9');
    }

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function down(Schema $schema): void
    {
        $table = $schema->getTable('users');
        $table->removeForeignKey('FK_1483A5E96EFF14B9');
        $table->dropColumn('front_id_global_admin');
    }

}
