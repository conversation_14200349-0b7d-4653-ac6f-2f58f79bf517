<?php

declare(strict_types=1);

namespace Migrations;

use Doctrine\DBAL\Exception;
use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use STCompany\Data\PermissionsTable;
use STCompany\Entity\Role;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20241022153114 extends AbstractMigration {

    /**
     *
     * @param Schema $schema
     * @return void
     * @throws Exception
     */
    public function up(Schema $schema): void
    {
        $this->connection->executeQuery('
                INSERT INTO
                    roles_permissions
                    (
                        role_id,
                        permission_id,
                        access_level
                    )
                SELECT
                    role_id,
                    32 as permission_id,
                    \'write\' access_level
                FROM 
                    roles r
                WHERE r.role_type IN (2, 5)
            ');
    }

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function down(Schema $schema): void
    {
        $this->connection->executeQuery('
            DELETE
            FROM
                roles_permissions
            WHERE
                permission_id = 32
        ');
    }

}
