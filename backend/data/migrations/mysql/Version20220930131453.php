<?php

declare(strict_types=1);

namespace Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20220930131453 extends AbstractMigration {

    public function up(Schema $schema): void
    {
        $this->connection->executeQuery('
            INSERT INTO
                permissions
                (permission_id, parent_permission_id, permission_name)
            VALUES
                (21, 1, "Manual calls upload")
        ');
    }

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function down(Schema $schema): void
    {
        $this->connection->executeQuery('
            DELETE FROM
                permissions
            WHERE
                permission_id = 21
        ');
    }
}
