<?php

declare(strict_types=1);

namespace Migrations;

use Doctrine\DBAL\Exception;
use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250421105507 extends AbstractMigration
{
    /**
     * @param Schema $schema
     * @return void
     * @throws Exception
     */
    public function up(Schema $schema): void
    {
        $this->connection->executeQuery(
            '
             INSERT INTO
                permissions
                (
                    permission_id,
                    permission_name,
                    system_name
                )
            VALUES
                (34, "Webhooks", "webhooks")
        '
        );
    }

    /**
     * @param Schema $schema
     * @return void
     * @throws Exception
     */
    public function down(Schema $schema): void
    {
        $this->connection->executeQuery(
            '
            DELETE
            FROM
                permissions
            WHERE
                permission_id = 34
        '
        );
    }
}
