<?php

declare(strict_types=1);

namespace Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20220922092606 extends AbstractMigration
{
    
    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function up(Schema $schema): void {
        $schema->dropTable('features');
    }

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function down(Schema $schema): void {
        $table = $schema->createTable('features');
        $table->addColumn('feature_id', 'smallint', [
            'notnull' => true,
            'unsigned' => true,
        ]);
        $table->addColumn('parent_feature_id', 'smallint', [
            'notnull' => false,
            'unsigned' => true,
            'default' => null,
        ]);
        $table->addColumn('feature_name', 'string', [
            'notnull' => true,
            'length' => 255,
        ]);

        $table->setPrimaryKey([
            'feature_id',
        ]);
        
        $table->addForeignKeyConstraint('features', ['parent_feature_id'], ['feature_id'], [
            'onUpdate' => 'CASCADE',
            'onDelete' => 'CASCADE',
        ]);

        $table->addOption('charset', 'utf8mb4');
        $table->addOption('collation', 'utf8mb4_unicode_ci');
        $table->addOption('engine', 'InnoDB');
    }
    
    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function postDown(Schema $schema): void {
        $this->connection->executeQuery('
            INSERT INTO
                features
                (feature_id, parent_feature_id, feature_name)
            VALUES
                (1, null, "Company"),
                (2, null, "Dashboard"),
                (3, null, "Call statistics"),
                (4, null, "Severe violations"),
                (5, null, "Clients"),
                (6, null, "Manager statistics"),
                (7, null, "Users"),
                (8, null, "Iconery"),
                (9, 1, "Payments"),
                (10, 1, "Profile"),
                (11, 1, "Users"),
                (12, 1, "Teams"),
                (13, 2, "Calls overview"),
                (14, 2, "Flags"),
                (15, 2, "Agents"),
                (16, 2, "Clients"),
                (17, 3, "Group name"),
                (18, 3, "Flag statistics"),
                (19, 3, "Time on phone")
        ');
    }
    
}
