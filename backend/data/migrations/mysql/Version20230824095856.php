<?php

declare(strict_types=1);

namespace Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20230824095856 extends AbstractMigration {
    const TABLE_NAME = 'users_companies_roles';

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function up(Schema $schema): void
    {
        $table = $schema->getTable(self::TABLE_NAME);
        $table->addColumn('is_auto_analyze_calls', 'boolean', [
            'notnull' => true,
            'default' => 0,
            'unsigned' => true,
        ]);
    }

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function down(Schema $schema): void
    {
        $table = $schema->getTable(self::TABLE_NAME);
        $table->dropColumn('is_auto_analyze_calls');
    }

}
