<?php

declare(strict_types=1);

namespace Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20221104143720 extends AbstractMigration {

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function up(Schema $schema): void
    {
        $table = $schema->getTable('algo_apis');
        $table->addColumn('algo_api_category_id', 'smallint', [
            'notnull' => true,
            'unsigned' => true,
            'default' => 1,
        ]);
        
        $table->addForeignKeyConstraint('algo_apis_categories', ['algo_api_category_id'], ['algo_api_category_id'], [
            'onUpdate' => 'CASCADE',
            'onDelete' => 'CASCADE',
        ], 'FK_8DFDAB1D9420C7B7');
    }
    
    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function postUp(Schema $schema): void {
        $this->connection->executeQuery('
            UPDATE
                algo_apis
            SET
                algo_api_category_id = 4
            WHERE
                path = "https://algo.robonote.io/api/v9"
        ');
        $this->connection->executeQuery('
            UPDATE
                algo_apis
            SET
                algo_api_category_id = 3
            WHERE
                path IN (
                    "https://algo.robonote.io/api/v6",
                    "https://algo16.robonote.io/api/v6",
                    "https://algo2.robonote.io/api/v6"
                )
        ');
        $this->connection->executeQuery('
            INSERT INTO
                algo_apis
             (path, algo_api_category_id)
            VALUES
                ("https://algo.robonote.io/api/v7", 2),
                ("https://algo16.robonote.io/api/v7", 2),
                ("https://algo2.robonote.io/api/v7", 2)
        ');
    }

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function down(Schema $schema): void
    {
        $table = $schema->getTable('algo_apis');
        $table->removeForeignKey('FK_8DFDAB1D9420C7B7');
        $table->dropColumn('algo_api_category_id');
    }

}
