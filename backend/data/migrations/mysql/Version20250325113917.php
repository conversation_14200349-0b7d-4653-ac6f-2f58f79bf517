<?php

declare(strict_types=1);

namespace Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250325113917 extends AbstractMigration {

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function up(Schema $schema): void
    {
        $this->connection->executeQuery('
            ALTER TABLE
                checklists
            ADD COLUMN
                calls_teams LONGTEXT DEFAULT NULL
            AFTER
                name;
        ');
        $this->connection->executeQuery('
            ALTER TABLE
                checklists
            ADD COLUMN
                calls_statuses LONGTEXT DEFAULT NULL
            AFTER
                name;
        ');
        $this->connection->executeQuery('
            ALTER TABLE
                checklists
            ADD COLUMN
                max_calls_count MEDIUMINT UNSIGNED DEFAULT NULL
            AFTER
                name;
        ');
        $this->connection->executeQuery('
            ALTER TABLE
                checklists
            ADD COLUMN
                description VARCHAR(1024) DEFAULT NULL
            AFTER
                name;
        ');
    }

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function down(Schema $schema): void
    {
        $table = $schema->getTable('checklists');

        $table->dropColumn('calls_teams');
        $table->dropColumn('calls_statuses');
        $table->dropColumn('max_calls_count');
        $table->dropColumn('description');
    }
}
