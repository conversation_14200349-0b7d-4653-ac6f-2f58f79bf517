<?php

declare(strict_types=1);

namespace Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250519120719 extends AbstractMigration {

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function up(Schema $schema): void
    {
        $table = $schema->createTable('companies_details');
        
        $table->addColumn('company_id', 'bigint', ['unsigned' => true]);
        $table->addColumn('industry', 'string', ['length' => 1024, 'notnull' => false]);
        $table->addColumn('products_services', 'string', ['length' => 1024, 'notnull' => false]);
        $table->addColumn('sales_model', 'string', ['length' => 1024, 'notnull' => false]);
        $table->addColumn('key_goals', 'string', ['length' => 1024, 'notnull' => false]);
        $table->addColumn('call_types_categories', 'string', ['length' => 1024, 'notnull' => false]);
        $table->addColumn('biggest_risks', 'string', ['length' => 1024, 'notnull' => false]);
        $table->addColumn('regulations_standards', 'string', ['length' => 1024, 'notnull' => false]);
        $table->addColumn('unique_proposition', 'string', ['length' => 1024, 'notnull' => false]);
        $table->addColumn('additional_info', 'string', ['length' => 1024, 'notnull' => false]);
        
        $table->setPrimaryKey(['company_id']);
        $table->addForeignKeyConstraint('companies', ['company_id'], ['company_id'], [
            'onUpdate' => 'CASCADE',
            'onDelete' => 'CASCADE',
        ]);
    }

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function down(Schema $schema): void
    {
        $schema->dropTable('companies_details');
    }

}
