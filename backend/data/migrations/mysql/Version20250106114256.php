<?php

declare(strict_types=1);

namespace Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250106114256 extends AbstractMigration {

    /**
     *
     * @param Schema $schema
     * @return void
     */
    public function up(Schema $schema): void
    {
        $table = $schema->createTable('checklists_points');

        $table->addColumn('checklist_point_id', 'bigint', [
            'notnull' => true,
            'autoincrement' => true,
            'unsigned' => true,
        ]);
        $table->setPrimaryKey(['checklist_point_id']);

        $table->addColumn('checklist_id', 'bigint', [
            'notnull' => true,
            'unsigned' => true,
        ]);
        $table->addColumn('title', 'string', [
            'notnull' => true,
            'length' => 1024,
        ]);
        $table->addColumn('description', 'string', [
            'notnull' => true,
            'length' => 1024,
        ]);
        $table->addColumn('expected_actions', 'string', [
            'notnull' => true,
            'length' => 1024,
        ]);
        $table->addColumn('good_performance_description', 'string', [
            'notnull' => true,
            'length' => 1024,
        ]);
        $table->addColumn('good_performance_example', 'string', [
            'notnull' => true,
            'length' => 1024,
        ]);
        $table->addColumn('bad_performance_description', 'string', [
            'notnull' => true,
            'length' => 1024,
        ]);
        $table->addColumn('bad_performance_example', 'string', [
            'notnull' => true,
            'length' => 1024,
        ]);
        $table->addColumn('order', 'integer', [
            'notnull' => true,
            'unsigned' => true,
        ]);
        $table->addColumn('updated_at', 'datetime', [
            'default' => 'CURRENT_TIMESTAMP',
        ]);

        $table->addUniqueIndex([
            'checklist_id',
            'title',
        ]);

        $table->addForeignKeyConstraint('checklists', ['checklist_id'], ['checklist_id'], [
            'onUpdate' => 'CASCADE',
            'onDelete' => 'CASCADE',
        ]);

        $table->addOption('charset', 'utf8mb4');
        $table->addOption('collation', 'utf8mb4_unicode_ci');
        $table->addOption('engine', 'InnoDB');
    }

    /**
     *
     * @param Schema $schema
     * @return void
     */
    public function down(Schema $schema): void
    {
        $schema->dropTable('checklists_points');
    }
}
