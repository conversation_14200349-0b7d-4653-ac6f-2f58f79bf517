<?php

declare(strict_types=1);

namespace Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20221104152647 extends AbstractMigration {

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function up(Schema $schema): void
    {
        $this->connection->executeQuery('
            ALTER TABLE
                permissions
            ADD COLUMN
                system_name VARCHAR(255) NOT NULL
            AFTER
                permission_name;
        ');

    }

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function postUp(Schema $schema): void {
        $systemNames = [
                ['name' => 'company', 'id' => 1],
                ['name' => 'dashboard', 'id' => 2],
                ['name' => 'call_statistics', 'id' => 3],
                ['name' => 'severe_violations', 'id' => 4],
                ['name' => 'clients', 'id' => 5],
                ['name' => 'manager_statistics', 'id' => 6],
                ['name' => 'users', 'id' => 7],
                ['name' => 'iconery', 'id' => 8],
                ['name' => 'payments', 'id' => 9],
                ['name' => 'profile', 'id' => 10],
                ['name' => 'company_users', 'id' => 11],
                ['name' => 'teams', 'id' => 12],
                ['name' => 'calls_overview', 'id' => 13],
                ['name' => 'flags', 'id' => 14],
                ['name' => 'agents', 'id' => 15],
                ['name' => 'clients_dashboard', 'id' => 16],
                ['name' => 'group_name', 'id' => 17],
                ['name' => 'flag_statistics', 'id' => 18],
                ['name' => 'time_on_phone', 'id' => 19],
                ['name' => 'roles', 'id' => 20],
                ['name' => 'manual_calls_upload', 'id' => 21],
            ];
            foreach ($systemNames as $systemName) {
                $this->connection->executeQuery(
                    "UPDATE permissions SET system_name = '".$systemName['name']."' WHERE permission_id = {$systemName['id']}"
                );

            }
    }

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function down(Schema $schema): void
    {
        $table = $schema->getTable('permissions');
        $table->dropColumn('system_name');
    }

}
