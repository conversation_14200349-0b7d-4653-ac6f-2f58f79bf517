<?php

declare(strict_types=1);

namespace Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230712172410 extends AbstractMigration {

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function up(Schema $schema): void
    {
        $table = $schema->getTable('permissions');
        $table->addColumn('is_read_permission_only', 'boolean', [
            'notnull' => true,
            'unsigned' => true,
            'default' => 0,
        ]);
        $table->addColumn('is_write_permission_only', 'boolean', [
            'notnull' => true,
            'unsigned' => true,
            'default' => 0,
        ]);
    }

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function down(Schema $schema): void
    {
        $table = $schema->getTable('permissions');
        $table->dropColumn('is_read_permission_only');
        $table->dropColumn('is_write_permission_only');
    }

}
