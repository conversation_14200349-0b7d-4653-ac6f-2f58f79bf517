<?php

declare(strict_types=1);

namespace Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230406091451 extends AbstractMigration {

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function up(Schema $schema): void
    {
        $this->connection->executeQuery('
            TRUNCATE TABLE roles_displayed_columns;
        ');
        $this->connection->executeQuery('
            ALTER TABLE
                roles_displayed_columns
            ADD COLUMN
                report VARCHAR(255) NOT NULL
            AFTER
                role_id;
        ');
    }

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function down(Schema $schema): void
    {
        $table = $schema->getTable('roles_displayed_columns');
        $table->dropColumn('report');
    }

}
