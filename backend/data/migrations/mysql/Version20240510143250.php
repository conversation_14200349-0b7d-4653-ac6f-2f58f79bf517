<?php

declare(strict_types=1);

namespace Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240510143250 extends AbstractMigration {

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function up(Schema $schema): void
    {
        $table = $schema->createTable('algo_events_hints');
        $table->addColumn('algo_event', 'string', [
            'notnull' => true,
            'length' => 255,
        ]);

        $table->addColumn('algo_api_id', 'integer', [
            'notnull' => true,
            'unsigned' => true,
        ]);
        
        $table->addColumn('text', 'string', [
            'notnull' => true,
            'length' => 255,
        ]);
        
        $table->addForeignKeyConstraint('algo_apis', ['algo_api_id'], ['algo_api_id'], [
            'onUpdate' => 'CASCADE',
            'onDelete' => 'CASCADE',
        ]);

        $table->setPrimaryKey([
            'algo_api_id',
            'algo_event',
        ]);
    }

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function down(Schema $schema): void
    {
        $schema->dropTable('algo_events_hints');
    }

}
