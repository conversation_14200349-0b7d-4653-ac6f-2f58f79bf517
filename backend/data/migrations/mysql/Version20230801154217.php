<?php

declare(strict_types=1);

namespace Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230801154217 extends AbstractMigration {

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function up(Schema $schema): void
    {
        $result = $this->connection->executeQuery('
            SELECT
                company_id
            FROM 
                companies
        ');
        $insertRecords = [];
        foreach ($result->fetchFirstColumn() as $companyId) {
            $insertRecords[] = '(4, ' . $companyId . ', "Admin")';
        }
        $sql = '
            INSERT INTO
                roles
                (
                    role_type,
                    company_id,
                    role_name
                )
            VALUES
        ' . implode(',', $insertRecords);
        if (count($insertRecords)) {
            $this->connection->executeQuery($sql);
        }
    }

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function down(Schema $schema): void
    {
        $this->connection->executeQuery('
            DELETE
            FROM
                roles
            WHERE
                role_type = 4
        ');
    }

}
