<?php

namespace Clickhouse\Migrations;

class Version20241227115752 extends \STClickhouse\Entity\Migration\BaseMigration
{
    /**
     * 
     * @return void
     */
    public function up(): void
    {
        $this->getClient()->write('
            CREATE TABLE calls_checklists_points (
                call_id String,
                call_duration UInt32,
                call_time DateTime,
                call_type String,
                company_id UInt32,
                agent_id UInt128,
                checklist_point_id UInt64,
                is_passed Bool,
                explanation String DEFAULT \'\',          
                created_at DateTime DEFAULT now()
            )
            ENGINE = MergeTree()
            PARTITION BY toYYYYMM(created_at)
            ORDER BY (
                call_id,
                company_id
            )
        ');
    }

    /**
     * 
     * @return void
     */
    public function down(): void
    {
        $this->getClient()->write('
            DROP TABLE
                calls_checklists_points
        ');
    }
}
