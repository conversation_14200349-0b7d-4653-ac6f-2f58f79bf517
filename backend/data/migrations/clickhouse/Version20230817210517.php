<?php

namespace Clickhouse\Migrations;

class Version20230817210517 extends \STClickhouse\Entity\Migration\BaseMigration
{
    /**
     * 
     * @return void
     */
    public function up(): void
    {
        $this->getClient()->write('
            CREATE TABLE emotions_api_log (
                company_id UInt32,
                call_id String,
                paragraph_number UInt16,
                event String,
                value Float32,
                created DateTime DEFAULT now()
            )
            ENGINE = Log
        ');
    }

    /**
     * 
     * @return void
     */
    public function down(): void
    {
        $this->getClient()->write('
            DROP TABLE emotions_api_log
        ');
    }
}
