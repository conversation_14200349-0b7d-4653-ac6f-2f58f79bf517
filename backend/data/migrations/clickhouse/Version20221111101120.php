<?php

namespace Clickhouse\Migrations;

class Version20221111101120 extends \STClickhouse\Entity\Migration\BaseMigration
{
    /**
     * 
     * @return void
     */
    public function up(): void
    {
        $this->getClient()->write('
            ALTER TABLE calls RENAME COLUMN is_delete TO is_deleted
        ');
    }

    /**
     * 
     * @return void
     */
    public function down(): void
    {
        $this->getClient()->write('
            ALTER TABLE calls RENAME COLUMN is_deleted TO is_delete
        ');
    }
}
