<?php

namespace Clickhouse\Migrations;

class Version20220930101151 extends \STClickhouse\Entity\Migration\BaseMigration
{
    /**
     * 
     * @return void
     */
    public function up(): void
    {
        $this->getClient()->write('
            CREATE TABLE calls (
                company_id UInt32,
                call_id String,
                call_time DateTime DEFAULT now(),
                call_status String,
                call_duration UInt32,
                original_file_name String,
                calling_user_id UInt128,
                client_id String,
                client_name String,
                client_status Nullable(String) DEFAULT NULL,
                client_value Nullable(Float32) DEFAULT NULL,
                client_source Nullable(String) DEFAULT NULL,
                file_path Nullable(String) DEFAULT NULL,
                s3_file_path Nullable(String) DEFAULT NULL,
                is_transcribed Bool DEFAULT 0,
                created DateTime DEFAULT now()
            )
            ENGINE = ReplacingMergeTree(created)
            PARTITION BY toYYYYMM(call_time)
            ORDER BY (
                company_id,
                call_id,
                calling_user_id,
                client_id
            )
            SETTINGS index_granularity = 8192
        ');
    }

    /**
     * 
     * @return void
     */
    public function down(): void
    {
        $this->getClient()->write('
            DROP TABLE calls
        ');
    }
}
