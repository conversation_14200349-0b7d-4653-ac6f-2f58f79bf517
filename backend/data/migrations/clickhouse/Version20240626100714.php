<?php

namespace Clickhouse\Migrations;

class Version20240626100714 extends \STClickhouse\Entity\Migration\BaseMigration
{
    /**
     * 
     * @return void
     */
    public function up(): void
    {
        $this->getClient()->write('
            ALTER TABLE
                calls
            ADD COLUMN
                transcribed_at Nullable(Datetime) DEFAULT NULL
            AFTER
                created
        ');
    }

    /**
     * 
     * @return void
     */
    public function down(): void
    {
        $this->getClient()->write('
            ALTER TABLE
                calls
            DROP COLUMN
                transcribed_at
        ');
    }
}
