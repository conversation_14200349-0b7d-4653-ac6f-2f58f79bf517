<?php

namespace Clickhouse\Migrations;

class Version20240430114628 extends \STClickhouse\Entity\Migration\BaseMigration
{
    /**
     * 
     * @return void
     */
    public function up(): void
    {
        $this->getClient()->write('
            ALTER TABLE
                dashboard_statistics_events_1
            DROP COLUMN
                calls_ids
        ');
        $this->getClient()->write('
            ALTER TABLE
                dashboard_statistics_events_2
            DROP COLUMN
                calls_ids
        ');

        $this->getClient()->write('
            ALTER TABLE
                dashboard_statistics_events_categories_1
            ADD COLUMN
                calls_ids Array(String)
            AFTER
                category_id
        ');
        $this->getClient()->write('
            ALTER TABLE
                dashboard_statistics_events_categories_2
            ADD COLUMN
                calls_ids Array(String)
            AFTER
                category_id
        ');
        $this->getClient()->write('
            ALTER TABLE
                dashboard_statistics_events_categories_1
            ADD COLUMN
                events_count UInt64
            AFTER
                category_id
        ');
        $this->getClient()->write('
            ALTER TABLE
                dashboard_statistics_events_categories_2
            ADD COLUMN
                events_count UInt64
            AFTER
                category_id
        ');
        $this->getClient()->write('
            ALTER TABLE
                dashboard_statistics_events_categories_2
            ADD COLUMN
                event_id UInt64
            AFTER
                category_id
        ');
        $this->getClient()->write('
            ALTER TABLE
                dashboard_statistics_events_categories_1
            ADD COLUMN
                event_id UInt64
            AFTER
                category_id
        ');
        $this->getClient()->write('
            ALTER TABLE
                dashboard_statistics_events_categories_1
            DROP COLUMN
                calls_count
        ');
        $this->getClient()->write('
            ALTER TABLE
                dashboard_statistics_events_categories_2
            DROP COLUMN
                calls_count
        ');
    }

    /**
     * 
     * @return void
     */
    public function down(): void
    {
        $this->getClient()->write('
            ALTER TABLE
                dashboard_statistics_events_categories_2
            DROP COLUMN
                calls_ids
        ');
        $this->getClient()->write('
              ALTER TABLE
                dashboard_statistics_events_categories_1
            DROP COLUMN
                calls_ids
        ');
        $this->getClient()->write('
            ALTER TABLE
                dashboard_statistics_events_categories_2
            DROP COLUMN
                event_id
        ');
        $this->getClient()->write('
              ALTER TABLE
                dashboard_statistics_events_categories_1
            DROP COLUMN
                event_id
        ');
        $this->getClient()->write('
            ALTER TABLE
                dashboard_statistics_events_categories_2
            DROP COLUMN
                events_count
        ');
        $this->getClient()->write('
              ALTER TABLE
                dashboard_statistics_events_categories_1
            DROP COLUMN
                events_count
        ');
        $this->getClient()->write('
            ALTER TABLE
                dashboard_statistics_events_1
            ADD COLUMN
                calls_ids Array(String)
            AFTER
                event_id
        ');
        $this->getClient()->write('
            ALTER TABLE
                dashboard_statistics_events_2
            ADD COLUMN
                calls_ids Array(String)
            AFTER
                event_id
        ');
        $this->getClient()->write('
            ALTER TABLE
                dashboard_statistics_events_categories_1
            ADD COLUMN
                calls_count UInt64
            AFTER
                category_id
        ');
        $this->getClient()->write('
            ALTER TABLE
                dashboard_statistics_events_categories_2
            ADD COLUMN
                calls_count UInt64
            AFTER
                category_id
        ');
    }
}
