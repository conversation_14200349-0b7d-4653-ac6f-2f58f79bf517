<?php

namespace Clickhouse\Migrations;

class Version20240930124259 extends \STClickhouse\Entity\Migration\BaseMigration
{
    /**
     * 
     * @return void
     */
    public function up(): void
    {
        $this->getClient()->write('
            CREATE TABLE precalculated_agents
            (
                date Date,
                company_id UInt64,
                role_id UInt64,
                agent_id UInt64,
                event_ids Array(UInt64),
                event_category_ids Array(UInt64),
                total_calls_duration UInt32 DEFAULT 0,
                total_calls_count UInt32 DEFAULT 0,
                effective_calls_duration UInt32 DEFAULT 0,
                not_effective_calls_duration UInt32 DEFAULT 0,
                effective_calls_count UInt32 DEFAULT 0,
                not_effective_calls_count UInt32 DEFAULT 0,
                reviewed_calls_count UInt32 DEFAULT 0,
                total_chats_count UInt32 DEFAULT 0,
                reviewed_chats_count UInt32 DEFAULT 0,
                total_forms_count UInt32 DEFAULT 0,
                reviewed_forms_count UInt32 DEFAULT 0,
                approved_forms_count UInt32 DEFAULT 0,
                refused_forms_count UInt32 DEFAULT 0,
                score Int32 DEFAULT 0,
                created DateTime64 DEFAULT now64()
            )
            ENGINE = ReplacingMergeTree(created)
            ORDER BY (
                date,
                company_id,
                role_id,
                agent_id
            )
            SETTINGS index_granularity = 8192;
        ');
    }

    /**
     * 
     * @return void
     */
    public function down(): void
    {
        $this->getClient()->write('
            DROP TABLE precalculated_agents
        ');
    }
}
