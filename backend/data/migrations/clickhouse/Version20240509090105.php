<?php

namespace Clickhouse\Migrations;

class Version20240509090105 extends \STClickhouse\Entity\Migration\BaseMigration
{
    /**
     * 
     * @return void
     */
    public function up(): void
    {
        $this->getClient()->write('
            RENAME TABLE dashboard_statistics_events_1 TO dashboard_statistics_reviewed_events_1
        ');
        $this->getClient()->write('
            RENAME TABLE dashboard_statistics_events_2 TO dashboard_statistics_reviewed_events_2
        ');
    }

    /**
     * 
     * @return void
     */
    public function down(): void
    {
        $this->getClient()->write('
            RENAME TABLE dashboard_statistics_reviewed_events_1 TO dashboard_statistics_events_1
        ');
        $this->getClient()->write('
            RENAME TABLE dashboard_statistics_reviewed_events_2 TO dashboard_statistics_events_2
        ');
    }
}
