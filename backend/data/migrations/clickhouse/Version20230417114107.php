<?php

namespace Clickhouse\Migrations;

class Version20230417114107 extends \STClickhouse\Entity\Migration\BaseMigration
{
    /**
     * 
     * @return void
     */
    public function up(): void
    {
        $this->getClient()->write('
            ALTER TABLE
                clients
            ADD COLUMN
                last_transaction_date Nullable(DateTime) DEFAULT NULL
            AFTER converted_date
        ');
    }

    /**
     * 
     * @return void
     */
    public function down(): void
    {
        $this->getClient()->write('
            ALTER TABLE
                clients
            DROP COLUMN
                last_transaction_date
        ');
    }
}
