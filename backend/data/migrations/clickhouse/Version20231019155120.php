<?php

namespace Clickhouse\Migrations;

class Version20231019155120 extends \STClickhouse\Entity\Migration\BaseMigration
{
    /**
     * 
     * @return void
     */
    public function up(): void
    {
        $this->getClient()->write('
            DROP TABLE IF EXISTS api_calls_logs_deprecated
        ');
    }

    /**
     * 
     * @return void
     */
    public function down(): void
    {
        $this->getClient()->write('
            CREATE TABLE api_calls_logs_deprecated
            (
                `id` String,
                `company_id` UInt32,
                `call_id` String,
                `message` Nullable(String),
                `created` DateTime DEFAULT now()
            )
            ENGINE = Log;
        ');
    }
}
