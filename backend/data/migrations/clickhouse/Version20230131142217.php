<?php

namespace Clickhouse\Migrations;

class Version20230131142217 extends \STClickhouse\Entity\Migration\BaseMigration
{
    /**
     * 
     * @return void
     */
    public function up(): void
    {
        $this->getClient()->write('
            ALTER TABLE
                calls_precalculated_values
            ADD COLUMN
                risk_rank Float32 DEFAULT 0
            AFTER
                score
        ');
    }

    /**
     * 
     * @return void
     */
    public function down(): void
    {
        $this->getClient()->write('
            ALTER TABLE
                calls_precalculated_values
            DROP COLUMN
                risk_rank
        ');
    }
}
