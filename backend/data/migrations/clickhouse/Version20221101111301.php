<?php

namespace Clickhouse\Migrations;

class Version20221101111301 extends \STClickhouse\Entity\Migration\BaseMigration
{
    /**
     * 
     * @return void
     */
    public function up(): void
    {
        $this->getClient()->write('
            CREATE TABLE algo_events (
                company_id UInt32,
                call_id String,
                paragraph_number UInt16,
                algo_api_id UInt16,
                event String,
                call_time DateTime DEFAULT now(),
                score Float32,
                main_point_location Array(UInt16),
                main_point_phrase String,
                created DateTime DEFAULT now()
            )
            ENGINE = ReplacingMergeTree(created)
            PARTITION BY toYYYYMM(call_time)
            ORDER BY (
                company_id,
                call_id,
                paragraph_number,
                algo_api_id,
                event
            )
            SETTINGS index_granularity = 8192
        ');
    }

    /**
     * 
     * @return void
     */
    public function down(): void
    {
        $this->getClient()->write('
            DROP TABLE algo_events
        ');
    }
}
