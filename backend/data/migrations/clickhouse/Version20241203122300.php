<?php

namespace Clickhouse\Migrations;

use STClickhouse\Entity\Migration\BaseMigration;

class Version20241203122300 extends BaseMigration
{
    /**
     * 
     * @return void
     */
    public function up(): void
    {
        $this->getClient()->write('
            ALTER TABLE calls_algo_events
                RENAME COLUMN algo_api_category_id TO industry_id
        ');
    }

    /**
     * 
     * @return void
     */
    public function down(): void
    {
        $this->getClient()->write('
            ALTER TABLE calls_algo_events
                RENAME COLUMN industry_id TO algo_api_category_id
        ');
    }
}
