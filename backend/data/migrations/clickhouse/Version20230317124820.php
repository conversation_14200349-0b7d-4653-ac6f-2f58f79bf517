<?php

namespace Clickhouse\Migrations;

class Version20230317124820 extends \STClickhouse\Entity\Migration\BaseMigration
{
    /**
     * 
     * @return void
     */
    public function up(): void
    {
        $this->getClient()->write('
            CREATE TABLE calls_comments_notifications (
                company_id UInt32,
                user_id UInt32,
                call_id String,
                comment_id String,
                is_unread Boolean DEFAULT 1,
                created DateTime DEFAULT now()
            )
            ENGINE = ReplacingMergeTree(created)
            PARTITION BY toYYYYMM(created)
            ORDER BY (
                comment_id,
                user_id
            )
            SETTINGS index_granularity = 8192
        ');
    }

    /**
     * 
     * @return void
     */
    public function down(): void
    {
        $this->getClient()->write('
            DROP TABLE IF EXISTS
                calls_comments_notifications
        ');
    }
}
