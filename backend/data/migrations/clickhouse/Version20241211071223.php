<?php

namespace Clickhouse\Migrations;

class Version20241211071223 extends \STClickhouse\Entity\Migration\BaseMigration
{
    /**
     * 
     * @return void
     */
    public function up(): void
    {
        $this->getClient()->write('
            ALTER TABLE
                clients
            DROP COLUMN
                email
        ');
        $this->getClient()->write('
            ALTER TABLE
                precalculated_clients
            DROP COLUMN
                email
        ');
    }

    /**
     * 
     * @return void
     */
    public function down(): void
    {
        $this->getClient()->write('
            ALTER TABLE
                clients
            ADD COLUMN
                email Nullable(String) DEFAULT NULL
            AFTER client_name
        ');
        $this->getClient()->write('
            ALTER TABLE
                precalculated_clients
            ADD COLUMN
                email Nullable(String) DEFAULT NULL
            AFTER client_name
        ');
    }
}
