<?php

namespace Clickhouse\Migrations;

class Version20240315160014 extends \STClickhouse\Entity\Migration\BaseMigration
{
    /**
     * 
     * @return void
     */
    public function up(): void
    {
        $this->getClient()->write('
            ALTER TABLE
                dashboard_statistics_managers_kpi_1
            ADD COLUMN
                reviewer_user_id UInt64
            AFTER
                team_ids
        ');
        $this->getClient()->write('
            ALTER TABLE
                dashboard_statistics_managers_kpi_2
            ADD COLUMN
                reviewer_user_id UInt64
            AFTER
                team_ids
        ');
        $this->getClient()->write('
            ALTER TABLE
                dashboard_statistics_managers_kpi_1
            DROP COLUMN
                manager_name
        ');
        $this->getClient()->write('
            ALTER TABLE
                dashboard_statistics_managers_kpi_2
            DROP COLUMN
                manager_name
        ');
    }

    /**
     * 
     * @return void
     */
    public function down(): void
    {
        $this->getClient()->write('
            ALTER TABLE
                dashboard_statistics_managers_kpi_1
            DROP COLUMN
                reviewer_user_id
        ');
        $this->getClient()->write('
            ALTER TABLE
                dashboard_statistics_managers_kpi_2
            DROP COLUMN
                reviewer_user_id
        ');
        $this->getClient()->write('
            ALTER TABLE
                dashboard_statistics_managers_kpi_1
            ADD COLUMN
                manager_name String
            AFTER
                team_ids
        ');
        $this->getClient()->write('
            ALTER TABLE
                dashboard_statistics_managers_kpi_2
            ADD COLUMN
                manager_name String
            AFTER
                team_ids
        ');
    }
}
