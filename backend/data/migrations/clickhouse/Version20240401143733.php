<?php

namespace Clickhouse\Migrations;

class Version20240401143733 extends \STClickhouse\Entity\Migration\BaseMigration
{
    /**
     * 
     * @return void
     */
    public function up(): void
    {
        $this->getClient()->write('
            ALTER TABLE
                calls_paragraphs
            ADD COLUMN
                risk_value_sense UInt16 DEFAULT 0
            AFTER
                risk_value
        ');
    }

    /**
     * 
     * @return void
     */
    public function down(): void
    {
        $this->getClient()->write('
            ALTER TABLE
                calls_paragraphs
            DROP COLUMN
                risk_value_sense
        ');
    }
}
