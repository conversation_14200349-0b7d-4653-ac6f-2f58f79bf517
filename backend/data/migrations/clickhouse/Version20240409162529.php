<?php

namespace Clickhouse\Migrations;

class Version20240409162529 extends \STClickhouse\Entity\Migration\BaseMigration
{
    /**
     * 
     * @return void
     */
    public function up(): void
    {
        $this->getClient()->write('
            ALTER TABLE
                calls
            ADD COLUMN
                translation_driver Nullable(String) DEFAULT NULL
            AFTER
                transcribing_driver
        ');
    }

    /**
     * 
     * @return void
     */
    public function down(): void
    {
        $this->getClient()->write('
            ALTER TABLE 
                calls
            DROP COLUMN
                translation_driver
        ');
    }
}
