<?php

namespace Clickhouse\Migrations;

class Version20230602101933 extends \STClickhouse\Entity\Migration\BaseMigration
{
    /**
     * 
     * @return void
     */
    public function up(): void
    {
        $this->getClient()->write('
            DROP TABLE calls_events_precalculated
        ');
        $this->getClient()->write('
            CREATE TABLE calls_events_precalculated (
                company_id UInt32,
                role_id UInt32,
                call_id String,
                call_is_reviewed Bool,
                call_reviewed_time Nullable(DateTime),
                call_score Int32 DEFAULT 0,
                call_risk_rank Float32 DEFAULT 0,
                call_comments Array(String),
                call_reviewers_names Array(String),
                call_reviewers_ids Array(UInt64),
                paragraph UInt32,
                event_id UInt64,
                event_name String,
                event_highlight Nullable(String),
                event_en_highlight Nullable(String),
                event_text Nullable(String),
                event_en_text Nullable(String),
                event_icon String,
                event_is_pinned Bool,
                event_is_confirmed Bool,
                event_category_id UInt64,
                event_category_name String,
                event_color_id UInt64,
                event_fill_color_hex String,
                event_outline_color_hex String,
                event_color_priority UInt16,
                created DateTime64 DEFAULT now64()
            )
            ENGINE = ReplacingMergeTree(created)
            PARTITION BY company_id
            ORDER BY (
                company_id,
                role_id,
                call_id,
                paragraph,
                event_id
            )
            SETTINGS index_granularity = 8192
        ');
    }

    /**
     * 
     * @return void
     */
    public function down(): void
    {
        $this->getClient()->write('
            DROP TABLE calls_events_precalculated
        ');
        $this->getClient()->write('
            CREATE TABLE calls_events_precalculated (
                company_id UInt32,
                role_id UInt32,
                call_id String,
                call_is_reviewed Bool,
                call_reviewed_time Nullable(DateTime),
                call_score Int32 DEFAULT 0,
                call_risk_rank Float32 DEFAULT 0,
                paragraph UInt32,
                event_id UInt64,
                event_name String,
                event_highlight Nullable(String),
                event_en_highlight Nullable(String),
                event_text Nullable(String),
                event_en_text Nullable(String),
                event_icon String,
                event_is_pinned Bool,
                event_is_confirmed Bool,
                event_category_id UInt64,
                event_category_name String,
                event_color_id UInt64,
                event_fill_color_hex String,
                event_outline_color_hex String,
                event_color_priority UInt16,
                event_reviewers_json String,
                created DateTime64 DEFAULT now64()
            )
            ENGINE = ReplacingMergeTree(created)
            PARTITION BY company_id
            ORDER BY (
                company_id,
                role_id,
                call_id,
                paragraph,
                event_id
            )
            SETTINGS index_granularity = 8192
        ');
    }
}
