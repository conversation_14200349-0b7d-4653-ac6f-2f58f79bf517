<?php

namespace Clickhouse\Migrations;

class Version20250321064315 extends \STClickhouse\Entity\Migration\BaseMigration
{
    /**
     * 
     * @return void
     */
    public function up(): void
    {
        $this->getClient()->write(<<<SQL
            INSERT INTO
                calls_checklists_points
            (
                call_id,
                call_duration,
                call_time,
                call_type,
                client_id,
                company_id,
                agent_id,
                checklist_point_id,
                is_passed,
                explanation,
                created_at
            )
            SELECT
                call_id,
                call_duration,
                call_time,
                call_type,
                c.client_id client_id,
                company_id,
                agent_id,
                checklist_point_id,
                is_passed,
                explanation,
                created_at
            FROM calls_checklists_points
            INNER JOIN (
                SELECT
                    client_id,
                    call_id
                FROM calls
                WHERE call_time > '2025-02-01 00:00:00'
            ) c ON c.call_id = calls_checklists_points.call_id;
        SQL);

        $this->getClient()->write(<<<SQL
            DELETE 
            FROM calls_checklists_points 
            WHERE client_id IS NULL OR client_id = ''
        SQL);
    }

    /**
     * 
     * @return void
     */
    public function down(): void
    {
        $this->getClient()->write('
            
        ');
    }
}
