<?php

namespace Clickhouse\Migrations;

class Version20240521145506 extends \STClickhouse\Entity\Migration\BaseMigration
{
    /**
     * 
     * @return void
     */
    public function up(): void
    {
        $this->getClient()->write('
            CREATE TABLE calls_reviews
            (
                company_id UInt32,
                call_id String,
                role_id UInt32,
                call_time DateTime DEFAULT now(),
                agent_id UInt128,
                client_id String,
                call_origin String DEFAULT \'api\',
                is_reviewed Bool DEFAULT 0,
                is_approved Nullable(Bool) DEFAULT NULL,
                reviewed_time DateTime,
                reviewer_user_id UInt64
            )
            ENGINE = ReplacingMergeTree(reviewed_time)
            ORDER BY
            (
                company_id,
                call_id,
                role_id
            )
            SETTINGS index_granularity = 8192;
        ');
    }

    /**
     * 
     * @return void
     */
    public function down(): void
    {
        $this->getClient()->write('
            DROP TABLE calls_reviews
        ');
    }
}
