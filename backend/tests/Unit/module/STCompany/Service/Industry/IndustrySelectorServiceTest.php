<?php

declare(strict_types=1);

namespace tests\Unit\module\STCompany\Service\Industry;

use PHPUnit\Framework\MockObject\Exception;
use STCompany\Data\CompanyIndustriesTable;
use STCompany\Entity\Industry\IndustryCollection;
use STCompany\Service\Industry\IndustrySelectorService;
use tests\TestCase;

final class IndustrySelectorServiceTest extends TestCase
{
    /**
     * @throws Exception
     */
    public function testGetIndustries(): void
    {
        $companyId = $this->faker->numberBetween(1, 100);

        $industriesCollection = $this->createMock(IndustryCollection::class);

        $industriesTable = $this->createMock(CompanyIndustriesTable::class);
        $industriesTable
            ->method('getIndustries')
            ->with($companyId)
            ->willReturn($industriesCollection);

        $selector = new IndustrySelectorService($industriesTable);

        $this->assertSame($industriesCollection, $selector->getIndustries($companyId));
    }
}
