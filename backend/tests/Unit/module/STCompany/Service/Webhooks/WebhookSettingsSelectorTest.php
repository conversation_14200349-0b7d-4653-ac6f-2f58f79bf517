<?php

declare(strict_types=1);

namespace tests\Unit\module\STCompany\Service\Webhooks;

use PHPUnit\Framework\MockObject\Exception;
use RuntimeException;
use STCompany\Data\CompaniesWebhooksSettingsTable;
use STCompany\Service\Webhooks\WebhookSettingsSelector;
use tests\TestCase;

final class WebhookSettingsSelectorTest extends TestCase
{
    /**
     * @throws Exception
     */
    public function testGetCompaniesWebhooksSettingsDataByType(): void
    {
        $companyId = $this->faker->numberBetween(1, 100);
        $webhookType = $this->faker->word();
        $url = $this->faker->url();
        $headers = [$this->faker->word() => $this->faker->word()];

        $webhooksData = [
            'type' => $webhookType,
            'url' => $url,
            'is_enabled' => $this->faker->numberBetween(0, 1),
            'headers' => json_encode($headers),
        ];

        $companiesWebhooksSettingsTable = $this->createMock(CompaniesWebhooksSettingsTable::class);
        $companiesWebhooksSettingsTable
            ->method('getWebhookSettingsDataByType')
            ->with($webhookType, $companyId)
            ->willReturn($webhooksData);

        $selector = new WebhookSettingsSelector($companiesWebhooksSettingsTable);

        $expectedData = $webhooksData;
        $expectedData['headers'] = $headers;

        $this->assertSame($expectedData, $selector->getCompaniesWebhooksSettingsDataByType($webhookType, $companyId));
    }

    /**
     * @throws Exception
     */
    public function testGetCompaniesWebhooksSettingsDataByTypeWhenNoSettings(): void
    {
        $companyId = $this->faker->numberBetween(1, 100);
        $webhookType = $this->faker->word();

        $companiesWebhooksSettingsTable = $this->createMock(CompaniesWebhooksSettingsTable::class);
        $companiesWebhooksSettingsTable
            ->method('getWebhookSettingsDataByType')
            ->with($webhookType, $companyId)
            ->willReturn(null);

        $selector = new WebhookSettingsSelector($companiesWebhooksSettingsTable);

        $this->assertNull($selector->getCompaniesWebhooksSettingsDataByType($webhookType, $companyId));
    }

    /**
     * @throws Exception
     */
    public function testIsWebhooksEnabled(): void
    {
        $companyId = $this->faker->numberBetween(1, 100);
        $webhookType = $this->faker->word();
        $isEnabled = $this->faker->numberBetween(0, 1);

        $webhooksData = [
            'type' => $webhookType,
            'url' => $this->faker->url(),
            'is_enabled' => $isEnabled,
        ];

        $companiesWebhooksSettingsTable = $this->createMock(CompaniesWebhooksSettingsTable::class);
        $companiesWebhooksSettingsTable
            ->method('getWebhookSettingsDataByType')
            ->with($webhookType, $companyId)
            ->willReturn($webhooksData);

        $selector = new WebhookSettingsSelector($companiesWebhooksSettingsTable);

        $this->assertSame((bool) $isEnabled, $selector->isWebhooksEnabled($webhookType, $companyId));
    }

    /**
     * @throws Exception
     */
    public function testIsWebhooksEnabledWhenNoSettings(): void
    {
        $companyId = $this->faker->numberBetween(1, 100);
        $webhookType = $this->faker->word();

        $companiesWebhooksSettingsTable = $this->createMock(CompaniesWebhooksSettingsTable::class);
        $companiesWebhooksSettingsTable
            ->method('getWebhookSettingsDataByType')
            ->with($webhookType, $companyId)
            ->willReturn(null);

        $selector = new WebhookSettingsSelector($companiesWebhooksSettingsTable);

        $this->assertFalse($selector->isWebhooksEnabled($webhookType, $companyId));
    }
}
