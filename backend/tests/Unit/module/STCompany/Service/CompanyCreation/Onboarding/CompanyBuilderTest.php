<?php

declare(strict_types=1);

namespace tests\Unit\module\STCompany\Service\CompanyCreation\Onboarding;

use PHPUnit\Framework\MockObject\Exception;
use STCompany\Entity\Company;
use STCompany\Service\CompanyCreation\Onboarding\CompanyBuilder;
use STCompany\Service\CompanyService;
use STOnboarding\Entity\OnboardingForm;
use tests\TestCase;

final class CompanyBuilderTest extends TestCase
{
    /**
     * @throws Exception
     */
    public function testCreateFromOnboardingForm(): void
    {
        $companyName = $this->faker->word();
        $companyLogo = $this->faker->text();

        $callsSettings = [
            'languages' => [
                $this->faker->languageCode(),
                $this->faker->languageCode(),
            ],
            'min_call_duration_for_auto_analyze' => $this->faker->numberBetween(1, 300),
        ];

        $onboardingForm = $this->createMock(OnboardingForm::class);
        $onboardingForm->method('getCompanyName')->willReturn($companyName);
        $onboardingForm->method('getCompanyLogo')->willReturn($companyLogo);
        $onboardingForm->method('getCallsSettings')->willReturn($callsSettings);

        $companyService = $this->createMock(CompanyService::class);

        $builder = new CompanyBuilder($companyService);
        $company = $builder->createFromOnboardingForm($onboardingForm);

        $this->assertSame($companyName, $company->getName());
        $this->assertSame($companyLogo, $company->getAvatar());
        $this->assertSame($callsSettings['languages'], $company->getLanguages());
        $this->assertSame($callsSettings['min_call_duration_for_auto_analyze'], $company->getMinCallDurationForAutoAnalyze());
    }

    /**
     * @throws Exception
     */
    public function testCreateFromOnboardingFormWhenOnlyRequiredFields(): void
    {
        $companyName = $this->faker->word();

        $onboardingForm = $this->createMock(OnboardingForm::class);
        $onboardingForm->method('getCompanyName')->willReturn($companyName);

        $companyService = $this->createMock(CompanyService::class);

        $builder = new CompanyBuilder($companyService);
        $company = $builder->createFromOnboardingForm($onboardingForm);

        $this->assertSame($companyName, $company->getName());
        $this->assertNull($company->getAvatar());
        $this->assertSame([], $company->getLanguages());
        $this->assertSame(0, $company->getMinCallDurationForAutoAnalyze());
    }

    /**
     * @throws Exception
     */
    public function testSetAwsData(): void
    {
        $awsS3BucketName = $this->faker->word();
        $awsS3BucketDir = $this->faker->text(10);

        $company = $this->createMock(Company::class);
        $company
            ->expects($this->once())
            ->method('setAwsS3BucketRegion')
            ->with(CompanyService::DEFAULT_AWS_REGION);
        $company
            ->expects($this->once())
            ->method('setAwsS3BucketName')
            ->with($awsS3BucketName);
        $company
            ->expects($this->once())
            ->method('setAwsS3BucketDir')
            ->with($awsS3BucketDir);

        $companyService = $this->createMock(CompanyService::class);
        $companyService
            ->method('getCommonAwsBucket')
            ->with($company)
            ->willReturn($awsS3BucketName);
        $companyService
            ->method('createCompanyAwsBucketDir')
            ->with($company)
            ->willReturn($awsS3BucketDir);

        $builder = new CompanyBuilder($companyService);
        $builder->setAwsData($company);
    }
}
