<?php

declare(strict_types=1);

namespace tests\Unit\module\STCompany\Service\CompanyCreation\Onboarding\UsersCreation;

use PHPUnit\Framework\MockObject\Exception;
use STCompany\Entity\Company;
use STCompany\Entity\User;
use STCompany\Service\CompanyCreation\Onboarding\UsersCreation\UserInviter;
use STCompany\Service\Interfaces\AuthCodesGeneratorInterface;
use STMail\Service\MailService;
use tests\TestCase;

final class UserInviterTest extends TestCase
{
    /**
     * @throws Exception
     */
    public function testInviteNewUser(): void
    {
        $email = $this->faker->email();
        $code = $this->faker->text(15);
        $companyId = $this->faker->numberBetween(1, 100);
        $companyName = $this->faker->word();

        $senderName = $this->faker->firstName() . ' ' . $this->faker->lastName();

        $user = $this->createMock(User::class);
        $user->method('getEmail')->willReturn($email);

        $authCodesGenerator = $this->createMock(AuthCodesGeneratorInterface::class);
        $authCodesGenerator
            ->method('getChangePasswordCode')
            ->with($user, 31536000)
            ->willReturn($code);

        $company = $this->createMock(Company::class);
        $company->method('getId')->willReturn($companyId);
        $company->method('getName')->willReturn($companyName);
        $company->method('getFrontId')->willReturn(Company::DEFAULT_FRONT_ID);

        $rawInviteLink = 'https://some-domain.com/invite/{code}?companyId={companyId}&companyName={companyName}&email=';
        $inviteLink = sprintf(
            'https://some-domain.com/invite/%s?companyId=%d&companyName=%s&email=%s',
            $code,
            $companyId,
            $companyName,
            $email
        );

        $mailData = [
            'template_id' => 'invite-new-user',
            'substitutions' => [
                'dynamic_html' => [
                    'account_link' => $inviteLink,
                    'invitation_link' => $inviteLink,
                ],
                'inviter' => $senderName,
                'company_name' => $companyName,
                'user_email' => $email,
            ],
        ];

        $mailService = $this->createMock(MailService::class);
        $mailService
            ->expects($this->once())
            ->method('addToQueue')
            ->with(Company::DEFAULT_FRONT_ID, $user, $mailData);

        $inviter = new UserInviter($authCodesGenerator, $mailService);
        $inviter->inviteNewUser($user, $company, $rawInviteLink, $senderName);
    }

    /**
     * @throws Exception
     */
    public function testInviteExistedUser(): void
    {
        $email = $this->faker->email();
        $senderName = $this->faker->firstName() . ' ' . $this->faker->lastName();
        $companyName = $this->faker->word();

        $user = $this->createMock(User::class);
        $user->method('getEmail')->willReturn($email);

        $company = $this->createMock(Company::class);
        $company->method('getName')->willReturn($companyName);
        $company->method('getFrontId')->willReturn(Company::DEFAULT_FRONT_ID);

        $mailData = [
            'template_id' => 'invite-to-company',
            'substitutions' => [
                'inviter' => $senderName,
                'subject' => 'Robonote: invite to ' . $companyName,
                'company_name' => $companyName,
                'user_email' => $user->getEmail(),
            ],
        ];

        $authCodesGenerator = $this->createMock(AuthCodesGeneratorInterface::class);
        $mailService = $this->createMock(MailService::class);
        $mailService
            ->expects($this->once())
            ->method('addToQueue')
            ->with(Company::DEFAULT_FRONT_ID, $user, $mailData);

        $inviter = new UserInviter($authCodesGenerator, $mailService);
        $inviter->inviteExistedUser($user, $company, $senderName);
    }
}
