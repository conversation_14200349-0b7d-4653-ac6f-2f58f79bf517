<?php

declare(strict_types=1);

namespace tests\Unit\module\STCompany\Service\CompanyCreation\Onboarding;

use Exception;
use PHPUnit\Framework\MockObject\Exception as PHPUnitException;
use STCompany\Entity\Company;
use STCompany\Service\CompanyCreation\Onboarding\CompanyBuilder;
use STCompany\Service\CompanyCreation\Onboarding\CompanyCreator;
use STCompany\Service\CompanyCreation\Onboarding\EventsCreator;
use STCompany\Service\CompanyCreation\Onboarding\PermissionsCreator;
use STCompany\Service\CompanyCreation\Onboarding\UsersCreator;
use STCompany\Service\CompanyService;
use STCompany\Service\Interfaces\ApplicationCreatorInterface;
use STOnboarding\Entity\OnboardingForm;
use tests\TestCase;

final class CompanyCreatorTest extends TestCase
{
    /**
     * @throws PHPUnitException
     * @throws Exception
     */
    public function testCreateFromOnboardingForm(): void
    {
        $inviteLink = $this->faker->url();

        $events = ['events data'];
        $onboardingForm = $this->createMock(OnboardingForm::class);
        $onboardingForm->method('getInviteLink')->willReturn($inviteLink);
        $onboardingForm->method('getActiveIndustryEvents')->willReturn($events);

        $company = $this->createMock(Company::class);

        $companyBuilder = $this->createMock(CompanyBuilder::class);
        $companyBuilder
            ->method('createFromOnboardingForm')
            ->with($onboardingForm)
            ->willReturn($company);

        $companyService = $this->createMock(CompanyService::class);
        $companyService
            ->expects($this->exactly(2))
            ->method('saveCompany')
            ->with($this->identicalTo($company));

        $company->expects($this->once())->method('initStartBalance');

        $companyBuilder
            ->expects($this->once())
            ->method('setAwsData')
            ->with($this->identicalTo($company));

        $permissionsCreator = $this->createMock(PermissionsCreator::class);
        $permissionsCreator
            ->expects($this->once())
            ->method('createPermissions')
            ->with($company);

        $users = ['users data'];
        $onboardingForm->method('getUsers')->willReturn($users);

        $usersCreator = $this->createMock(UsersCreator::class);
        $usersCreator
            ->expects($this->once())
            ->method('createUsers')
            ->with($company, $users);

        $eventsCreator = $this->createMock(EventsCreator::class);
        $eventsCreator
            ->expects($this->once())
            ->method('createEvents')
            ->with($company, $events);

        $applicationCreator = $this->createMock(ApplicationCreatorInterface::class);
        $applicationCreator
            ->expects($this->once())
            ->method('generateApplication')
            ->with($company);

        $creator = new CompanyCreator(
            $companyBuilder,
            $companyService,
            $permissionsCreator,
            $usersCreator,
            $eventsCreator,
            $applicationCreator
        );
        $this->assertSame($company, $creator->createFromOnboardingForm($onboardingForm));
    }
}
