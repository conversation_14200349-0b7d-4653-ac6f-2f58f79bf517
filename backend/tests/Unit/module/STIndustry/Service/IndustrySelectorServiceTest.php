<?php

declare(strict_types=1);

namespace tests\Unit\module\STIndustry\Service;

use PHPUnit\Framework\MockObject\Exception;
use STApi\Entity\Exception\NotFoundApiException;
use STIndustry\Data\IndustriesTable;
use STIndustry\Entity\IndustryCollection;
use STIndustry\Entity\Industry;
use STIndustry\Service\IndustrySelectorService;
use tests\TestCase;

final class IndustrySelectorServiceTest extends TestCase
{
    /**
     * @throws Exception
     */
    public function testGetIndustries(): void
    {
        $industriesCollection = $this->createMock(IndustryCollection::class);
        $industriesTable = $this->createMock(IndustriesTable::class);
        $industriesTable
            ->method('getIndustries')
            ->willReturn($industriesCollection);

        $selector = new IndustrySelectorService($industriesTable);

        $this->assertSame($industriesCollection, $selector->getIndustries());
    }

    /**
     * @return void
     * @throws Exception
     * @throws NotFoundApiException
     */
    public function testGetIndustry(): void
    {
        $industryId = $this->faker->numberBetween(1, 100);

        $industry = $this->createMock(Industry::class);

        $industriesTable = $this->createMock(IndustriesTable::class);
        $industriesTable
            ->method('getIndustry')
            ->with($industryId)
            ->willReturn($industry);

        $selector = new IndustrySelectorService($industriesTable);

        $this->assertSame($industry, $selector->getIndustry($industryId));
    }
}
