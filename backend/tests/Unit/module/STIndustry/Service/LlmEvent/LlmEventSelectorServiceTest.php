<?php

declare(strict_types=1);

namespace tests\Unit\module\STIndustry\Service\LlmEvent;

use PHPUnit\Framework\MockObject\Exception;
use STApi\Entity\Exception\NotFoundApiException;
use STIndustry\Data\IndustriesLlmEventsTable;
use STIndustry\Entity\LlmEvent\LlmEvent;
use STIndustry\Entity\LlmEvent\LlmEventCollection;
use STIndustry\Service\LlmEvent\LlmEventSelectorService;
use tests\TestCase;

final class LlmEventSelectorServiceTest extends TestCase
{
    /**
     * @dataProvider industriesIdsDataProvider
     * @param int|array $industryIds
     * @return void
     * @throws Exception
     */
    public function testGetLlmEvents(int|array $industryIds): void
    {
        $llmEventsCollection = $this->createMock(LlmEventCollection::class);

        $llmEventsTable = $this->createMock(IndustriesLlmEventsTable::class);
        $llmEventsTable
            ->method('getLlmEvents')
            ->with($industryIds)
            ->willReturn($llmEventsCollection);

        $selector = new LlmEventSelectorService($llmEventsTable);

        $this->assertSame($llmEventsCollection, $selector->getLlmEvents($industryIds));
    }

    public static function industriesIdsDataProvider(): array
    {
        return [
            'filter by one' => [123],
            'filter by many' => [[123, 321]],
        ];
    }

    /**
     * @throws Exception
     * @throws NotFoundApiException
     */
    public function testGetLlmEvent(): void
    {
        $industryId = $this->faker->numberBetween(1, 100);
        $llmEventId = $this->faker->numberBetween(101, 200);

        $llmEvent = $this->createMock(LlmEvent::class);

        $llmEventTable = $this->createMock(IndustriesLlmEventsTable::class);
        $llmEventTable
            ->method('getLlmEvent')
            ->with($llmEventId)
            ->willReturn($llmEvent);

        $selector = new LlmEventSelectorService($llmEventTable);

        $this->assertSame($llmEvent, $selector->getLlmEvent($llmEventId, $industryId));
    }
}
