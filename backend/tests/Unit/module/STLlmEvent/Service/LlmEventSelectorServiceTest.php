<?php

namespace tests\Unit\module\STLlmEvent\Service;

use PHPUnit\Framework\MockObject\Exception;
use STLib\Mvc\Data\CollectionResultSet;
use STLlmEvent\Data\LlmEventsTable;
use STLlmEvent\Entity\LlmEvent;
use STLlmEvent\Entity\LlmEventCollection;
use STLlmEvent\Service\LlmEventSelectorService;
use tests\TestCase;

class LlmEventSelectorServiceTest extends TestCase
{
    /**
     * @throws Exception
     */
    public function testGetLlmEvents(): void
    {
        $name1 = $this->faker->word();
        $name2 = $this->faker->word();
        $description1 = $this->faker->text();
        $description2 = $this->faker->text();

        $llmEvent1 = new LlmEvent();
        $llmEvent1->setName($name1);
        $llmEvent1->setDescription($description1);

        $llmEvent2 = new LlmEvent();
        $llmEvent2->setName($name2);
        $llmEvent2->setDescription($description2);

        $llmEvents = [$llmEvent1, $llmEvent2];

        $llmEventsCollection = new LlmEventCollection($llmEvents);

        $llmEventsTable = $this->createMock(LlmEventsTable::class);
        $llmEventsTable
            ->method('getLlmEvents')
            ->willReturn($llmEventsCollection);

        $selector = new LlmEventSelectorService($llmEventsTable);

        $this->assertSame($llmEventsCollection, $selector->getLlmEvents());
    }
}
