<?php

namespace tests\Unit\module\STLlmEvent\Service;

use PHPUnit\Framework\MockObject\Exception;
use STLlmEvent\Data\LlmEventsTable;
use STLlmEvent\Service\LlmEventRemoverService;
use tests\TestCase;

class LlmEventRemoverServiceTest extends TestCase
{
    /**
     * @throws Exception
     */
    public function testRemove(): void
    {
        $llmEventId = $this->faker->numberBetween(1, 100);

        $llmEventsTable = $this->createMock(LlmEventsTable::class);
        $llmEventsTable
            ->expects($this->once())
            ->method('deleteLlmEvent')
            ->with($llmEventId);

        $remover = new LlmEventRemoverService($llmEventsTable);
        $remover->delete($llmEventId);
    }
}
