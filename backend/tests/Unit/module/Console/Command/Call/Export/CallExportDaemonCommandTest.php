<?php

declare(strict_types=1);

namespace tests\Unit\module\Console\Command\Call\Export;

use Console\Command\Call\Export\CallExportDaemonCommand;
use Laminas\Cli\Input\ParamAwareInputInterface;
use PHPUnit\Framework\MockObject\Exception;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;
use STCall\Daemon\Export\CallExportDaemon;
use STRabbit\Service\DaemonService;
use Symfony\Component\Console\Exception\ExceptionInterface;
use Symfony\Component\Console\Output\OutputInterface;
use tests\TestCase;

final class CallExportDaemonCommandTest extends TestCase
{
    /**
     * @return void
     * @throws Exception
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     * @throws ExceptionInterface
     */
    public function testRun(): void
    {
        $daemon = $this->createMock(CallExportDaemon::class);
        $daemon
            ->expects(self::once())
            ->method('setQueueName')
            ->with(CallExportDaemon::CALLS_EXPORTS_QUEUE_NAME)
            ->willReturnSelf();
        $daemon
            ->expects(self::once())
            ->method('setErrorQueueName')
            ->with(CallExportDaemon::CALLS_EXPORTS_QUEUE_ERROR_NAME);
        $this->serviceManager->setService(CallExportDaemon::class, $daemon);

        $daemonService = $this->createMock(DaemonService::class);
        $daemonService->expects($this->once())->method('run')->with($daemon);
        $this->serviceManager->setService(DaemonService::class, $daemonService);

        $input = $this->createMock(ParamAwareInputInterface::class);
        $output = $this->createMock(OutputInterface::class);

        /**
         * @var CallExportDaemonCommand $command
         */
        $command = $this->serviceManager->get(CallExportDaemonCommand::class);
        $result = $command->run($input, $output);

        $this->assertSame(1, $result);
    }
}
