<?php

declare(strict_types=1);

namespace tests\Unit\module\Console\Command\Call\Export;

use Console\Command\Call\Export\CallExportCommand;
use Laminas\Cli\Input\ParamAwareInputInterface;
use PHPUnit\Framework\MockObject\Exception;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;
use STCall\Service\Export\RunCallsExportsService;
use Symfony\Component\Console\Exception\ExceptionInterface;
use Symfony\Component\Console\Output\OutputInterface;
use tests\TestCase;

final class CallExportCommandTest extends TestCase
{
    /**
     * @return void
     * @throws Exception
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     * @throws ExceptionInterface
     */
    public function testRun(): void
    {
        $runCallsExportsService = $this->createMock(RunCallsExportsService::class);
        $runCallsExportsService->expects($this->once())->method('run');
        $this->serviceManager->setService(RunCallsExportsService::class, $runCallsExportsService);

        $input = $this->createMock(ParamAwareInputInterface::class);
        $output = $this->createMock(OutputInterface::class);

        /**
         * @var CallExportCommand $command
         */
        $command = $this->serviceManager->get(CallExportCommand::class);
        $result = $command->run($input, $output);

        $this->assertSame(0, $result);
    }
}
