<?php

declare(strict_types=1);

namespace tests\Unit\module\STAlgo\Service;

use Laminas\Db\ResultSet\ResultSet;
use PHPUnit\Framework\MockObject\Exception;
use ReflectionException;
use STA<PERSON>go\Data\AlgoApisTable;
use STA<PERSON>go\Data\CompaniesAlgoApisTable;
use STAlgo\Entity\AlgoApi;
use STAlgo\Entity\AlgoApiCollection;
use STAlgo\Service\AlgoApiService;
use STApi\Entity\Exception\NotFoundApiException;
use STLib\Mvc\Hydrator\Hydrator;
use tests\TestCase;

final class AlgoApiServiceTest extends TestCase
{
    /**
     * @throws Exception
     * @throws NotFoundApiException
     * @throws ReflectionException
     */
    public function testGetAlgoApis(): void
    {
        $algoApi1 = $this->createMock(AlgoApi::class);
        $algoApi2 = $this->createMock(AlgoApi::class);

        $algoApiData1 = ['key1' => 'value1'];
        $algoApiData2 = ['key2' => 'value2'];
        $algoApisData = [$algoApiData1, $algoApiData2];

        $algoApisResultSet = new ResultSet();
        $algoApisResultSet->initialize($algoApisData);

        $algoApisTable = $this->createMock(AlgoApisTable::class);
        $algoApisTable
            ->method('getApis')
            ->willReturn($algoApisResultSet);

        $hydratorMap = [
            [$algoApiData1, AlgoApi::class, $algoApi1],
            [$algoApiData2, AlgoApi::class, $algoApi2]
        ];
        $hydrator = $this->createMock(Hydrator::class);
        $hydrator
            ->method('hydrateClass')
            ->willReturnMap($hydratorMap);

        $algoApisCollection = new AlgoApiCollection([$algoApi1, $algoApi2]);

        $companiesAlgoApisTable = $this->createMock(CompaniesAlgoApisTable::class);

        $service = new AlgoApiService($algoApisTable, $companiesAlgoApisTable, $hydrator);
        $this->assertEquals($algoApisCollection, $service->getAlgoApis());
    }

    /**
     * @throws Exception
     * @throws NotFoundApiException
     * @throws ReflectionException
     */
    public function testGetAlgoApisWithIdsFilter(): void
    {
        $algoApiId1 = $this->faker->numberBetween(1, 100);
        $algoApiId2 = $this->faker->numberBetween(101, 200);

        $algoApiIds = [$algoApiId1, $algoApiId2];

        $algoApi1 = $this->createMock(AlgoApi::class);
        $algoApi2 = $this->createMock(AlgoApi::class);

        $algoApiData1 = ['key1' => 'value1'];
        $algoApiData2 = ['key2' => 'value2'];
        $algoApisData = [$algoApiData1, $algoApiData2];

        $algoApisResultSet = new ResultSet();
        $algoApisResultSet->initialize($algoApisData);

        $algoApisTable = $this->createMock(AlgoApisTable::class);
        $algoApisTable
            ->method('getApis')
            ->with($algoApiIds)
            ->willReturn($algoApisResultSet);

        $hydratorMap = [
            [$algoApiData1, AlgoApi::class, $algoApi1],
            [$algoApiData2, AlgoApi::class, $algoApi2]
        ];
        $hydrator = $this->createMock(Hydrator::class);
        $hydrator
            ->method('hydrateClass')
            ->willReturnMap($hydratorMap);

        $algoApisCollection = new AlgoApiCollection([$algoApi1, $algoApi2]);

        $companiesAlgoApisTable = $this->createMock(CompaniesAlgoApisTable::class);

        $service = new AlgoApiService($algoApisTable, $companiesAlgoApisTable, $hydrator);
        $this->assertEquals($algoApisCollection, $service->getAlgoApis($algoApiIds));
    }

    /**
     * @throws ReflectionException
     * @throws NotFoundApiException
     */
    public function testGetAlgoApisIndexedByPath(): void
    {
        $algoApiId1 = $this->faker->numberBetween(1, 100);
        $algoApiId2 = $this->faker->numberBetween(101, 200);
        $algoApiId3 = $this->faker->numberBetween(201, 300);

        $path1 = $this->faker->text(10);
        $path2 = $this->faker->text(10);
        $path3 = $this->faker->text(10);

        $algoApi1 = new AlgoApi();
        $algoApi1->setPath($path1);
        $algoApi1->setAnalyzeMethod(CompaniesAlgoApisTable::NER_ANALYZE_METHOD);

        $algoApi2 = new AlgoApi();
        $algoApi2->setPath($path2);
        $algoApi2->setAnalyzeMethod($this->faker->word());

        $algoApi3 = new AlgoApi();
        $algoApi3->setPath($path3);
        $algoApi3->setAnalyzeMethod(CompaniesAlgoApisTable::NER_ANALYZE_METHOD);

        $originalAlgoApiCollection = new AlgoApiCollection();
        $originalAlgoApiCollection->add($algoApi1, $algoApiId1);
        $originalAlgoApiCollection->add($algoApi2, $algoApiId2);
        $originalAlgoApiCollection->add($algoApi3, $algoApiId3);

        $service = $this->getMockBuilder(AlgoApiService::class)
            ->onlyMethods(['getAlgoApis'])
            ->disableOriginalConstructor()
            ->getMock();

        $service->method('getAlgoApis')->willReturn($originalAlgoApiCollection);

        $indexedByPathAlgoApiCollection = $service->getNerAlgoApisIndexedByPath();

        $this->assertFalse($indexedByPathAlgoApiCollection->isEmpty());

        // $algoApi1
        $this->assertTrue($indexedByPathAlgoApiCollection->keyExists($path1));
        $this->assertSame($algoApi1, $indexedByPathAlgoApiCollection->offsetGet($path1));

        // $algoApi3
        $this->assertTrue($indexedByPathAlgoApiCollection->keyExists($path3));
        $this->assertSame($algoApi3, $indexedByPathAlgoApiCollection->offsetGet($path3));
    }
}
