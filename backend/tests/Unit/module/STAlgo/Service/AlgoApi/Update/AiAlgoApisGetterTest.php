<?php

declare(strict_types=1);

namespace tests\Unit\module\STAlgo\Service\AlgoApi\Update;

use GuzzleHttp\Exception\GuzzleException;
use PHPUnit\Framework\MockObject\Exception;
use STAlgo\Entity\AlgoApiCollection;
use STAlgo\Service\AlgoApi\Update\AiAlgoApisGetter;
use STAlgo\Service\AlgoApi\Update\AlgoApiEntityBuilder;
use STAlgo\Service\Client;
use tests\TestCase;

final class AiAlgoApisGetterTest extends TestCase
{
    /**
     * @return void
     * @throws Exception
     * @throws GuzzleException
     */
    public function testProvide(): void
    {
        $algoApisData = [['some key' => 'some value']];

        $client = $this->createMock(Client::class);
        $client->method('getModels')->willReturn($algoApisData);

        $algoApisCollection = $this->createMock(AlgoApiCollection::class);

        $entityBuilder = $this->createMock(AlgoApiEntityBuilder::class);
        $entityBuilder
            ->method('buildCollection')
            ->with($algoApisData)
            ->willReturn($algoApisCollection);

        $getter = new AiAlgoApisGetter($client, $entityBuilder);
        $this->assertSame($algoApisCollection, $getter->get());
    }
}
