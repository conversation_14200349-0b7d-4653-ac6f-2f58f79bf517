<?php

declare(strict_types=1);

namespace tests\Unit\module\STAlgo\Service;

use GuzzleHttp\Client as GuzzleClient;
use GuzzleHttp\Exception\GuzzleException;
use JsonException;
use PHPUnit\Framework\MockObject\Exception;
use Psr\Http\Message\ResponseInterface;
use Psr\Http\Message\StreamInterface;
use STAlgo\Entity\AlgoApi;
use STAlgo\Service\AlgoEvents\RequestCreation\EventsAlgoApiRequestInterface;
use STAlgo\Service\Client;
use STAlgo\Service\Interfaces\ConfigurationInterface;
use STAlgo\Service\ParamsBuilding\RequestParams;
use STRoboTruck\Service\DataCollection\DataCollector;
use tests\TestCase;

class ClientTest extends TestCase
{
    /**
     * @dataProvider envProvider
     * @param mixed $requestEnv
     * @param string $expectedEnv
     * @return void
     * @throws Exception
     * @throws GuzzleException
     */
    public function testAnalyze(mixed $requestEnv, string $expectedEnv): void
    {
        $url = $this->faker->url();
        $params = [
            'key1' => $this->faker->word(),
            'key2' => $this->faker->word(),
            'key3' => [$this->faker->word() => $this->faker->word()],
        ];

        $headers = ['header1' => $this->faker->word(), 'header2' => $this->faker->word()];

        $request = $this->createMock(EventsAlgoApiRequestInterface::class);
        $request->method('getUrl')->willReturn($url);
        $request->method('getParams')->willReturn($params);
        $request->method('getEnv')->willReturn($requestEnv);

        $requestParams = [
            'body' => json_encode($params),
            'headers' => $headers,
            'query' => [
                'env' => $expectedEnv,
            ],
        ];

        $responseContent = $this->faker->text();
        $stream = $this->createMock(StreamInterface::class);
        $stream
            ->method('getContents')
            ->willReturn($responseContent);

        $response = $this->createMock(ResponseInterface::class);
        $response->method('getBody')->willReturn($stream);

        $guzzleClient = $this->createMock(GuzzleClient::class);
        $guzzleClient
            ->expects($this->once())
            ->method('post')
            ->with($url, $requestParams)
            ->willReturn($response);

        $configuration = $this->createMock(ConfigurationInterface::class);

        $client = $this->getMockBuilder(Client::class)
            ->onlyMethods(['getGuzzleClient', 'getHeaders'])
            ->setConstructorArgs([
                $configuration,
                $this->createMock(DataCollector::class)
            ])
            ->getMock();
        $client
            ->method('getGuzzleClient')
            ->willReturn($guzzleClient);
        $client
            ->method('getHeaders')
            ->willReturn($headers);

        $this->assertSame($responseContent, $client->analyze($request));
    }

    /**
     * @throws Exception
     * @throws GuzzleException
     */
    public function testGetEvents()
    {
        $algoApiUrl = 'https://ai-solutions.robonote.io';
        $algoApiKey = 'some-test-key'; // from backend/phpunit.xml
        $env = 'test';

        $headers = [
            'Content-Type' => 'application/json',
            'Authorization' => 'Bearer ' . $algoApiKey
        ];

        $algoApiPath = $this->faker->filePath();
        $algoApi = $this->createMock(AlgoApi::class);
        $algoApi->method('getPath')->willReturn($algoApiPath);

        $url = $algoApiUrl . $algoApiPath . '/get_events';
        $responseContent = 'some json response';

        $stream = $this->createMock(StreamInterface::class);
        $stream
            ->method('getContents')
            ->willReturn($responseContent);

        $response = $this->createMock(ResponseInterface::class);
        $response->method('getBody')->willReturn($stream);

        $guzzleClient = $this->createMock(GuzzleClient::class);
        $guzzleClient
            ->method('get')
            ->with($url, ['headers' => $headers, 'query' => ['env' => $env]])
            ->willReturn($response);

        $configuration = $this->createMock(ConfigurationInterface::class);

        $client = $this->getMockBuilder(Client::class)
            ->onlyMethods(['getGuzzleClient'])
            ->setConstructorArgs([
                $configuration,
                $this->createMock(DataCollector::class)
            ])
            ->getMock();
        $client->method('getGuzzleClient')->willReturn($guzzleClient);

        $this->assertSame($responseContent, $client->getEvents($algoApi));
    }

    /**
     * @throws Exception
     * @throws GuzzleException
     */
    public function testGetSpeakers()
    {
        $algoApiUrl = 'https://ai-solutions.robonote.io';
        $algoApiKey = 'some-test-key'; // from backend/phpunit.xml
        $env = 'test';

        $headers = [
            'Content-Type' => 'application/json',
            'Authorization' => 'Bearer ' . $algoApiKey
        ];

        $url = $algoApiUrl . '/api/classV1';

        $params = ['some key' => 'some value'];
        $responseContent = 'some json response';

        $stream = $this->createMock(StreamInterface::class);
        $stream
            ->method('getContents')
            ->willReturn($responseContent);

        $response = $this->createMock(ResponseInterface::class);
        $response->method('getBody')->willReturn($stream);

        $guzzleClient = $this->createMock(GuzzleClient::class);
        $guzzleClient
            ->method('post')
            ->with($url, ['body' => json_encode($params), 'headers' => $headers, 'query' => ['env' => $env]])
            ->willReturn($response);

        $configuration = $this->createMock(ConfigurationInterface::class);

        $client = $this->getMockBuilder(Client::class)
            ->onlyMethods(['getGuzzleClient'])
            ->setConstructorArgs([
                $configuration,
                $this->createMock(DataCollector::class)
            ])
            ->getMock();
        $client->method('getGuzzleClient')->willReturn($guzzleClient);

        $this->assertSame($responseContent, $client->getSpeakers($params));
    }

    /**
     * @throws Exception
     * @throws GuzzleException
     */
    public function testGetModels(): void
    {
        $algoApiUrl = $this->faker->url();
        $algoApiKey = $this->faker->text();

        $configuration = $this->createMock(ConfigurationInterface::class);
        $configuration
            ->method('get')
            ->with('algo')
            ->willReturn(['api_url' => $algoApiUrl, 'api_key' => $algoApiKey]);

        $headers = [
            'Content-Type' => 'application/json',
            'Authorization' => 'Bearer ' . $algoApiKey
        ];

        $url = $algoApiUrl . '/api/get_models_with_descriptions';

        $algoApisData = [['some key' => 'some value']];
        $responseContent = json_encode($algoApisData);

        $stream = $this->createMock(StreamInterface::class);
        $stream
            ->method('getContents')
            ->willReturn($responseContent);

        $response = $this->createMock(ResponseInterface::class);
        $response->method('getBody')->willReturn($stream);

        $guzzleClient = $this->createMock(GuzzleClient::class);
        $guzzleClient
            ->method('get')
            ->with($url, ['headers' => $headers])
            ->willReturn($response);

        $client = $this->getMockBuilder(Client::class)
            ->onlyMethods(['getGuzzleClient'])
            ->setConstructorArgs([
                $configuration,
                $this->createMock(DataCollector::class)
            ])
            ->getMock();
        $client->method('getGuzzleClient')->willReturn($guzzleClient);

        $this->assertSame($algoApisData, $client->getModels());
    }

    /**
     * @return void
     * @throws Exception
     * @throws GuzzleException
     * @throws JsonException
     */
    public function testGetCallSummarization(): void
    {
        $summarizationAlgoApiUrl = $this->faker->url();
        $algoApiKey = $this->faker->text();

        $callData = ['some call data'];
        $requestParams = $this->createMock(RequestParams::class);
        $requestParams->method('toArray')->willReturn($callData);

        $configuration = $this->createMock(ConfigurationInterface::class);
        $configuration
            ->method('get')
            ->with('algo')
            ->willReturn(['summarization_api_url' => $summarizationAlgoApiUrl, 'api_key' => $algoApiKey]);

        $headers = [
            'Content-Type' => 'application/json',
            'Authorization' => 'Bearer ' . $algoApiKey
        ];

        $url = $summarizationAlgoApiUrl . '/api/algo/summarizationV3';

        $summarizationData = [['some key' => 'some value']];
        $responseContent = json_encode($summarizationData);

        $stream = $this->createMock(StreamInterface::class);
        $stream
            ->method('getContents')
            ->willReturn($responseContent);

        $response = $this->createMock(ResponseInterface::class);
        $response->method('getBody')->willReturn($stream);

        $guzzleClient = $this->createMock(GuzzleClient::class);
        $guzzleClient
            ->method('post')
            ->with($url, ['body' => json_encode($callData), 'headers' => $headers, 'timeout' => 120])
            ->willReturn($response);

        $dataCollector = $this->createMock(DataCollector::class);

        $client = $this->getMockBuilder(Client::class)
            ->onlyMethods(['getGuzzleClient'])
            ->setConstructorArgs([$configuration, $dataCollector])
            ->getMock();
        $client->method('getGuzzleClient')->willReturn($guzzleClient);

        $this->assertSame($summarizationData, $client->getCallSummarization($requestParams));
    }

    /**
     * @throws Exception
     * @throws GuzzleException
     * @throws JsonException
     */
    public function testImproveLlmEvent(): void
    {
        $eventName = $this->faker->text();
        $eventDescription = $this->faker->text();

        $improveUrl = $this->faker->url();
        $algoApiKey = $this->faker->text();

        $configuration = $this->createMock(ConfigurationInterface::class);
        $configuration
            ->method('get')
            ->with('algo')
            ->willReturn(['improve_llm_event_url' => $improveUrl, 'api_key' => $algoApiKey]);

        $headers = [
            'Content-Type' => 'application/json',
            'Authorization' => 'Bearer ' . $algoApiKey
        ];

        $url = $improveUrl . '/api/algo/tuningV1/promptV1';

        $improvedData = [['some key' => 'some value']];
        $responseContent = json_encode($improvedData);

        $stream = $this->createMock(StreamInterface::class);
        $stream
            ->method('getContents')
            ->willReturn($responseContent);

        $response = $this->createMock(ResponseInterface::class);
        $response->method('getBody')->willReturn($stream);

        $requestData = [
            'event' => [
                'title' => $eventName,
                'description' => $eventDescription,
            ],
        ];
        $guzzleClient = $this->createMock(GuzzleClient::class);
        $guzzleClient
            ->method('post')
            ->with($url, ['body' => json_encode($requestData), 'headers' => $headers, 'timeout' => 120])
            ->willReturn($response);

        $dataCollector = $this->createMock(DataCollector::class);

        $client = $this->getMockBuilder(Client::class)
            ->onlyMethods(['getGuzzleClient'])
            ->setConstructorArgs([$configuration, $dataCollector])
            ->getMock();
        $client->method('getGuzzleClient')->willReturn($guzzleClient);

        $this->assertSame($improvedData, $client->improveLlmEvent($eventName, $eventDescription));
    }

    /**
     * @return void
     * @throws Exception
     * @throws GuzzleException
     * @throws JsonException
     */
    public function testImproveChecklistPoint(): void
    {
        $title = $this->faker->text();
        $expectedActions = $this->faker->text();
        $goodPerformanceDescription = $this->faker->text();
        $badPerformanceDescription = $this->faker->text();

        $improveUrl = $this->faker->url();
        $algoApiKey = $this->faker->text();

        $configuration = $this->createMock(ConfigurationInterface::class);
        $configuration
            ->method('get')
            ->with('algo')
            ->willReturn(['improve_checklist_point_url' => $improveUrl, 'api_key' => $algoApiKey]);

        $headers = [
            'Content-Type' => 'application/json',
            'Authorization' => 'Bearer ' . $algoApiKey
        ];

        $url = $improveUrl . '/api/algo/tuningV1/checklistV5';

        $improvedData = [['some key' => 'some value']];
        $responseContent = json_encode($improvedData);

        $stream = $this->createMock(StreamInterface::class);
        $stream
            ->method('getContents')
            ->willReturn($responseContent);

        $response = $this->createMock(ResponseInterface::class);
        $response->method('getBody')->willReturn($stream);

        $requestData = [
            'endpoint' => 'checklistV6',
            'event' => [
                'title' => $title,
                'expected_criteria' => $expectedActions,
                'green_assessment_description' => $goodPerformanceDescription,
                'red_assessment_description' => $badPerformanceDescription,
            ]
        ];
        $guzzleClient = $this->createMock(GuzzleClient::class);
        $guzzleClient
            ->method('post')
            ->with($url, ['body' => json_encode($requestData), 'headers' => $headers, 'timeout' => 120])
            ->willReturn($response);

        $dataCollector = $this->createMock(DataCollector::class);

        $client = $this->getMockBuilder(Client::class)
            ->onlyMethods(['getGuzzleClient'])
            ->setConstructorArgs([$configuration, $dataCollector])
            ->getMock();
        $client->method('getGuzzleClient')->willReturn($guzzleClient);

        $this->assertSame($improvedData, $client->improveChecklistPoint($requestData));
    }

    public static function envProvider(): array
    {
        return [
            [null, 'test'],
            ['some env', 'some env'],
        ];
    }

    /**
     * @throws Exception
     * @throws GuzzleException
     * @throws JsonException
     */
    public function testTranslate(): void
    {
        $params = ['some key' => 'some value'];
        $translationAlgoApiUrl = $this->faker->url();
        $algoApiKey = $this->faker->text();

        $configuration = $this->createMock(ConfigurationInterface::class);
        $configuration
            ->method('get')
            ->with('algo')
            ->willReturn(['translation_api_url' => $translationAlgoApiUrl, 'api_key' => $algoApiKey]);

        $headers = [
            'Content-Type' => 'application/json',
            'Authorization' => 'Bearer ' . $algoApiKey
        ];

        $url = $translationAlgoApiUrl . '/api/translation/translationV1';

        $translationData = [['some translated key' => 'some translated value']];
        $responseContent = json_encode($translationData);

        $stream = $this->createMock(StreamInterface::class);
        $stream
            ->method('getContents')
            ->willReturn($responseContent);

        $response = $this->createMock(ResponseInterface::class);
        $response->method('getBody')->willReturn($stream);

        $guzzleClient = $this->createMock(GuzzleClient::class);
        $guzzleClient
            ->method('post')
            ->with($url, ['headers' => $headers, 'body' => json_encode($params), 'timeout' => 300])
            ->willReturn($response);

        $dataCollector = $this->createMock(DataCollector::class);

        $client = $this->getMockBuilder(Client::class)
            ->onlyMethods(['getGuzzleClient'])
            ->setConstructorArgs([$configuration, $dataCollector])
            ->getMock();
        $client->method('getGuzzleClient')->willReturn($guzzleClient);

        $this->assertSame($translationData, $client->translate($params));
    }

    /**
     * @throws Exception
     * @throws GuzzleException
     * @throws JsonException
     */
    public function testGetChecklistResult(): void
    {
        $params = ['some key' => 'some value'];
        $checklistAlgoApiUrl = $this->faker->url();
        $algoApiKey = $this->faker->text();

        $configuration = $this->createMock(ConfigurationInterface::class);
        $configuration
            ->method('get')
            ->with('algo')
            ->willReturn(['checklist_api_url' => $checklistAlgoApiUrl, 'api_key' => $algoApiKey]);

        $headers = [
            'Content-Type' => 'application/json',
            'Authorization' => 'Bearer ' . $algoApiKey
        ];

        $url = $checklistAlgoApiUrl . '/api/checklist/checklistV6';

        $checklistsData = [['some another key' => 'some another value']];
        $responseContent = json_encode($checklistsData);

        $stream = $this->createMock(StreamInterface::class);
        $stream
            ->method('getContents')
            ->willReturn($responseContent);

        $response = $this->createMock(ResponseInterface::class);
        $response->method('getBody')->willReturn($stream);

        $guzzleClient = $this->createMock(GuzzleClient::class);
        $guzzleClient
            ->method('post')
            ->with($url, [
                'headers' => $headers,
                'body' => json_encode($params),
                'query' => [
                    'env' => 'test',
                ],
                'timeout' => 180
            ])
            ->willReturn($response);

        $dataCollector = $this->createMock(DataCollector::class);
        $client = $this->getMockBuilder(Client::class)
            ->onlyMethods(['getGuzzleClient'])
            ->setConstructorArgs([$configuration, $dataCollector])
            ->getMock();
        $client->method('getGuzzleClient')->willReturn($guzzleClient);

        $this->assertSame($checklistsData, $client->getChecklistResult($params));
    }
}
