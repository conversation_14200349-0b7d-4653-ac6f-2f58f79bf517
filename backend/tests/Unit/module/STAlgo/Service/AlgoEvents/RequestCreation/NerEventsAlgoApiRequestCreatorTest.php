<?php

declare(strict_types=1);

namespace tests\Unit\module\STAlgo\Service\AlgoEvents\RequestCreation;

use Laminas\Db\ResultSet\ResultSet;
use PHPUnit\Framework\MockObject\Exception;
use ReflectionException;
use STAlgo\Data\AlgoApisTable;
use STAlgo\Entity\AlgoApi;
use STAlgo\Entity\AlgoApiCollection;
use STAlgo\Service\AlgoApiService;
use STAlgo\Service\AlgoEvents\RequestCreation\NerEventsAlgoApiRequest;
use STAlgo\Service\AlgoEvents\RequestCreation\NerEventsAlgoApiRequestCreator;
use STAlgo\Service\ParamsBuilding\RequestParams;
use STApi\Entity\Exception\NotFoundApiException;
use STCompany\Entity\Company;
use STConfiguration\Service\ConfigurationService;
use tests\TestCase;

class NerEventsAlgoApiRequestCreatorTest extends TestCase
{
    /**
     * @return void
     * @throws Exception
     * @throws ReflectionException
     * @throws NotFoundApiException
     */
    public function testCreate(): void
    {
        $companyId = $this->faker->numberBetween(1, 100);
        $company = $this->createMock(Company::class);
        $company->method('getId')->willReturn($companyId);

        $requestParamsData = ['some key' => 'some value'];
        $requestParams = $this->createMock(RequestParams::class);
        $requestParams
            ->method('toArray')
            ->willReturn($requestParamsData);

        $algoApiId1 = $this->faker->numberBetween(101, 200);
        $algoApiId2 = $this->faker->numberBetween(201, 300);

        $algoApisData = [
            ['algo_api_id' => $algoApiId1],
            ['algo_api_id' => $algoApiId2]
        ];
        $algoApisResultSet = new ResultSet();
        $algoApisResultSet->initialize($algoApisData);

        $algoApisTable = $this->createMock(AlgoApisTable::class);
        $algoApisTable
            ->method('getCompanyNerAlgoApis')
            ->with($companyId)
            ->willReturn($algoApisResultSet);

        $algoApi1 = $this->createMock(AlgoApi::class);
        $algoApi2 = $this->createMock(AlgoApi::class);

        $algoApisCollection = new AlgoApiCollection([$algoApi1, $algoApi2]);
        $algoApiService = $this->createMock(AlgoApiService::class);
        $algoApiService
            ->method('getAlgoApis')
            ->with([$algoApiId1, $algoApiId2])
            ->willReturn($algoApisCollection);

        $algoApiUrl = $this->faker->url();
        $configuration = $this->createMock(ConfigurationService::class);
        $configuration
            ->method('get')
            ->with('algo')
            ->willReturn(['api_url' => $algoApiUrl]);

        $creator = new NerEventsAlgoApiRequestCreator(
            $algoApisTable, $algoApiService, $configuration
        );
        $requests = $creator->create($company, $requestParams);

        $this->assertCount(2, $requests);

        $this->assertInstanceOf(NerEventsAlgoApiRequest::class, $requests[0]);
        $this->assertStringStartsWith($algoApiUrl, $requests[0]->getUrl());
        $this->assertSame($requestParamsData, $requests[0]->getParams());

        $this->assertInstanceOf(NerEventsAlgoApiRequest::class, $requests[1]);
        $this->assertStringStartsWith($algoApiUrl, $requests[1]->getUrl());
        $this->assertSame($requestParamsData, $requests[1]->getParams());
    }

    /**
     * @throws Exception
     * @throws NotFoundApiException
     * @throws ReflectionException
     */
    public function testCreateWithAlgoApiParams(): void
    {
        $algoApiId1 = $this->faker->numberBetween(1, 100);
        $algoApiId2 = $this->faker->numberBetween(101, 200);

        $env1 = $this->faker->word();
        $env2 = $this->faker->word();

        $analyzeMethod1 = $this->faker->text();
        $analyzeMethod2 = $this->faker->text();

        $algoApiParams = [
            [
                'algo_api_id' => $algoApiId1,
                'env' => $env1,
                'analyze_method' => $analyzeMethod1
            ],
            [
                'algo_api_id' => $algoApiId2,
                'env' => $env2,
                'analyze_method' => $analyzeMethod2
            ]
        ];

        $company = $this->createMock(Company::class);

        $requestParamsData = ['some key' => 'some value'];
        $requestParams = $this->createMock(RequestParams::class);
        $requestParams
            ->method('toArray')
            ->willReturn($requestParamsData);

        $algoApisTable = $this->createMock(AlgoApisTable::class);

        $algoApi1 = $this->createMock(AlgoApi::class);
        $algoApi2 = $this->createMock(AlgoApi::class);

        $algoApi2->method('getId')->willReturn($algoApiId2);
        $algoApi2->method('getId')->willReturn($algoApiId2);

        $algoApisCollection = new AlgoApiCollection([
            $algoApiId1 => $algoApi1,
            $algoApiId2 => $algoApi2
        ]);

        $algoApiService = $this->createMock(AlgoApiService::class);
        $algoApiService
            ->method('getAlgoApis')
            ->with([$algoApiId1, $algoApiId2])
            ->willReturn($algoApisCollection);

        $algoApiUrl = $this->faker->url();
        $configuration = $this->createMock(ConfigurationService::class);
        $configuration
            ->method('get')
            ->with('algo')
            ->willReturn(['api_url' => $algoApiUrl]);

        $creator = new NerEventsAlgoApiRequestCreator(
            $algoApisTable, $algoApiService, $configuration
        );
        $requests = $creator->create($company, $requestParams, $algoApiParams);

        $this->assertCount(2, $requests);

        $this->assertInstanceOf(NerEventsAlgoApiRequest::class, $requests[0]);
        $this->assertStringStartsWith($algoApiUrl, $requests[0]->getUrl());
        $this->assertSame($requestParamsData, $requests[0]->getParams());
        $this->assertStringContainsString($analyzeMethod1, $requests[0]->getUrl());
        $this->assertSame($env1, $requests[0]->getEnv());

        $this->assertInstanceOf(NerEventsAlgoApiRequest::class, $requests[1]);
        $this->assertStringStartsWith($algoApiUrl, $requests[1]->getUrl());
        $this->assertSame($requestParamsData, $requests[1]->getParams());
        $this->assertStringContainsString($analyzeMethod2, $requests[1]->getUrl());
        $this->assertSame($env2, $requests[1]->getEnv());
    }
}
