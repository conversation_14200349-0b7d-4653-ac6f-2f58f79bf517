<?php

declare(strict_types=1);

namespace tests\Unit\module\STUser\Service;

use PHPUnit\Framework\MockObject\Exception;
use <PERSON>Three\Auth\TwoFactorAuth;
use RobThree\Auth\TwoFactorAuthException;
use STUser\Service\Interfaces\ConfigurationInterface;
use STUser\Service\TwoFactorAuthFactory;
use tests\TestCase;

final class TwoFactorAuthFactoryTest extends TestCase
{
    /**
     * @return void
     * @throws Exception
     * @throws TwoFactorAuthException
     */
    public function testCreate(): void
    {
        $issuer = $this->faker->word();
        $configuration = $this->createMock(ConfigurationInterface::class);
        $configuration
            ->method('get')
            ->with('auth')
            ->willReturn([
                'two-factor-auth' => [
                    'issuer' => $issuer,
                ],
            ]);
        $factory = new TwoFactorAuthFactory($configuration);
        $twoFactorAuth = $factory->create();

        $this->assertInstanceOf(TwoFactorAuth::class, $twoFactorAuth);
        $this->assertStringContainsString($issuer, $twoFactorAuth->getQRText('', ''));
    }
}
