<?php

namespace tests\Unit\module\STRoboTruck\Service;

use PHPUnit\Framework\MockObject\Exception;
use STCompany\Entity\Company;
use STRoboTruck\Service\DataCollection\DataCollector;
use STRoboTruck\Service\ExternalLogCollectorService;
use STUser\Entity\User;
use tests\TestCase;

class ExternalLogCollectorServiceTest extends TestCase
{
    /**
     * @throws Exception
     */
    public function testCollect(): void
    {
        $roboTruckEventName = 'external_log';

        $source = $this->faker->word();

        $userId = $this->faker->numberBetween(1, 100);
        $user = $this->createMock(User::class);
        $user
            ->method('getId')
            ->willReturn($userId);

        $parameters = [
            'key 1' => 'value 1',
            'key 2' => 'value 2',
        ];

        $roboTruckEventMessage = 'External log';
        $roboTruckEventExtra = [
            'user_id' => $userId,
            'parameters' => $parameters,
        ];

        $dataCollector = $this->createMock(DataCollector::class);
        $dataCollector
            ->expects($this->once())
            ->method('collect')
            ->with($roboTruckEventName, $roboTruckEventMessage, $roboTruckEventExtra, null, $source);

        $externalLogCollector = new ExternalLogCollectorService($dataCollector);
        $externalLogCollector->collect($source, $parameters, $user);
    }

    /**
     * @throws Exception
     */
    public function testCollectWhenNoUser(): void
    {
        $roboTruckEventName = 'external_log';

        $source = $this->faker->word();

        $parameters = [
            'key 1' => 'value 1',
            'key 2' => 'value 2',
        ];

        $roboTruckEventMessage = 'External log';
        $roboTruckEventExtra = [
            'user_id' => null,
            'parameters' => $parameters,
        ];

        $dataCollector = $this->createMock(DataCollector::class);
        $dataCollector
            ->expects($this->once())
            ->method('collect')
            ->with($roboTruckEventName, $roboTruckEventMessage, $roboTruckEventExtra, null, $source);

        $externalLogCollector = new ExternalLogCollectorService($dataCollector);
        $externalLogCollector->collect($source, $parameters);
    }
}
