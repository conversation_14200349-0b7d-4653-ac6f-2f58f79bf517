<?php

declare(strict_types=1);

namespace tests\Unit\module\STCall\Listeners;

use Lam<PERSON>\EventManager\EventInterface;
use PhpAmqpLib\Channel\AMQPChannel;
use PhpAmqpLib\Message\AMQPMessage;
use PHPUnit\Framework\MockObject\Exception;
use STCall\Listeners\CallStepFinishedListener;
use STCall\Service\Webhooks\Interfaces\WebhookServiceInterface;
use STCall\Service\Webhooks\WebhookServiceFactory;
use STCompany\Service\Webhooks\WebhookSettingsSelector;
use STRabbit\Service\RabbitService;
use STRoboTruck\Service\DataCollection\DataCollector;
use tests\TestCase;

final class CallStepFinishedListenerTest extends TestCase
{
    /**
     * @throws Exception
     */
    public function testListen(): void
    {
        $companyId = $this->faker->numberBetween(1, 100);
        $callId = $this->faker->uuid();

        $originalQueueName = $this->faker->sentence();
        $queueName = 'webhooks';
        $webhookType = $this->faker->word();

        $data = ['some_key' => 'some_value'];

        $getParamMap = [
            ['company_id', null, $companyId],
            ['call_id', null, $callId],
            ['queue_name', null, $originalQueueName],
            ['data', null, $data],
        ];
        $event = $this->createMock(EventInterface::class);
        $event->method('getParam')->willReturnMap($getParamMap);

        $webhookService = $this->createMock(WebhookServiceInterface::class);
        $webhookService->method('getType')->willReturn($webhookType);

        $webhookServiceFactory = $this->createMock(WebhookServiceFactory::class);
        $webhookServiceFactory->method('create')->with($originalQueueName)->willReturn($webhookService);

        $webhookSettingsSelector = $this->createMock(WebhookSettingsSelector::class);
        $webhookSettingsSelector
            ->method('isWebhooksEnabled')
            ->with($webhookType, $companyId)
            ->willReturn(true);

        $expectedData = [
            'company_id' => $companyId,
            'call_id' => $callId,
            'source' => $originalQueueName,
            'data' => $data,
        ];

        $channel = $this->createMock(AMQPChannel::class);
        $channel
            ->expects($this->once())
            ->method('basic_publish')
            ->with(
                self::callback(
                    function (AMQPMessage $message) use ($expectedData) {
                        $messageBody = json_decode($message->getBody(), true);

                        return $expectedData === $messageBody;
                    }
                ),
                '',
                $queueName
            );
        $channel
            ->expects($this->once())
            ->method('close');

        $rabbit = $this->createMock(RabbitService::class);
        $rabbit
            ->method('getChannel')
            ->willReturn($channel);

        $dataCollector = $this->createMock(DataCollector::class);

        $listener = new CallStepFinishedListener($webhookServiceFactory, $webhookSettingsSelector, $rabbit, $dataCollector);

        $listener->listen($event);
    }

    /**
     * @throws Exception
     */
    public function testListenWhenWebhooksIsOff(): void
    {
        $companyId = $this->faker->numberBetween(1, 100);
        $callId = $this->faker->uuid();

        $originalQueueName = $this->faker->sentence();
        $webhookType = $this->faker->word();

        $webhookService = $this->createMock(WebhookServiceInterface::class);
        $webhookService->method('getType')->willReturn($webhookType);

        $webhookServiceFactory = $this->createMock(WebhookServiceFactory::class);
        $webhookServiceFactory->method('create')->with($originalQueueName)->willReturn($webhookService);

        $webhookSettingsSelector = $this->createMock(WebhookSettingsSelector::class);
        $webhookSettingsSelector
            ->method('isWebhooksEnabled')
            ->with($webhookType, $companyId)
            ->willReturn(false);

        $getParamMap = [
            ['company_id', null, $companyId],
            ['call_id', null, $callId],
            ['queue_name', null, $originalQueueName],
            ['data', null, ['some data']],
        ];
        $event = $this->createMock(EventInterface::class);
        $event->method('getParam')->willReturnMap($getParamMap);

        $rabbit = $this->createMock(RabbitService::class);
        $rabbit->expects($this->never())->method('getChannel');

        $dataCollector = $this->createMock(DataCollector::class);
        $dataCollector->expects($this->never())->method('collect');

        $listener = new CallStepFinishedListener($webhookServiceFactory, $webhookSettingsSelector, $rabbit, $dataCollector);

        $listener->listen($event);
    }

    /**
     * @dataProvider wrongDataDataProvider
     * @param int|null $companyId
     * @param string|null $callId
     * @param array|null $data
     * @param string|null $originalQueueName
     * @return void
     * @throws Exception
     */
    public function testListenWhenWrongEventData(
        ?int $companyId,
        ?string $callId,
        ?array $data,
        ?string $originalQueueName
    ): void {
        $roboTruckEventName = 'add_step_results_to_webhooks_queue_fail';

        $getParamMap = [
            ['company_id', null, $companyId],
            ['call_id', null, $callId],
            ['queue_name', null, $originalQueueName],
            ['data', null, $data],
        ];
        $event = $this->createMock(EventInterface::class);
        $event->method('getParam')->willReturnMap($getParamMap);

        $webhookServiceFactory = $this->createMock(WebhookServiceFactory::class);
        $webhookSettingsSelector = $this->createMock(WebhookSettingsSelector::class);

        $rabbit = $this->createMock(RabbitService::class);
        $rabbit->expects($this->never())->method('getChannel');

        $roboTruckMessage = 'Fail to extract call data.';
        $roboTruckExtra = ['event' => json_encode($event)];

        $dataCollector = $this->createMock(DataCollector::class);
        $dataCollector
            ->expects($this->once())
            ->method('collect')
            ->with($roboTruckEventName, $roboTruckMessage, $roboTruckExtra, null);

        $listener = new CallStepFinishedListener($webhookServiceFactory, $webhookSettingsSelector, $rabbit, $dataCollector);

        $listener->listen($event);
    }

    public static function wrongDataDataProvider(): array
    {
        return [
            [null, 'some call id', ['some data'], 'some queue name'],
            [123, null, ['some data'], 'some queue name'],
            [123, 'some call id', null, 'some queue name'],
            [123, 'some call id', ['some data'], null],
        ];
    }
}
