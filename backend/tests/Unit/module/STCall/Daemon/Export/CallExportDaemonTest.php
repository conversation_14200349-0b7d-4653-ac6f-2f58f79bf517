<?php

declare(strict_types=1);

namespace tests\Unit\module\STCall\Daemon\Export;

use Carbon\Carbon;
use PHPUnit\Framework\MockObject\Exception;
use STCall\Daemon\Export\CallExportDaemon;
use STCall\Service\Export\CallExportGenerator;
use STCall\Service\Interfaces\CompanySaverInterface;
use STCall\Service\Interfaces\CompanySelectorInterface;
use STCompany\Entity\Company;
use tests\TestCase;
use tests\WithConsecutive;

final class CallExportDaemonTest extends TestCase
{
    /**
     * @throws Exception
     */
    public function testHandle(): void
    {
        $lastExportDate = Carbon::now()->subDays(4)->subHours(6);
        $startDate = (clone $lastExportDate)->addSecond();

        $now = Carbon::now();
        Carbon::setTestNow($now);

        $endDate = $now;

        $companyId = $this->faker->numberBetween(1, 100);
        $company = $this->createMock(Company::class);
        $company->method('getLastExportDate')->willReturn($lastExportDate);
        $company->expects($this->once())->method('setLastExportDate')->with($endDate);

        $companySelector = $this->createMock(CompanySelectorInterface::class);
        $companySelector->method('getCompany')->with($companyId)->willReturn($company);

        $message = json_encode(['company_id' => $companyId]);

        $callExportGeneratorService = $this->createMock(CallExportGenerator::class);
        $callExportGeneratorService
            ->expects($this->exactly(3))
            ->method('generate')
            ->with(
                ...
                WithConsecutive::create(
                    [$company, $startDate, $endDate, 'calls'],
                    [$company, $startDate, $endDate, 'paragraphs'],
                    [$company, $startDate, $endDate, 'events'],
                )
            );

        $companySaver = $this->createMock(CompanySaverInterface::class);
        $companySaver
            ->expects($this->once())
            ->method('saveCompany')
            ->with($company);

        $daemon = new CallExportDaemon($companySelector, $callExportGeneratorService, $companySaver);
        $daemon->handle($message);
    }

    /**
     * @throws Exception
     */
    public function testHandleWhenNoStartDate(): void
    {
        $now = Carbon::now();
        Carbon::setTestNow($now);

        $endDate = $now;

        $companyId = $this->faker->numberBetween(1, 100);
        $company = $this->createMock(Company::class);
        $company->method('getLastExportDate')->willReturn(null);
        $company->expects($this->once())->method('setLastExportDate')->with($endDate);

        $companySelector = $this->createMock(CompanySelectorInterface::class);
        $companySelector->method('getCompany')->with($companyId)->willReturn($company);

        $message = json_encode(['company_id' => $companyId]);

        $startDate = clone $endDate;
        $startDate->subDay();

        $callExportGeneratorService = $this->createMock(CallExportGenerator::class);
        $callExportGeneratorService
            ->expects($this->exactly(3))
            ->method('generate')
            ->with(
                ...
                WithConsecutive::create(
                    [$company, $startDate, $endDate, 'calls'],
                    [$company, $startDate, $endDate, 'paragraphs'],
                    [$company, $startDate, $endDate, 'events'],
                )
            );

        $companySaver = $this->createMock(CompanySaverInterface::class);
        $companySaver
            ->expects($this->once())
            ->method('saveCompany')
            ->with($company);

        $daemon = new CallExportDaemon($companySelector, $callExportGeneratorService, $companySaver);
        $daemon->handle($message);
    }
}
