<?php

declare(strict_types=1);

namespace tests\Unit\module\STCall\Service\Export;

use Aws\Sdk;
use PHPUnit\Framework\MockObject\Exception;
use STCall\Service\Export\AwsSdkFactory;
use STCall\Service\Interfaces\ConfigurationInterface;
use tests\TestCase;

final class AwsSdkFactoryTest extends TestCase
{
    /**
     * @throws Exception
     */
    public function testCreate(): void
    {
        $region = $this->faker->word();
        $awsConfig = [
            'api' => ['some key' => 'some value']
        ];

        $configuration = $this->createMock(ConfigurationInterface::class);
        $configuration
            ->method('get')
            ->with('aws')
            ->willReturn($awsConfig);

        $expectedAwsSdk = new Sdk(array_merge($awsConfig['api'], ['region' => $region]));

        $factory = new AwsSdkFactory($configuration);
        $awsSdk = $factory->create($region);

        $this->assertEquals($expectedAwsSdk, $awsSdk);
    }

    /**
     * @throws Exception
     */
    public function testCreateWithoutRegion(): void
    {
        $awsConfig = [
            'api' => ['some key' => 'some value']
        ];

        $configuration = $this->createMock(ConfigurationInterface::class);
        $configuration
            ->method('get')
            ->with('aws')
            ->willReturn($awsConfig);

        $expectedAwsSdk = new Sdk(array_merge($awsConfig['api'], ['region' => 'eu-central-1']));

        $factory = new AwsSdkFactory($configuration);
        $awsSdk = $factory->create();

        $this->assertEquals($expectedAwsSdk, $awsSdk);
    }
}
