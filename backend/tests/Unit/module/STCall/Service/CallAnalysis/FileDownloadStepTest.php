<?php

namespace tests\Unit\module\STCall\Service\CallAnalysis;

use Exception;
use GuzzleHttp\Exception\GuzzleException;
use PHPUnit\Framework\MockObject\Exception as PHPUnitException;
use ST<PERSON>pi\Entity\Exception\NoAccessToFileApiException;
use STCall\Data\CallsTable;
use ST<PERSON>all\Entity\Call;
use STCall\Entity\CallFactory;
use STCall\Service\CallAnalysis\Downloading\FileDownloader;
use STCall\Service\CallAnalysis\FileDownloadStep;
use STCall\Service\Import\CallSaving\Result\Result;
use STCall\Service\Import\UploadParams\UploadParams;
use STCall\Service\Import\UploadService;
use STCompany\Entity\Company;
use STCompany\Service\CompanyService;
use STLib\Mvc\Hydrator\Hydrator;
use STRoboTruck\Service\DataCollection\DataCollector;
use tests\TestCase;

class FileDownloadStepTest extends TestCase
{
    /**
     * @return void
     * @throws GuzzleException
     * @throws NoAccessToFileApiException
     * @throws PHPUnitException
     */
    public function testRun(): void
    {
        $roboTruckEventName = 'call_upload_file_download_step_success';

        $callsTable = $this->createMock(CallsTable::class);
        $callFactory = $this->createMock(CallFactory::class);
        $fileDownloader = $this->createMock(FileDownloader::class);

        $companyId = $this->faker->numberBetween(1, 1000);
        $requestId = $this->faker->word();
        $company = $this->createMock(Company::class);

        $companyService = $this->createMock(CompanyService::class);
        $companyService
            ->method('getCompany')
            ->with($companyId)
            ->willReturn($company);

        $recordingFile = $this->faker->url();

        $hydrateData = [
            'driver_name' => 'api-upload',
            'content' => '',
            'sourced_content' => '',
            'company' => $company,
            'user' => null,
            'options' => [
                'recording_file' => $recordingFile
            ],
        ];
        $uploadParams = $this->createMock(UploadParams::class);

        $hydrator = $this->createMock(Hydrator::class);
        $hydrator
            ->method('hydrateClass')
            ->with($hydrateData, UploadParams::class)
            ->willReturn($uploadParams);

        $callData = ['some_call_key' => 'some_call_value'];
        $call = $this->createMock(Call::class);
        $call
            ->expects(self::once())
            ->method('toArray')
            ->willReturn($callData);

        $callUploadResult = $this->createMock(Result::class);
        $callUploadResult
            ->method('getCall')
            ->willReturn($call);

        $uploadService = $this->createMock(UploadService::class);
        $uploadService
            ->method('uploadCall')
            ->with($uploadParams)
            ->willReturn($callUploadResult);

        $roboTruckEventMessage = json_encode($callData);
        $roboTruckEventExtra = [
            'id' => $requestId,
            'company_id' => $companyId,
            'call_id' => null,
        ];

        $oldLogName = 'api-calls-logs';
        $dataCollector = $this->createMock(DataCollector::class);
        $dataCollector
            ->expects($this->once())
            ->method('collect')
            ->with($roboTruckEventName, $roboTruckEventMessage, $roboTruckEventExtra, $oldLogName);

        $step = new FileDownloadStep(
            $callsTable,
            $callFactory,
            $fileDownloader,
            $hydrator,
            $dataCollector,
            $uploadService,
            $companyService
        );
        $step->applyOptions([
            'params' => ['recording_file' => $recordingFile],
            'request_id' => $requestId
        ]);

        $step->run($companyId);
    }

    /**
     * @throws NoAccessToFileApiException
     * @throws \PHPUnit\Framework\MockObject\Exception
     * @throws GuzzleException
     */
    public function testRunWhenError(): void
    {
        $roboTruckEventName = 'call_upload_file_download_step_error';

        $callsTable = $this->createMock(CallsTable::class);
        $callFactory = $this->createMock(CallFactory::class);
        $fileDownloader = $this->createMock(FileDownloader::class);
        $uploadService = $this->createMock(UploadService::class);

        $companyId = $this->faker->numberBetween(1, 1000);
        $requestId = $this->faker->word();
        $company = $this->createMock(Company::class);
        $company
            ->method('getId')
            ->willReturn($companyId);

        $companyService = $this->createMock(CompanyService::class);
        $companyService
            ->method('getCompany')
            ->with($companyId)
            ->willReturn($company);

        $error = 'Some error';
        $hydrator = $this->createMock(Hydrator::class);
        $hydrator
            ->expects($this->once())
            ->method('hydrateClass')
            ->willThrowException(new Exception($error));

        $errorMessage = json_encode(['code' => 0, 'error' => $error]);
        $roboTruckEventMessage = $errorMessage;
        $roboTruckEventExtra = [
            'id' => $requestId,
            'company_id' => $companyId,
            'call_id' => null,
        ];

        $oldLogName = 'api-calls-logs';
        $dataCollector = $this->createMock(DataCollector::class);
        $dataCollector
            ->expects($this->once())
            ->method('collect')
            ->with($roboTruckEventName, $roboTruckEventMessage, $roboTruckEventExtra, $oldLogName);

        $step = new FileDownloadStep(
            $callsTable,
            $callFactory,
            $fileDownloader,
            $hydrator,
            $dataCollector,
            $uploadService,
            $companyService
        );
        $step->applyOptions([
            'params' => ['recording_file' => $this->faker->url()],
            'request_id' => $requestId
        ]);

        $this->expectException(Exception::class);
        $this->expectExceptionMessage($error);

        $step->run($companyId);
    }
}
