<?php

declare(strict_types=1);

namespace tests\Unit\module\STOnboarding\Service;

use PHPUnit\Framework\MockObject\Exception;
use STIndustry\Entity\LlmEvent\LlmEvent;
use STIndustry\Entity\LlmEvent\LlmEventCollection;
use STOnboarding\Service\Interfaces\ConfigurationInterface;
use STOnboarding\Service\Interfaces\EventsAlgoEventsSelectorInterface;
use STOnboarding\Service\Interfaces\IndustryLlmEventsSelectorInterface;
use STOnboarding\Service\OnboardingIndustryLlmEventsSelector;
use tests\TestCase;

class OnboardingIndustryLlmEventsSelectorTest extends TestCase
{
    /**
     * @throws Exception
     */
    public function testGetMostPopularLlmEventsWhenPopularEventsLessThanLimit()
    {
        $industryId = $this->faker->numberBetween(1, 100);

        $llmEventName1 = $this->faker->word();
        $llmEventName2 = strtoupper($this->faker->text(10));
        $llmEventName3 = ' ' . strtolower($this->faker->sentence(3)) . ' ';

        $llmEvent1 = $this->createMock(LlmEvent::class);
        $llmEvent1->method('getName')->willReturn($llmEventName1);
        $llmEvent1->method('toArray')->willReturn(['name' => $llmEventName1]);

        $llmEvent2 = $this->createMock(LlmEvent::class);
        $llmEvent2->method('getName')->willReturn($llmEventName2);
        $llmEvent2->method('toArray')->willReturn(['name' => $llmEventName2]);

        $llmEvent3 = $this->createMock(LlmEvent::class);
        $llmEvent3->method('getName')->willReturn($llmEventName3);
        $llmEvent3->method('toArray')->willReturn(['name' => $llmEventName3]);

        $llmEventCollection = new LlmEventCollection([$llmEvent1, $llmEvent2, $llmEvent3]);

        $industryLlmEventsSelector = $this->createMock(IndustryLlmEventsSelectorInterface::class);
        $industryLlmEventsSelector
            ->method('getLlmEvents')
            ->with($industryId)
            ->willReturn($llmEventCollection);

        $popularLlmEventsLimit = 3;
        $onboardingConfig = [
            'popular-events-limit' => $popularLlmEventsLimit
        ];

        $configuration = $this->createMock(ConfigurationInterface::class);
        $configuration
            ->method('get')
            ->with('onboarding')
            ->willReturn($onboardingConfig);

        $eventsAlgoEventsSelector = $this->createMock(EventsAlgoEventsSelectorInterface::class);
        $eventsAlgoEventsSelector
            ->method('filterMostPopularAlgoEvents')
            ->with([
                trim(strtolower($llmEventName1)),
                trim(strtolower($llmEventName2)),
                trim(strtolower($llmEventName3))
            ], $popularLlmEventsLimit)
            ->willReturn([' ' . strtolower($llmEventName2), strtoupper($llmEventName3) . ' ']);

        $selector = new OnboardingIndustryLlmEventsSelector(
            $industryLlmEventsSelector,
            $eventsAlgoEventsSelector,
            $configuration
        );

        $popularLlmEventsCollection = $selector->getPopularLlmEvents($industryId);

        $this->assertSame($popularLlmEventsLimit, $popularLlmEventsCollection->count());

        $this->assertSame($llmEvent2, $popularLlmEventsCollection[0]);
        $this->assertSame($llmEvent3, $popularLlmEventsCollection[1]);
        $this->assertSame($llmEvent1, $popularLlmEventsCollection[2]);
    }

    /**
     * @throws Exception
     */
    public function testGetMostPopularLlmEventsWhenLlmEventsLessThanLimit()
    {
        $industryId = $this->faker->numberBetween(1, 100);

        $llmEventName1 = $this->faker->word();
        $llmEventName2 = strtolower($this->faker->text(10));

        $llmEvent1 = $this->createMock(LlmEvent::class);
        $llmEvent1->method('getName')->willReturn($llmEventName1);
        $llmEvent1->method('toArray')->willReturn(['name' => $llmEventName1]);

        $llmEvent2 = $this->createMock(LlmEvent::class);
        $llmEvent2->method('getName')->willReturn($llmEventName2);
        $llmEvent2->method('toArray')->willReturn(['name' => $llmEventName2]);

        $llmEventCollection = new LlmEventCollection([$llmEvent1, $llmEvent2]);

        $industryLlmEventsSelector = $this->createMock(IndustryLlmEventsSelectorInterface::class);
        $industryLlmEventsSelector
            ->method('getLlmEvents')
            ->with($industryId)
            ->willReturn($llmEventCollection);

        $popularLlmEventsLimit = 3;
        $onboardingConfig = [
            'popular-events-limit' => $popularLlmEventsLimit
        ];

        $configuration = $this->createMock(ConfigurationInterface::class);
        $configuration
            ->method('get')
            ->with('onboarding')
            ->willReturn($onboardingConfig);

        $eventsAlgoEventsSelector = $this->createMock(EventsAlgoEventsSelectorInterface::class);
        $eventsAlgoEventsSelector
            ->method('filterMostPopularAlgoEvents')
            ->with([$llmEventName1, $llmEventName2], $popularLlmEventsLimit)
            ->willReturn([$llmEventName2, $llmEventName1]);

        $selector = new OnboardingIndustryLlmEventsSelector(
            $industryLlmEventsSelector,
            $eventsAlgoEventsSelector,
            $configuration
        );

        $popularLlmEventsCollection = $selector->getPopularLlmEvents($industryId);

        $this->assertSame(2, $popularLlmEventsCollection->count());

        $this->assertSame($llmEvent2, $popularLlmEventsCollection[0]);
        $this->assertSame($llmEvent1, $popularLlmEventsCollection[1]);
    }
}
