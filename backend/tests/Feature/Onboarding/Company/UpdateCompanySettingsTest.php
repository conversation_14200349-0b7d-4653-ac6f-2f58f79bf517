<?php

declare(strict_types=1);

namespace tests\Feature\Onboarding\Company;

use Exception;
use PHPUnit\Framework\MockObject\Exception as PHPUnitException;
use STApi\Entity\Exception\NotFoundApiException;
use STOnboarding\Data\OnboardingFormsTable;
use STOnboarding\Entity\OnboardingForm;
use STOnboarding\Service\Notification\OnboardingFormChecker;
use tests\Feature\AppTokenTestCase;

final class UpdateCompanySettingsTest extends AppTokenTestCase
{
    /**
     * @return void
     * @throws PHPUnitException
     * @throws Exception
     */
    public function testUpdateSettings(): void
    {
        $companyName = $this->faker->text(30);

        $externalId = $this->faker->uuid();
        $frontFormLink = $this->faker->url();
        $inviteLink = $this->faker->url();
        $logoBase64 = $this->faker->text(1000);

        $onboardingFormToSave = $this->createMock(OnboardingForm::class);
        $onboardingFormToSave
            ->expects($this->once())
            ->method('setCompanyName')
            ->with($companyName);
        $onboardingFormToSave
            ->expects($this->once())
            ->method('setCompanyLogo')
            ->with($logoBase64);

        $savedOnboardingForm = new OnboardingForm();
        $savedOnboardingForm->setExternalId($externalId);
        $savedOnboardingForm->setFrontFormLink($frontFormLink);
        $savedOnboardingForm->setInviteLink($inviteLink);
        $savedOnboardingForm->setCompanyName($companyName);
        $savedOnboardingForm->setCompanyLogo($logoBase64);

        $onboardingFormsTable = $this->createMock(OnboardingFormsTable::class);
        $onboardingFormsTable
            ->method('getFormByExternalId')
            ->with($externalId)
            ->willReturnOnConsecutiveCalls(
                $onboardingFormToSave,
                $onboardingFormToSave,
                $onboardingFormToSave,
                $savedOnboardingForm
            );

        $onboardingFormsTable->expects($this->once())
            ->method('save')
            ->with($onboardingFormToSave);

        $this->serviceManager->setService(OnboardingFormsTable::class, $onboardingFormsTable);

        $this->getRequest()->setContent(json_encode([
            'company_name' => $companyName,
            'company_logo' => $logoBase64,
        ]));

        $this->dispatchApi('onboarding/form/' . $externalId, 'PUT');

        $response = json_decode($this->getResponse()->getContent(), true);

        $this->assertSame($externalId, $response['result']['form']['id']);
        $this->assertSame($companyName, $response['result']['form']['company_name']);
        $this->assertSame($logoBase64, $response['result']['form']['company_logo']);

        $this->assertResponseStatusCode(200);
    }

    /**
     * @throws PHPUnitException
     * @throws Exception
     */
    public function testUpdateSettingsWhenWrongFormId(): void
    {
        $externalId = $this->faker->uuid();
        $onboardingFormsTable = $this->createMock(OnboardingFormsTable::class);
        $onboardingFormsTable
            ->method('getFormByExternalId')
            ->with($externalId)
            ->willThrowException(new NotFoundApiException());
        $this->serviceManager->setService(OnboardingFormsTable::class, $onboardingFormsTable);

        $this->dispatchApi('onboarding/form/' . $externalId, 'PUT');

        $expectedError = '"id":["The onboarding form does not exists."]';

        $this->assertStringContainsString($expectedError, $this->getResponse()->getContent());
        $this->assertResponseStatusCode(422);
    }

    /**
     * @throws PHPUnitException
     * @throws Exception
     */
    public function testUpdateSettingsWhenDisableForEditForm(): void
    {
        $externalId = $this->faker->uuid();

        $onboardingForm = $this->createMock(OnboardingForm::class);
        $onboardingForm->method('getIsSubmitted')->willReturn(true);

        $onboardingFormsTable = $this->createMock(OnboardingFormsTable::class);
        $onboardingFormsTable
            ->method('getFormByExternalId')
            ->with($externalId)
            ->willReturn($onboardingForm);
        $this->serviceManager->setService(OnboardingFormsTable::class, $onboardingFormsTable);

        $this->dispatchApi('onboarding/form/' . $externalId, 'PUT');

        $expectedError = '"id":["The completed onboarding form is already submitted and disable for edit."]';

        $this->assertStringContainsString($expectedError, $this->getResponse()->getContent());
        $this->assertResponseStatusCode(422);
    }

    /**
     * @throws PHPUnitException
     * @throws Exception
     */
    public function testUpdateSettingsWhenEmptyLogo(): void
    {
        $companyName = $this->faker->text(30);

        $externalId = $this->faker->uuid();
        $frontFormLink = $this->faker->url();
        $inviteLink = $this->faker->url();

        $onboardingFormToSave = $this->createMock(OnboardingForm::class);
        $onboardingFormToSave
            ->expects($this->once())
            ->method('setCompanyName')
            ->with($companyName);
        $onboardingFormToSave
            ->expects($this->never())
            ->method('setCompanyLogo');

        $oldLogo = 'some old logo';
        $savedOnboardingForm = new OnboardingForm();
        $savedOnboardingForm->setExternalId($externalId);
        $savedOnboardingForm->setFrontFormLink($frontFormLink);
        $savedOnboardingForm->setInviteLink($inviteLink);
        $savedOnboardingForm->setCompanyName($companyName);
        $savedOnboardingForm->setCompanyLogo($oldLogo);

        $onboardingFormsTable = $this->createMock(OnboardingFormsTable::class);
        $onboardingFormsTable
            ->method('getFormByExternalId')
            ->with($externalId)
            ->willReturnOnConsecutiveCalls(
                $onboardingFormToSave,
                $onboardingFormToSave,
                $onboardingFormToSave,
                $savedOnboardingForm
            );

        $onboardingFormsTable->expects($this->once())
            ->method('save')
            ->with($onboardingFormToSave);

        $this->serviceManager->setService(OnboardingFormsTable::class, $onboardingFormsTable);

        $this->getRequest()->setContent(json_encode([
            'company_name' => $companyName,
            'company_logo' => $this->faker->randomElement(['', null]),
        ]));

        $this->dispatchApi('onboarding/form/' . $externalId, 'PUT');

        $response = json_decode($this->getResponse()->getContent(), true);

        $this->assertSame($externalId, $response['result']['form']['id']);
        $this->assertSame($companyName, $response['result']['form']['company_name']);
        $this->assertSame($oldLogo, $response['result']['form']['company_logo']);

        $this->assertResponseStatusCode(200);
    }

    /**
     * @dataProvider wrongCompanyNamesDataProvider
     * @param string|null $companyName
     * @return void
     * @throws PHPUnitException
     */
    public function testUpdateSettingsWhenWrongCompanyName(?string $companyName): void
    {
        $externalId = $this->faker->uuid();
        $onboardingFormsTable = $this->createMock(OnboardingFormsTable::class);

        $onboardingFormsTable->expects($this->never())
            ->method('save');

        $this->serviceManager->setService(OnboardingFormsTable::class, $onboardingFormsTable);

        $this->getRequest()->setContent(json_encode(['company_name' => $companyName]));

        $this->dispatchApi('onboarding/form/' . $externalId, 'PUT');

        $expectedError = '"company_name":["Company name cannot be empty and must be less than 255 symbols."]';

        $this->assertStringContainsString($expectedError, $this->getResponse()->getContent());

        $this->assertResponseStatusCode(422);
    }

    public static function wrongCompanyNamesDataProvider(): array
    {
        return [
            [
                null
            ],
            [
                ''
            ],
            [
                str_repeat('a', 256),
            ]
        ];
    }

    /**
     * @throws PHPUnitException
     * @throws Exception
     */
    public function testUpdateSettingsWhenWrongCompanyLogo(): void
    {
        $externalId = $this->faker->uuid();
        $onboardingFormsTable = $this->createMock(OnboardingFormsTable::class);

        $onboardingFormsTable->expects($this->never())
            ->method('save');

        $this->serviceManager->setService(OnboardingFormsTable::class, $onboardingFormsTable);

        $this->getRequest()->setContent(json_encode([
            'company_name' => 'some name',
            'company_logo' => str_repeat('a', (50 * 1024) + 1),
        ]));

        $this->dispatchApi('onboarding/form/' . $externalId, 'PUT');

        $expectedError = '"company_logo":["Logo must be less than 50 kb."]';

        $this->assertStringContainsString($expectedError, $this->getResponse()->getContent());

        $this->assertResponseStatusCode(422);
    }
}
