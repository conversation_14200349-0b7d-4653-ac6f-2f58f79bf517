<?php

declare(strict_types=1);

namespace tests\Feature\Companies\Checklists\ChecklistPoints;

use Exception;
use PHPUnit\Framework\MockObject\Exception as PHPUnitException;
use STApi\Entity\Exception\NotFoundApiException;
use STCompany\Data\ChecklistsPointsTable;
use STCompany\Data\ChecklistsTable;
use STCompany\Data\PermissionsTable;
use STCompany\Entity\Checklist\ChecklistPoint;
use STCompany\Entity\Checklist\ChecklistPointCollection;
use STCompany\Entity\Role;
use tests\Feature\AuthTestCase;

final class ChangeOrderTest extends AuthTestCase
{
    /**
     * @throws PHPUnitException
     * @throws Exception
     */
    public function testChangeOrder(): void
    {
        $checklistId = $this->faker->numberBetween(1, 100);

        $checklistPointId1 = $this->faker->numberBetween(101, 200);
        $checklistPointId2 = $this->faker->numberBetween(201, 300);
        $checklistPointId3 = $this->faker->numberBetween(301, 400);

        $orderedIds = [$checklistPointId2, $checklistPointId1, $checklistPointId3];

        $checklistsTable = $this->createMock(ChecklistsTable::class);
        $this->serviceManager->setService(ChecklistsTable::class, $checklistsTable);

        $checklistPoint1 = $this->createMock(ChecklistPoint::class);
        $checklistPoint1->method('toArray')->willReturn(['id' => $checklistPointId1]);
        $checklistPoint2 = $this->createMock(ChecklistPoint::class);
        $checklistPoint2->method('toArray')->willReturn(['id' => $checklistPointId2]);
        $checklistPoint3 = $this->createMock(ChecklistPoint::class);
        $checklistPoint3->method('toArray')->willReturn(['id' => $checklistPointId3]);

        $checklistPointCollection = new ChecklistPointCollection([$checklistPoint1, $checklistPoint2, $checklistPoint3]);

        $checklistsPointsTable = $this->createMock(ChecklistsPointsTable::class);
        $checklistsPointsTable
            ->method('getChecklistPointsByChecklistId')
            ->with($checklistId, $this->companyId)
            ->willReturn($checklistPointCollection);
        $checklistsPointsTable
            ->expects($this->once())
            ->method('updateOrder')
            ->with($orderedIds);
        $this->serviceManager->setService(ChecklistsPointsTable::class, $checklistsPointsTable);

        $this->getRequest()->setContent(json_encode(['order' => $orderedIds]));

        $this->loginAs(Role::MANAGER_ROLE_TYPE);
        $this->addPermissionsToCompanyManager(
            PermissionsTable::FLOWS,
            PermissionsTable::WRITE_PERMISSION
        );

        $this->dispatchApi('checklist/' . $checklistId . '/checklist-points/change-order', 'POST');

        $response = json_decode($this->getResponse()->getContent(), true);

        $this->assertSame($orderedIds, $response['result']['order']);

        $this->assertResponseStatusCode(200);
    }

    /**
     * @throws PHPUnitException
     * @throws Exception
     */
    public function testChangeOrderWhenChecklistOfAnotherCompany(): void
    {
        $checklistId = $this->faker->numberBetween(1, 100);

        $checklistsTable = $this->createMock(ChecklistsTable::class);
        $checklistsTable
            ->expects($this->once())
            ->method('getChecklist')
            ->with($checklistId, $this->companyId)
            ->willThrowException(new NotFoundApiException('Checklist not found'));
        $this->serviceManager->setService(ChecklistsTable::class, $checklistsTable);

        $checklistsPointsTable = $this->createMock(ChecklistsPointsTable::class);
        $this->serviceManager->setService(ChecklistsPointsTable::class, $checklistsPointsTable);

        $this->loginAs(Role::ADMIN_ROLE_TYPE);
        $this->dispatchApi('checklist/' . $checklistId . '/checklist-points/change-order', 'POST');

        $response = json_decode($this->getResponse()->getContent(), true);

        $this->assertSame('Checklist not found', $response['error']['messages']);

        $this->assertResponseStatusCode(404);
    }

    /**
     * @throws PHPUnitException
     * @throws Exception
     */
    public function testChangeOrderWhenWrongChecklistPointId(): void
    {
        $checklistId = $this->faker->numberBetween(1, 100);
        $checklistPointId1 = $this->faker->numberBetween(101, 200);
        $checklistPointId2 = $this->faker->numberBetween(201, 300);

        $orderedIds = [$checklistPointId2, $checklistPointId1];

        $checklistsTable = $this->createMock(ChecklistsTable::class);
        $this->serviceManager->setService(ChecklistsTable::class, $checklistsTable);

        $checklistPoint1 = $this->createMock(ChecklistPoint::class);
        $checklistPoint1->method('toArray')->willReturn(['id' => $checklistPointId1]);

        $checklistPoint2 = $this->createMock(ChecklistPoint::class);
        $checklistPoint2->method('toArray')->willReturn(['id' => $this->faker->numberBetween(301, 400)]);

        $checklistPointCollection = new ChecklistPointCollection([$checklistPoint1, $checklistPoint2]);

        $checklistsPointsTable = $this->createMock(ChecklistsPointsTable::class);
        $checklistsPointsTable
            ->method('getChecklistPointsByChecklistId')
            ->with($checklistId, $this->companyId)
            ->willReturn($checklistPointCollection);
        $this->serviceManager->setService(ChecklistsPointsTable::class, $checklistsPointsTable);

        $this->getRequest()->setContent(json_encode(['order' => $orderedIds]));

        $this->loginAs(Role::ADMIN_ROLE_TYPE);
        $this->dispatchApi('checklist/' . $checklistId . '/checklist-points/change-order', 'POST');

        $response = json_decode($this->getResponse()->getContent(), true);

        $this->assertSame('Wrong set of checklist points', $response['error']['messages']['order'][0]);

        $this->assertResponseStatusCode(422);
    }

    /**
     * @dataProvider adminRoleTypesDataProvider
     * @param int $adminRoleType
     * @return void
     * @throws PHPUnitException
     * @throws Exception
     */
    public function testChangeOrderByAdmin(int $adminRoleType): void
    {
        $checklistId = $this->faker->numberBetween(1, 100);

        $checklistPointId1 = $this->faker->numberBetween(101, 200);
        $checklistPointId2 = $this->faker->numberBetween(201, 300);

        $orderedIds = [$checklistPointId2, $checklistPointId1];

        $checklistsTable = $this->createMock(ChecklistsTable::class);
        $this->serviceManager->setService(ChecklistsTable::class, $checklistsTable);

        $checklistPoint1 = $this->createMock(ChecklistPoint::class);
        $checklistPoint1->method('toArray')->willReturn(['id' => $checklistPointId1]);
        $checklistPoint2 = $this->createMock(ChecklistPoint::class);
        $checklistPoint2->method('toArray')->willReturn(['id' => $checklistPointId2]);

        $checklistPointCollection = new ChecklistPointCollection([$checklistPoint1, $checklistPoint2]);

        $checklistsPointsTable = $this->createMock(ChecklistsPointsTable::class);
        $checklistsPointsTable
            ->method('getChecklistPointsByChecklistId')
            ->with($checklistId, $this->companyId)
            ->willReturn($checklistPointCollection);
        $this->serviceManager->setService(ChecklistsPointsTable::class, $checklistsPointsTable);

        $this->getRequest()->setContent(json_encode(['order' => $orderedIds]));

        $this->loginAs($adminRoleType);
        $this->dispatchApi('checklist/' . $checklistId . '/checklist-points/change-order', 'POST');

        $this->assertResponseStatusCode(200);
    }

    public static function adminRoleTypesDataProvider(): array
    {
        return [
            [1], // Role::ADMIN_ROLE_TYPE
            [4], // Role::COMPANY_ADMIN_ROLE_TYPE
        ];
    }

    /**
     * @throws PHPUnitException
     * @throws Exception
     */
    public function testChangeOrderWhenReadPermission(): void
    {
        $checklistId = $this->faker->numberBetween(1, 100);

        $checklistsPointsTable = $this->createMock(ChecklistsPointsTable::class);
        $this->serviceManager->setService(ChecklistsPointsTable::class, $checklistsPointsTable);

        $this->loginAs(Role::MANAGER_ROLE_TYPE);
        $this->addPermissionsToCompanyManager(
            PermissionsTable::FLOWS,
            PermissionsTable::READ_PERMISSION
        );

        $this->dispatchApi('checklist/' . $checklistId . '/checklist-points/change-order', 'POST');

        $response = json_decode($this->getResponse()->getContent(), true);

        $this->assertSame(
            'User has required permission, but doesn\'t have adequate access level',
            $response['error']['messages']
        );

        $this->assertResponseStatusCode(403);
    }

    /**
     * @throws PHPUnitException
     * @throws Exception
     */
    public function testChangeOrderWhenNoPermission(): void
    {
        $checklistId = $this->faker->numberBetween(1, 100);

        $checklistsPointsTable = $this->createMock(ChecklistsPointsTable::class);
        $this->serviceManager->setService(ChecklistsPointsTable::class, $checklistsPointsTable);

        $this->loginAs(Role::MANAGER_ROLE_TYPE);
        $this->dispatchApi('checklist/' . $checklistId . '/checklist-points/change-order', 'POST');

        $response = json_decode($this->getResponse()->getContent(), true);

        $this->assertSame(
            'User doesn\'t have required permission',
            $response['error']['messages']
        );

        $this->assertResponseStatusCode(403);
    }
}
