<?php

declare(strict_types=1);

namespace tests\Feature\Companies\Checklists;

use Exception;
use PHPUnit\Framework\MockObject\Exception as PHPUnitException;
use STCompany\Data\ChecklistsTable;
use STCompany\Data\PermissionsTable;
use STC<PERSON>pany\Entity\Checklist\Checklist;
use STCompany\Entity\Checklist\ChecklistCollection;
use STCompany\Entity\Role;
use tests\Feature\AuthTestCase;

final class CreateChecklistTest extends AuthTestCase
{
    /**
     * @return void
     * @throws PHPUnitException
     * @throws Exception
     */
    public function testCreateFirst(): void
    {
        $checklistName = $this->faker->word();
        $callDurationThreshold = $this->faker->numberBetween(1, 500);

        $checklistCollection = new ChecklistCollection();

        $checklistsTable = $this->createMock(ChecklistsTable::class);
        $checklistsTable
            ->method('getCompanyChecklists')
            ->with($this->companyId)
            ->willReturn($checklistCollection);
        $checklistsTable
            ->expects($this->once())
            ->method('saveChecklist')
            ->with(
                self::callback(
                    function (Checklist $checklist) use ($checklistName, $callDurationThreshold) {
                        return $checklist->getName() === $checklistName
                            && $checklist->getCallDurationThreshold() === $callDurationThreshold
                            && $checklist->getCompanyId() === $this->companyId;
                    }
                ),
            );
        $this->serviceManager->setService(ChecklistsTable::class, $checklistsTable);

        $this->getRequest()->setContent(json_encode([
            'checklist_name' => $checklistName,
            'call_duration_threshold' => $callDurationThreshold
        ]));

        $this->loginAs(Role::MANAGER_ROLE_TYPE);
        $this->addPermissionsToCompanyManager(
            PermissionsTable::FLOWS,
            PermissionsTable::WRITE_PERMISSION
        );

        $this->dispatchApi('checklist', 'POST');

        $response = json_decode($this->getResponse()->getContent(), true);

        $this->assertSame($this->companyId, $response['result']['checklist']['company_id']);
        $this->assertSame($checklistName, $response['result']['checklist']['name']);
        $this->assertSame($callDurationThreshold, $response['result']['checklist']['call_duration_threshold']);
        $this->assertResponseStatusCode(200);
    }

    /**
     * @dataProvider adminRoleTypesDataProvider
     * @param int $adminRoleType
     * @return void
     * @throws PHPUnitException
     * @throws Exception
     */
    public function testCreateByAdmin(int $adminRoleType): void
    {
        $checklistName = $this->faker->word();
        $callDurationThreshold = $this->faker->numberBetween(1, 500);

        $checklistCollection = new ChecklistCollection();

        $checklistsTable = $this->createMock(ChecklistsTable::class);
        $checklistsTable
            ->method('getCompanyChecklists')
            ->with($this->companyId)
            ->willReturn($checklistCollection);
        $this->serviceManager->setService(ChecklistsTable::class, $checklistsTable);

        $this->getRequest()->setContent(json_encode([
            'checklist_name' => $checklistName,
            'call_duration_threshold' => $callDurationThreshold
        ]));

        $this->loginAs($adminRoleType);
        $this->dispatchApi('checklist', 'POST');

        $this->assertResponseStatusCode(200);
    }

    public static function adminRoleTypesDataProvider(): array
    {
        return [
            [1], // Role::ADMIN_ROLE_TYPE
            [4], // Role::COMPANY_ADMIN_ROLE_TYPE
        ];
    }

    /**
     * @throws PHPUnitException
     * @throws Exception
     */
    public function testCreateSecondByNonGlobalAdmin(): void
    {
        $checklistCollection = new ChecklistCollection();
        $checklistCollection->add($this->createMock(Checklist::class));

        $checklistsTable = $this->createMock(ChecklistsTable::class);
        $checklistsTable
            ->method('getCompanyChecklists')
            ->with($this->companyId)
            ->willReturn($checklistCollection);
        $checklistsTable
            ->expects($this->never())
            ->method('saveChecklist');
        $this->serviceManager->setService(ChecklistsTable::class, $checklistsTable);

        $this->loginAs(Role::COMPANY_ADMIN_ROLE_TYPE);
        $this->dispatchApi('checklist', 'POST');

        $response = json_decode($this->getResponse()->getContent(), true);

        $this->assertSame(
            'You can\'t create more than one Flow yourself. If you need it, please contact support.',
            current($response['error']['messages']['checklist'])
        );

        $this->assertResponseStatusCode(422);
    }

    /**
     * @throws PHPUnitException
     * @throws Exception
     */
    public function testCreateSecondByGlobalAdmin(): void
    {
        $checklistName = $this->faker->word();
        $callDurationThreshold = $this->faker->numberBetween(1, 500);

        $checklistCollection = new ChecklistCollection();
        $checklistCollection->add($this->createMock(Checklist::class));

        $checklistsTable = $this->createMock(ChecklistsTable::class);
        $checklistsTable
            ->method('getCompanyChecklists')
            ->with($this->companyId)
            ->willReturn($checklistCollection);
        $this->serviceManager->setService(ChecklistsTable::class, $checklistsTable);

        $this->getRequest()->setContent(json_encode([
            'checklist_name' => $checklistName,
            'call_duration_threshold' => $callDurationThreshold
        ]));

        $this->loginAs(Role::ADMIN_ROLE_TYPE);
        $this->dispatchApi('checklist', 'POST');

        $this->assertResponseStatusCode(200);
    }

    /**
     * @throws PHPUnitException
     * @throws Exception
     */
    public function testCreateWhenReadPermission(): void
    {
        $this->loginAs(Role::MANAGER_ROLE_TYPE);
        $this->addPermissionsToCompanyManager(
            PermissionsTable::FLOWS,
            PermissionsTable::READ_PERMISSION
        );

        $this->dispatchApi('checklist', 'POST');

        $response = json_decode($this->getResponse()->getContent(), true);

        $this->assertSame(
            'User has required permission, but doesn\'t have adequate access level',
            $response['error']['messages']
        );

        $this->assertResponseStatusCode(403);
    }

    /**
     * @throws PHPUnitException
     * @throws Exception
     */
    public function testCreateWhenNoPermissions(): void
    {
        $this->loginAs(Role::MANAGER_ROLE_TYPE);
        $this->dispatchApi('checklist', 'POST');

        $response = json_decode($this->getResponse()->getContent(), true);

        $this->assertSame(
            'User doesn\'t have required permission',
            $response['error']['messages']
        );

        $this->assertResponseStatusCode(403);
    }
}
