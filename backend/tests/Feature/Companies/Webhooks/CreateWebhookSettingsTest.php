<?php

declare(strict_types=1);

namespace tests\Feature\Companies\Webhooks;

use Exception;
use PHPUnit\Framework\MockObject\Exception as PHPUnitException;
use Psr\Container\ContainerExceptionInterface;
use STCall\Service\Webhooks\Interfaces\WebhookServiceInterface;
use STCompany\Data\CompaniesWebhooksSettingsTable;
use STCompany\Data\PermissionsTable;
use STCompany\Entity\Role;
use STCompany\Service\Webhooks\WebhookSettingsSelector;
use tests\Feature\AuthTestCase;

final class CreateWebhookSettingsTest extends AuthTestCase
{
    /**
     * @return void
     * @throws PHPUnitException
     * @throws Exception
     * @throws ContainerExceptionInterface
     */
    public function testCreate(): void
    {
        $webhookType = $this->faker->randomElement(WebhookServiceInterface::TYPES);
        $isEnabled = $this->faker->boolean();
        $url = $this->faker->url();
        $headers = [$this->faker->word() => $this->faker->word()];
        $newId = $this->faker->numberBetween(1, 100);

        $webhookSettingsData = [
            'company_webhook_setting_id' => $newId,
            'type' => $webhookType,
            'is_enabled' => (int) $isEnabled,
            'url' => $url,
            'headers' => json_encode($headers),
            'company_id' => $this->companyId,
            'some_another_field' => 'some value',
        ];
        $webhookSettingsTable = $this->createMock(CompaniesWebhooksSettingsTable::class);
        $webhookSettingsTable
            ->expects($this->once())
            ->method('getWebhookSettingsDataByType')
            ->with($webhookType, $this->companyId)
            ->willReturn(null);
        $webhookSettingsTable
            ->expects($this->once())
            ->method('createWebhookSettings')
            ->with($webhookType, $url, $headers, $isEnabled, $this->companyId)
            ->willReturn($newId);

        $webhookSettingsTable
            ->expects($this->once())
            ->method('getWebhookSettingsData')
            ->with($newId, $this->companyId)
            ->willReturn($webhookSettingsData);
        $this->serviceManager->setService(CompaniesWebhooksSettingsTable::class, $webhookSettingsTable);

        $this->serviceManager->setService(
            WebhookSettingsSelector::class,
            $this->serviceManager->build(WebhookSettingsSelector::class)
        );

        $this->getRequest()->setContent(json_encode([
            'type' => $webhookType,
            'is_enabled' => $isEnabled,
            'url' => $url,
            'headers' => $headers,
        ]));

        $this->loginAs(Role::MANAGER_ROLE_TYPE);
        $this->addPermissionsToCompanyManager(
            PermissionsTable::WEBHOOKS,
            PermissionsTable::WRITE_PERMISSION
        );

        $this->dispatchApi('company/webhooks', 'POST');

        $response = json_decode($this->getResponse()->getContent(), true);

        $expectedData = [
            'id' => $newId,
            'type' => $webhookType,
            'is_enabled' => $isEnabled,
            'url' => $url,
            'headers' => $headers,
            'company_id' => $this->companyId
        ];

        $this->assertSame($expectedData, $response['result']['webhook']);

        $this->assertResponseStatusCode(200);
    }

    /**
     * @throws ContainerExceptionInterface
     * @throws PHPUnitException
     * @throws Exception
     */
    public function testCreateWhenWebhookAlreadyExists(): void
    {
        $webhookType = $this->faker->randomElement(WebhookServiceInterface::TYPES);
        $isEnabled = $this->faker->boolean();
        $url = $this->faker->url();
        $headers = [$this->faker->word() => $this->faker->word()];
        $newId = $this->faker->numberBetween(1, 100);

        $webhookSettingsData = [
            'id' => $newId,
            'type' => $webhookType,
            'is_enabled' => (int) $isEnabled,
            'url' => $url,
            'headers' => json_encode($headers),
            'company_id' => $this->companyId,
        ];
        $webhookSettingsTable = $this->createMock(CompaniesWebhooksSettingsTable::class);
        $webhookSettingsTable
            ->method('getWebhookSettingsDataByType')
            ->with($webhookType, $this->companyId)
            ->willReturn($webhookSettingsData);
        $this->serviceManager->setService(CompaniesWebhooksSettingsTable::class, $webhookSettingsTable);

        $this->serviceManager->setService(
            WebhookSettingsSelector::class,
            $this->serviceManager->build(WebhookSettingsSelector::class)
        );

        $this->getRequest()->setContent(json_encode([
            'type' => $webhookType,
            'is_enabled' => $isEnabled,
            'url' => $url,
            'headers' => $headers,
        ]));

        $this->loginAs(Role::ADMIN_ROLE_TYPE);
        $this->dispatchApi('company/webhooks', 'POST');

        $response = json_decode($this->getResponse()->getContent(), true);

        $this->assertSame('Webhook with that type already exists.', $response['error']['messages']['type'][0]);

        $this->assertResponseStatusCode(422);
    }

    /**
     * @dataProvider wrongDataDataProvider
     * @param string|null $webhookType
     * @param mixed $isEnabled
     * @param string|null $url
     * @param mixed $headers
     * @param string $error
     * @param string $fieldName
     * @return void
     * @throws ContainerExceptionInterface
     * @throws PHPUnitException
     */
    public function testCreateWhenWrongData(
        ?string $webhookType,
        mixed $isEnabled,
        ?string $url,
        mixed $headers,
        string $error,
        string $fieldName
    ): void {
        $webhookSettingsTable = $this->createMock(CompaniesWebhooksSettingsTable::class);
        $this->serviceManager->setService(CompaniesWebhooksSettingsTable::class, $webhookSettingsTable);

        $this->serviceManager->setService(
            WebhookSettingsSelector::class,
            $this->serviceManager->build(WebhookSettingsSelector::class)
        );

        $this->getRequest()->setContent(json_encode([
            'type' => $webhookType,
            'is_enabled' => $isEnabled,
            'url' => $url,
            'headers' => $headers,
        ]));

        $this->loginAs(Role::ADMIN_ROLE_TYPE);
        $this->dispatchApi('company/webhooks', 'POST');

        $response = json_decode($this->getResponse()->getContent(), true);

        $this->assertSame($error, $response['error']['messages'][$fieldName][0]);

        $this->assertResponseStatusCode(422);
    }

    public static function wrongDataDataProvider(): array
    {
        return [
            [
                'webhookType' => 'wrong type',
                'isEnabled' => true,
                'url' => 'some-url.com',
                'headers' => ['header' => 'value'],
                'error' => 'Wrong type.',
                'fieldName' => 'type'
            ],
            [
                'webhookType' => 'events',
                'isEnabled' => 'some wrong is enabled',
                'url' => 'some-url.com',
                'headers' => ['header' => 'value'],
                'error' => 'Wrong is enabled.',
                'fieldName' => 'is_enabled'
            ],
            [
                'webhookType' => 'summarization',
                'isEnabled' => true,
                'url' => null,
                'headers' => ['header' => 'value'],
                'error' => 'Url must be not empty and less than 1024 symbols',
                'fieldName' => 'url'
            ],
            [
                'webhookType' => 'summarization',
                'isEnabled' => true,
                'url' => str_repeat('a', 1025),
                'headers' => ['header' => 'value'],
                'error' => 'Url must be not empty and less than 1024 symbols',
                'fieldName' => 'url'
            ],
            [
                'webhookType' => 'summarization',
                'isEnabled' => true,
                'url' => 'some-url.com',
                'headers' => 'some wrong headers',
                'error' => 'Headers must be an array.',
                'fieldName' => 'headers'
            ],
        ];
    }

    /**
     * @throws PHPUnitException
     * @throws Exception
     */
    public function testCreateWhenReadPermissions(): void
    {
        $this->loginAs(Role::MANAGER_ROLE_TYPE);
        $this->addPermissionsToCompanyManager(
            PermissionsTable::WEBHOOKS,
            PermissionsTable::READ_PERMISSION
        );

        $this->dispatchApi('company/webhooks', 'POST');

        $response = json_decode($this->getResponse()->getContent(), true);

        $this->assertSame(
            'User has required permission, but doesn\'t have adequate access level',
            $response['error']['messages']
        );

        $this->assertResponseStatusCode(403);
    }

    /**
     * @throws PHPUnitException
     * @throws Exception
     */
    public function testCreateWhenNoPermissions(): void
    {
        $this->loginAs(Role::MANAGER_ROLE_TYPE);
        $this->dispatchApi('company/webhooks', 'POST');

        $response = json_decode($this->getResponse()->getContent(), true);

        $this->assertSame(
            'User doesn\'t have required permission',
            $response['error']['messages']
        );

        $this->assertResponseStatusCode(403);
    }
}
