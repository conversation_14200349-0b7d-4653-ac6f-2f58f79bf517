<?php

declare(strict_types=1);

namespace tests\Feature\Companies\Webhooks;

use Exception;
use PHPUnit\Framework\MockObject\Exception as PHPUnitException;
use STCompany\Data\PermissionsTable;
use STCompany\Entity\Role;
use tests\Feature\AuthTestCase;

final class GetWebhookTypesTest extends AuthTestCase
{
    /**
     * @return void
     * @throws PHPUnitException
     * @throws Exception
     */
    public function testGetTypes(): void
    {
        $this->loginAs(Role::MANAGER_ROLE_TYPE);
        $this->addPermissionsToCompanyManager(
            PermissionsTable::WEBHOOKS,
            PermissionsTable::READ_PERMISSION
        );

        $this->dispatchApi('company/webhooks/types', 'GET');

        $response = json_decode($this->getResponse()->getContent(), true);

        $expectedData = ['paragraphs', 'translation', 'events', 'checklists', 'summarization'];

        $this->assertSame($expectedData, $response['result']['types']);

        $this->assertResponseStatusCode(200);
    }

    /**
     * @throws PHPUnitException
     * @throws Exception
     */
    public function testGetTypesWhenNoPermissions(): void
    {
        $this->loginAs(Role::MANAGER_ROLE_TYPE);
        $this->dispatchApi('company/webhooks/types', 'GET');

        $response = json_decode($this->getResponse()->getContent(), true);

        $this->assertSame(
            'User doesn\'t have required permission',
            $response['error']['messages']
        );

        $this->assertResponseStatusCode(403);
    }
}
