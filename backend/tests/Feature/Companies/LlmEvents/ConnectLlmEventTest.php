<?php

declare(strict_types=1);

namespace tests\Feature\Companies\LlmEvents;

use Exception;
use PHPUnit\Framework\MockObject\Exception as PHPUnitException;
use STApi\Entity\Exception\NotFoundApiException;
use STCompany\Data\CompanyLlmEventsTable;
use STCompany\Data\PermissionsTable;
use STCompany\Entity\LlmEvent\LlmEvent as CompanyLlmEvent;
use STCompany\Entity\Role;
use STLlmEvent\Data\LlmEventsTable;
use STLlmEvent\Entity\LlmEvent;
use tests\Feature\AuthTestCase;

final class ConnectLlmEventTest extends AuthTestCase
{
    /**
     * @throws Exception
     * @throws PHPUnitException
     */
    public function testConnect(): void
    {
        $llmEventId = $this->faker->numberBetween(101, 200);
        $llmEvent = $this->createMock(LlmEvent::class);

        $llmEventsTable = $this->createMock(LlmEventsTable::class);
        $llmEventsTable
            ->method('getLlmEvent')
            ->with($llmEventId)
            ->willReturn($llmEvent);
        $this->serviceManager->setService(LlmEventsTable::class, $llmEventsTable);

        $companyLlmEvent = $this->createMock(CompanyLlmEvent::class);
        $companyLlmEvent->method('toArray')->willReturn(['id' => $llmEventId, 'company_id' => $this->companyId]);

        $companyLlmEventsTable = $this->createMock(CompanyLlmEventsTable::class);
        $companyLlmEventsTable
            ->method('getLlmEvent')
            ->with($llmEventId, $this->companyId)
            ->willReturnOnConsecutiveCalls($this->throwException(new NotFoundApiException()), $companyLlmEvent);

        $companyLlmEventsTable
            ->expects($this->once())
            ->method('saveEvent')
            ->with(
                self::callback(
                    function (CompanyLlmEvent $companyLlmEvent) use ($llmEventId) {
                        return $companyLlmEvent->getId() === $llmEventId
                            && $companyLlmEvent->getCompanyId() === $this->companyId;
                    }
                ),
            );
        $this->serviceManager->setService(CompanyLlmEventsTable::class, $companyLlmEventsTable);

        $this->loginAs(Role::MANAGER_ROLE_TYPE);
        $this->addPermissionsToCompanyManager(
            PermissionsTable::EVENTS,
            PermissionsTable::WRITE_PERMISSION
        );
        $this->dispatchApi('company/llm-event/' . $llmEventId, 'POST');

        $response = json_decode($this->getResponse()->getContent(), true);

        $this->assertSame($llmEventId, $response['result']['event']['id']);
        $this->assertSame($this->companyId, $response['result']['event']['company_id']);
        $this->assertResponseStatusCode(200);
    }

    /**
     * @throws PHPUnitException
     * @throws Exception
     */
    public function testConnectWhenAlreadyConnected(): void
    {
        $llmEventId = $this->faker->numberBetween(101, 200);
        $llmEvent = $this->createMock(LlmEvent::class);

        $llmEventsTable = $this->createMock(LlmEventsTable::class);
        $llmEventsTable
            ->method('getLlmEvent')
            ->with($llmEventId)
            ->willReturn($llmEvent);
        $this->serviceManager->setService(LlmEventsTable::class, $llmEventsTable);

        $companyLlmEvent = $this->createMock(CompanyLlmEvent::class);
        $companyLlmEvent->method('toArray')->willReturn(['id' => $llmEventId, 'company_id' => $this->companyId]);

        $companyLlmEventsTable = $this->createMock(CompanyLlmEventsTable::class);
        $companyLlmEventsTable
            ->method('getLlmEvent')
            ->with($llmEventId, $this->companyId)
            ->willReturn($companyLlmEvent);

        $companyLlmEventsTable
            ->expects($this->never())
            ->method('saveEvent');
        $this->serviceManager->setService(CompanyLlmEventsTable::class, $companyLlmEventsTable);

        $this->loginAs(Role::MANAGER_ROLE_TYPE);
        $this->addPermissionsToCompanyManager(
            PermissionsTable::EVENTS,
            PermissionsTable::WRITE_PERMISSION
        );
        $this->dispatchApi('company/llm-event/' . $llmEventId, 'POST');

        $response = json_decode($this->getResponse()->getContent(), true);

        $this->assertSame($llmEventId, $response['result']['event']['id']);
        $this->assertSame($this->companyId, $response['result']['event']['company_id']);
        $this->assertResponseStatusCode(200);
    }

    /**
     * @throws PHPUnitException
     * @throws Exception
     */
    public function testConnectWhenReadPermission(): void
    {
        $llmEventId = $this->faker->numberBetween(101, 200);

        $this->loginAs(Role::MANAGER_ROLE_TYPE);
        $this->addPermissionsToCompanyManager(
            PermissionsTable::EVENTS,
            PermissionsTable::READ_PERMISSION
        );
        $this->dispatchApi('company/llm-event/' . $llmEventId, 'POST');

        $response = json_decode($this->getResponse()->getContent(), true);

        $this->assertSame(403, $response['error']['code']);
        $this->assertSame(
            'User has required permission, but doesn\'t have adequate access level',
            $response['error']['messages']
        );
        $this->assertResponseStatusCode(403);
    }

    /**
     * @throws PHPUnitException
     * @throws Exception
     */
    public function testConnectWhenNoPermission(): void
    {
        $llmEventId = $this->faker->numberBetween(101, 200);

        $this->loginAs(Role::MANAGER_ROLE_TYPE);
        $this->dispatchApi('company/llm-event/' . $llmEventId, 'POST');

        $response = json_decode($this->getResponse()->getContent(), true);

        $this->assertSame(403, $response['error']['code']);
        $this->assertSame('User doesn\'t have required permission', $response['error']['messages']);
        $this->assertResponseStatusCode(403);
    }
}
