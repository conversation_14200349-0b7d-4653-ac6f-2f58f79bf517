<?php

declare(strict_types=1);

namespace tests\Feature\LlmEvents;

use Exception;
use PHPUnit\Framework\MockObject\Exception as PHPUnitException;
use STLlmEvent\Entity\LlmEvent;
use STLlmEvent\Entity\LlmEventCollection;
use STLlmEvent\Service\LlmEventSelectorService;
use tests\Feature\AppTokenTestCase;

final class ListTest extends AppTokenTestCase
{
    /**
     * @throws Exception
     * @throws PHPUnitException
     */
    public function testGetList(): void
    {
        $llmEventId1 = $this->faker->numberBetween(1, 100);
        $llmEventId2 = $this->faker->numberBetween(101, 200);
        $name1 = $this->faker->word();
        $name2 = $this->faker->word();
        $description1 = $this->faker->text();
        $description2 = $this->faker->text();

        $llmEvent1 = new LlmEvent();
        $llmEvent1->setId($llmEventId1);
        $llmEvent1->setName($name1);
        $llmEvent1->setDescription($description1);

        $llmEvent2 = new LlmEvent();
        $llmEvent2->setId($llmEventId2);
        $llmEvent2->setName($name2);
        $llmEvent2->setDescription($description2);

        $llmEventCollection = new LlmEventCollection();
        $llmEventCollection->add($llmEvent1, $llmEventId1);
        $llmEventCollection->add($llmEvent2, $llmEventId2);

        $llmEventSelector = $this->createMock(LlmEventSelectorService::class);
        $llmEventSelector
            ->method('getLlmEvents')
            ->willReturn($llmEventCollection);

        $this->serviceManager->setService(LlmEventSelectorService::class, $llmEventSelector);

        $this->dispatchApi('llm-events');
        $this->assertResponseStatusCode(200);

        $expectedData = json_encode(
            [
                'result' => [
                    'events' => [
                        [
                            'id' => $llmEventId1,
                            'name' => $name1,
                            'description' => $description1,
                        ],
                        [
                            'id' => $llmEventId2,
                            'name' => $name2,
                            'description' => $description2,
                        ],
                    ],
                ],
                'error' => null,
            ]
        );

        $this->assertSame($expectedData, $this->getResponse()->getBody());
    }
}
