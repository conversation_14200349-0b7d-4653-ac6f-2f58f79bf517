<?php

declare(strict_types=1);

namespace tests\Feature\LlmEvents;

use Exception;
use JsonException;
use PHPUnit\Framework\MockObject\Exception as PHPUnitException;
use STAlgo\Service\Client;
use STCompany\Entity\Role;
use tests\Feature\AuthTestCase;

final class ImproveLlmEventTest extends AuthTestCase
{
    /**
     * @throws PHPUnitException
     * @throws Exception
     */
    public function testImprove(): void
    {
        $originalName = $this->faker->text();
        $originalDescription = $this->faker->text();

        $improvedName = $this->faker->text();
        $improvedDescription = $this->faker->text();

        $resultData = [
            'status' => 'ok',
            'results' => [
                'title' => $improvedName,
                'description' => $improvedDescription
            ],
        ];
        $client = $this->createMock(Client::class);
        $client
            ->method('improveLlmEvent')
            ->with($originalName, $originalDescription)
            ->willReturn($resultData);
        $this->serviceManager->setService(Client::class, $client);

        $this->getRequest()->setContent(json_encode([
            'name' => $originalName,
            'description' => $originalDescription
        ]));

        $this->loginAs(Role::MANAGER_ROLE_TYPE);
        $this->dispatchApi('llm-events/improve', 'POST');

        $response = json_decode($this->getResponse()->getContent(), true);

        $this->assertSame($improvedName, $response['result']['event']['name']);
        $this->assertSame($improvedDescription, $response['result']['event']['description']);

        $this->assertResponseStatusCode(200);
    }

    /**
     * @throws PHPUnitException
     * @throws Exception
     */
    public function testImproveWhenUnsuccessfulAlgoResponse(): void
    {
        $originalName = $this->faker->text();
        $originalDescription = $this->faker->text();

        $resultData = [
            'status' => 'fail',
            'results' => [],
        ];
        $client = $this->createMock(Client::class);
        $client
            ->method('improveLlmEvent')
            ->with($originalName, $originalDescription)
            ->willReturn($resultData);
        $this->serviceManager->setService(Client::class, $client);

        $this->getRequest()->setContent(json_encode([
            'name' => $originalName,
            'description' => $originalDescription
        ]));

        $this->loginAs(Role::MANAGER_ROLE_TYPE);
        $this->dispatchApi('llm-events/improve', 'POST');

        $error = 'Failed to improve LLM event.';
        $response = json_decode($this->getResponse()->getContent(), true);

        $this->assertSame($error, $response['error']['messages']);

        $this->assertResponseStatusCode(502);
    }

    /**
     * @dataProvider wrongDataDataProvider
     * @param array $resultData
     * @return void
     * @throws PHPUnitException
     * @throws Exception
     */
    public function testImproveWhenWrongAlgoResponse(array $resultData): void
    {
        $originalName = $this->faker->text();
        $originalDescription = $this->faker->text();

        $client = $this->createMock(Client::class);
        $client
            ->method('improveLlmEvent')
            ->with($originalName, $originalDescription)
            ->willReturn($resultData);
        $this->serviceManager->setService(Client::class, $client);

        $this->getRequest()->setContent(json_encode([
            'name' => $originalName,
            'description' => $originalDescription
        ]));

        $this->loginAs(Role::MANAGER_ROLE_TYPE);
        $this->dispatchApi('llm-events/improve', 'POST');

        $error = 'Failed to improve LLM event.';
        $response = json_decode($this->getResponse()->getContent(), true);

        $this->assertSame($error, $response['error']['messages']);

        $this->assertResponseStatusCode(502);
    }

    public static function wrongDataDataProvider(): array
    {
        return [
            [
                [
                    'results' => [
                        'title' => 'title',
                        'description' => 'description',
                    ]
                ]
            ],
            [
                [
                    'status' => 'fail',
                    'results' => [
                        'title' => 'title',
                        'description' => 'description',
                    ]
                ]
            ],
            [
                [
                    'status' => 'ok',
                ]
            ],
            [
                [
                    'status' => 'ok',
                    'results' => [
                        'title' => 'title'
                    ]
                ]
            ],
            [
                [
                    'status' => 'ok',
                    'results' => [
                        'description' => 'description',
                    ]
                ]
            ]
        ];
    }

    /**
     * @throws PHPUnitException
     * @throws Exception
     */
    public function testImproveWhenUnexpectedAlgoResponse(): void
    {
        $originalName = $this->faker->text();
        $originalDescription = $this->faker->text();

        $client = $this->createMock(Client::class);
        $client
            ->method('improveLlmEvent')
            ->with($originalName, $originalDescription)
            ->willThrowException(new JsonException());
        $this->serviceManager->setService(Client::class, $client);

        $this->getRequest()->setContent(json_encode([
            'name' => $originalName,
            'description' => $originalDescription
        ]));

        $this->loginAs(Role::MANAGER_ROLE_TYPE);
        $this->dispatchApi('llm-events/improve', 'POST');

        $error = 'Failed to improve LLM event.';
        $response = json_decode($this->getResponse()->getContent(), true);

        $this->assertSame($error, $response['error']['messages']);

        $this->assertResponseStatusCode(502);
    }
}
