<?php

declare(strict_types=1);

namespace tests\Feature;

use Exception;
use Laminas\Http\Headers;
use PHPUnit\Framework\MockObject\Exception as PHPUnitException;
use PHPUnit\Framework\MockObject\MockObject;
use STCompany\Entity\Company;
use STCompany\Entity\Permission\PermissionCollection;
use STCompany\Entity\Permission\UserPermission;
use STCompany\Entity\Role;
use STCompany\Entity\User as CompanyUser;
use STCompany\Service\CompanyService;
use STCompany\Service\UserService;
use STFront\Entity\Front;
use STFront\Service\FrontService;
use STRedis\Service\RedisService;
use STUser\Data\UsersTable;
use STUser\Entity\User;
use STUser\Service\AuthService;

abstract class AuthTestCase extends AppTokenTestCase
{
    protected User|MockObject $globalAdminUser;
    protected User|MockObject $companyAdminUser;
    protected User|MockObject $companyManagerUser;

    protected int $frontId;
    protected Front $front;
    protected int $companyId;
    protected Company|MockObject $company;

    /**
     * @return void
     * @throws PHPUnitException
     */
    protected function setUp(): void
    {
        parent::setUp();

        $this->frontId = $this->faker->numberBetween(1, 100);
        $this->companyId = $this->faker->numberBetween(101, 200);

        $this->createUsers();
        $this->createFrontService();
        $this->setCompany();
    }

    /**
     * @throws PHPUnitException
     */
    private function createFrontService(): void
    {
        $this->front = $this->createMock(Front::class);
        $this->front->method('getId')->willReturn($this->frontId);

        $frontService = $this->createMock(FrontService::class);
        $frontService
            ->method('getActiveFront')
            ->willReturn($this->front);
        $this->serviceManager->setService(FrontService::class, $frontService);
    }

    /**
     * @return void
     * @throws PHPUnitException
     */
    private function setCompany(): void
    {
        $this->company = $this->createMock(Company::class);
        $this->company
            ->method('getId')
            ->willReturn($this->companyId);

        $companyServiceMap = [
            [$this->companyId, $this->globalAdminUser->getId(), $this->company],
            [$this->companyId, $this->companyAdminUser->getId(), $this->company],
            [$this->companyId, $this->companyManagerUser->getId(), $this->company]
        ];
        $companyService = $this->createMock(CompanyService::class);
        $companyService
            ->method('getCompany')
            ->willReturnMap($companyServiceMap);

        $this->serviceManager->setService(CompanyService::class, $companyService);
    }

    /**
     * @throws PHPUnitException
     */
    private function createUsers(): void
    {
        $globalAdminUserId = $this->faker->numberBetween(101, 200);

        $globalAdminRole = $this->createMock(Role::class);
        $globalAdminRole->method('isAdmin')->willReturn(true);

        $this->globalAdminUser = $this->createMock(CompanyUser::class);
        $this->globalAdminUser
            ->method('isGlobalAdmin')
            ->willReturn(true);
        $this->globalAdminUser
            ->method('getRole')
            ->willReturn($globalAdminRole);
        $this->globalAdminUser
            ->method('getId')
            ->willReturn($globalAdminUserId);

        $companyAdminUserId = $this->faker->numberBetween(201, 300);

        $companyAdminRole = $this->createMock(Role::class);
        $companyAdminRole->method('isCompanyAdmin')->willReturn(true);

        $this->companyAdminUser = $this->createMock(CompanyUser::class);
        $this->companyAdminUser
            ->method('getRole')
            ->willReturn($companyAdminRole);
        $this->companyAdminUser
            ->method('getId')
            ->willReturn($companyAdminUserId);

        $companyManagerUserId = $this->faker->numberBetween(301, 400);
        $companyManagerRoleId = $this->faker->numberBetween(401, 500);

        $companyManagerRole = $this->createMock(Role::class);
        $companyManagerRole->method('getId')->willReturn($companyManagerRoleId);
        $companyManagerRole->method('isManager')->willReturn(true);
        $companyManagerRole->method('getPermissions')->willReturn(new PermissionCollection());

        $this->companyManagerUser = $this->createMock(CompanyUser::class);
        $this->companyManagerUser
            ->method('getRole')
            ->willReturn($companyManagerRole);

        $this->companyManagerUser
            ->method('getId')
            ->willReturn($companyManagerUserId);
    }

    /**
     * @throws PHPUnitException
     * @throws Exception
     */
    protected function loginAs(int $userRole): void
    {
        $user = $this->getUserByRole($userRole);

        $userService = $this->createMock(UserService::class);
        $userService
            ->method('getCompanyUserActivity')
            ->with($this->companyId, $user->getId())
            ->willReturn(true);
        $userService
            ->method('getUser')
            ->with($this->companyId, $user->getId())
            ->willReturn($user);

        $this->serviceManager->setService(UserService::class, $userService);

        $this->setAuthUsers($user);
    }

    /**
     * @throws PHPUnitException
     * @throws Exception
     */
    protected function addPermissionsToCompanyManager(int $permissionId, string $accessLevel): void
    {
        $user = $this->getUserByRole(Role::MANAGER_ROLE_TYPE);

        $role = $user->getRole();
        $permissions = $role->getPermissions();
        $permission = $this->createMock(UserPermission::class);
        $permission->method('getAccessLevel')->willReturn($accessLevel);

        $permissions->add($permission, $permissionId);
    }

    /**
     * @throws Exception
     */
    private function getUserByRole(int $userRole): User
    {
        return match($userRole) {
            Role::ADMIN_ROLE_TYPE => $this->globalAdminUser,
            Role::COMPANY_ADMIN_ROLE_TYPE => $this->companyAdminUser,
            Role::MANAGER_ROLE_TYPE => $this->companyManagerUser,
            default => throw new Exception('Unknown user role'),
        };
    }

    /**
     * @param User $user
     * @return void
     * @throws PHPUnitException
     */
    private function setAuthUsers(User $user): void
    {
        $token = $this->faker->text(30);

        $this->setHeaders($token);

        $usersTable = $this->createMock(UsersTable::class);
        $redisService = $this->createMock(RedisService::class);
        $authService = $this->getMockBuilder(AuthService::class)
            ->onlyMethods(['restore', 'isSigned', 'getUser'])
            ->setConstructorArgs([$usersTable, $redisService])
            ->getMock();
        $authService
            ->method('restore')
            ->with($token)
            ->willReturn($user);
        $authService
            ->method('isSigned')
            ->willReturn(true);
        $authService
            ->method('getUser')
            ->willReturn($user);

        $this->serviceManager->setService(AuthService::class, $authService);
    }

    /**
     * @param string $token
     * @return void
     * @throws PHPUnitException
     */
    private function setHeaders(string $token): void
    {
        /**
         * @var Headers $headers
         */
        $headers = $this->getRequest()->getHeaders();

        $headers->addHeaders([
            'auth-token' => $token,
            'company-id' => $this->companyId,
        ]);
        $this->getRequest()->setHeaders($headers);
    }
}
