<?php

declare(strict_types=1);

namespace tests\Feature;

use ArrayObject;
use Exception;
use Laminas\Http\Headers;
use PHPUnit\Framework\MockObject\Exception as PHPUnitException;
use STApi\Data\ApiApplicationsTable;
use tests\TestCase as BaseTestCase;

abstract class AppTokenTestCase extends BaseTestCase
{
    protected string $bearerToken;

    /**
     * @throws PHPUnitException
     */
    protected function setUp(): void
    {
        parent::setUp();

        $this->setHeaders();
        $this->setApplication();
    }

    /**
     * @return void
     * @throws PHPUnitException
     */
    private function setHeaders(): void
    {
        $this->bearerToken = $this->faker->text(30);
        $authorizationToken = 'Bearer ' . $this->bearerToken;

        $headers = new Headers();
        $headers->addHeaders([
            'Authorization' => $authorizationToken,
        ]);
        $this->getRequest()->setHeaders($headers);
    }

    /**
     * @return void
     * @throws PHPUnitException
     */
    private function setApplication(): void
    {
        $applicationArrayObject = $this->createMock(ArrayObject::class);
        $applicationArrayObject
            ->method('getArrayCopy')
            ->willReturn([
                'application_type' => ApiApplicationsTable::ROBONOTE_APPLICATION
            ]);

        $applicationTable = $this->createMock(ApiApplicationsTable::class);
        $applicationTable
            ->method('getApplicationByToken')
            ->with($this->bearerToken)
            ->willReturn($applicationArrayObject);

        $this->serviceManager->setService(ApiApplicationsTable::class, $applicationTable);
    }

    /**
     * @param $url
     * @param $method
     * @param array $params
     * @return void
     * @throws Exception
     */
    protected function dispatchApi($url, $method = null, array $params = []): void
    {
        $this->dispatch('/api/v0/' . $url, $method, $params);
    }
}
