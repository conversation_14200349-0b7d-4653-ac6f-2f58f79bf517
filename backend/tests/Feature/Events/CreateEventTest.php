<?php

declare(strict_types=1);

namespace tests\Feature\Events;

use Exception;
use Laminas\Db\Adapter\Adapter;
use Laminas\Db\ResultSet\ResultSet;
use PHPUnit\Framework\MockObject\Exception as PHPUnitException;
use STAlgo\Data\AlgoEventsTable;
use STCall\Data\CallsAlgoEventsTable;
use STCompany\Data\EventsAlgoEventsTable;
use STCompany\Data\EventsCategoriesTable;
use STCompany\Data\EventsColorsTable;
use STCompany\Data\EventsSearchWordsTable;
use STCompany\Data\EventsTable;
use STCompany\Data\RolesTable;
use STCompany\Entity\Event\Event;
use STCompany\Entity\Event\EventCollection;
use STCompany\Entity\Role;
use STCompany\Service\EventService;
use STCompany\Validator\EventValidator;
use tests\Feature\AuthTestCase;

final class CreateEventTest extends AuthTestCase
{
    /**
     * @throws Exception
     * @throws PHPUnitException
     */
    public function testCreateEvent(): void
    {
        $eventName = $this->faker->word();

        $algoEventName1 = $this->faker->text(10);
        $algoEventName2 = $this->faker->sentence(3);

        $categoryId = $this->faker->numberBetween(101, 200);
        $roleId = $this->faker->numberBetween(201, 300);
        $colorId = $this->faker->numberBetween(301, 400);
        $eventId = $this->faker->numberBetween(401, 500);

        $adapter = $this->createMock(Adapter::class);
        $this->serviceManager->setService(Adapter::class, $adapter);

        $role = $this->createMock(Role::class);
        $role->method('getId')->willReturn($roleId);

        $savedEvent = $this->createMock(Event::class);
        $savedEvent->method('getId')->willReturn($eventId);
        $savedEvent->method('getRole')->willReturn($role);

        $eventsTable = $this->createMock(EventsTable::class);
        $eventsTable
            ->expects($this->once())
            ->method('saveEvent')
            ->with(
                self::callback(
                    function (Event $event) use ($eventName, $eventId) {
                        $event->setId($eventId);
                        return $event->getName() === $eventName;
                    }
                ),
            )
            ->willReturn($eventId);
        $eventsTable
            ->method('getEvents')
            ->with($this->companyId, $categoryId)
            ->willReturn($this->createMock(EventCollection::class));
        $eventsTable
            ->method('getEvent')
            ->with($this->companyId, $eventId)
            ->willReturn($savedEvent);
        $this->serviceManager->setService(EventsTable::class, $eventsTable);

        $categoryResultSet = new ResultSet();
        $categoryResultSet->initialize([
            [
                'category_id' => $categoryId,
                'role_id' => $roleId,
                'color_id' => $colorId
            ]
        ]);

        $categoriesTable = $this->createMock(EventsCategoriesTable::class);
        $categoriesTable
            ->method('getCategory')
            ->with($this->companyId, $categoryId)
            ->willReturn($categoryResultSet);
        $this->serviceManager->setService(EventsCategoriesTable::class, $categoriesTable);

        $roleResultSet = new ResultSet();
        $roleResultSet->initialize([
            ['role_id' => $roleId]
        ]);
        $rolesTable = $this->createMock(RolesTable::class);
        $rolesTable
            ->method('getRole')
            ->with($this->companyId, $roleId)
            ->willReturn($roleResultSet);
        $this->serviceManager->setService(RolesTable::class, $rolesTable);

        $colorResultSet = new ResultSet();
        $colorResultSet->initialize([['color_id' => $colorId]]);
        $eventsColorsTable = $this->createMock(EventsColorsTable::class);
        $eventsColorsTable
            ->method('getColor')
            ->with($colorId)
            ->willReturn($colorResultSet);
        $this->serviceManager->setService(EventsColorsTable::class, $eventsColorsTable);

        $searchWordsResultSet = new ResultSet();
        $searchWordsResultSet->initialize([]);
        $eventsSearchWordsTable = $this->createMock(EventsSearchWordsTable::class);
        $eventsSearchWordsTable
            ->method('getSearchWordsByEventIds')
            ->with([])
            ->willReturn($searchWordsResultSet);
        $this->serviceManager->setService(EventsSearchWordsTable::class, $eventsSearchWordsTable);

        $algoEventsWordsResultSet = new ResultSet();
        $algoEventsWordsResultSet->initialize([]);
        $eventsAlgoEventsTable = $this->createMock(EventsAlgoEventsTable::class);
        $eventsAlgoEventsTable
            ->expects($this->once())
            ->method('saveAlgoEvents')
            ->with(
                self::callback(
                    function (Event $event) use ($algoEventName1, $algoEventName2) {
                        return $event->getAlgoEvents() === [$algoEventName1, $algoEventName2];
                    }
                ),
            );
        $eventsAlgoEventsTable
            ->method('getAlgoEvents')
            ->with([])
            ->willReturn($algoEventsWordsResultSet);
        $this->serviceManager->setService(EventsAlgoEventsTable::class, $eventsAlgoEventsTable);

        $eventValidator = $this->createMock(EventValidator::class);
        $this->serviceManager->setService(EventValidator::class, $eventValidator);

        $eventService = $this->getMockBuilder(EventService::class)
            ->setConstructorArgs([
                    $categoriesTable,
                    $eventsTable,
                    $eventsSearchWordsTable,
                    $eventsAlgoEventsTable,
                    $eventsColorsTable,
                    $this->createMock(CallsAlgoEventsTable::class),
                    $rolesTable,
                    $this->createMock(AlgoEventsTable::class),
                ]
            )
            ->onlyMethods(['getConnection', 'getCallPrecalculationManager', 'getEvent'])
            ->getMock();
        $eventService
            ->method('getEvent')
            ->with($this->companyId, $eventId)
            ->willReturn($savedEvent);
        $this->serviceManager->setService(EventService::class, $eventService);

        $this->getRequest()->setContent(json_encode([
            'name' => ' ' . $eventName . " \n",
            'algo_events' => [$algoEventName1, $algoEventName2],
            'category_id' => $categoryId,
            'role_id' => $roleId,
        ]));

        $this->loginAs(Role::COMPANY_ADMIN_ROLE_TYPE);
        $this->dispatchApi('company/event', 'POST');
    }
}
