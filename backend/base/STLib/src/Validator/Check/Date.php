<?php

namespace STLib\Validator\Check;

use Lam<PERSON>\Validator\AbstractValidator;

class Date extends AbstractValidator
{
    
    const INVALID = 'invalid';
    const TOO_EARLY = 'early';
    const TOO_LATE = 'late';

    /**
     *
     * @var array
     */
    protected $messageTemplates = [
        self::INVALID   => 'Invalid type given. DateTime expected',
        self::TOO_EARLY => 'The input is earlier than %min% date',
        self::TOO_LATE  => 'The input is later than %max% date',
    ];
    
    /**
     *
     * @var \DateTime
     */
    protected ?\DateTime $min = null;
    
    /**
     *
     * @var \DateTime
     */
    protected ?\DateTime $max = null;
    
    /**
     * 
     * @return \DateTime|null
     */
    public function getMin(): ?\DateTime {
        return $this->min;
    }

    /**
     * 
     * @return \DateTime|null
     */
    public function getMax(): ?\DateTime {
        return $this->max;
    }

    /**
     * 
     * @param \DateTime|string $min
     * @return AbstractValidator
     * @throws \InvalidArgumentException
     */
    public function setMin(\DateTime|string $min): AbstractValidator {
        if (is_null($min)) {
            return $this;
        }
        $this->min = new \DateTime($min);
        if (!($this->min instanceof \DateTime)) {
            throw new \InvalidArgumentException('Invalid min value');
        }
        return $this;
    }

    /**
     * 
     * @param \DateTime|string $max
     * @return AbstractValidator
     * @throws \InvalidArgumentException
     */
    public function setMax(\DateTime|string $max): AbstractValidator {
        if (is_null($max)) {
            return $this;
        }
        $this->max = new \DateTime($max);
        if (!($this->max instanceof \DateTime)) {
            throw new \InvalidArgumentException('Invalid max value');
        }
        return $this;
    }

    /**
     * 
     * @param \DateTime|string $value
     * @return bool
     */
    public function isValid($value) {
        $this->setValue(new \DateTime($value));

        if (!($this->getValue() instanceof \DateTime)) {
            $this->error(self::INVALID);
            return false;
        }
        
        if ($this->getMax() instanceof \DateTime && $this->getValue() > $this->getMax()) {
            $this->error(self::TOO_LATE);
            return false;
        }
        
        if ($this->getMin() instanceof \DateTime && $this->getValue() < $this->getMin()) {
            $this->error(self::TOO_EARLY);
            return false;
        }

        return true;
    }

}
