<?php

namespace STLib\Mvc\Controller;

use Laminas\Mvc\Controller\AbstractActionController;

abstract class AbstractController extends AbstractActionController {
    
    protected const MP3 = 'mp3';
    protected const CSV = 'csv';
    protected const PDF = 'pdf';
    protected const XLSX = 'xlsx';
    
    protected const CSV_FORMAT = 'csv';
    protected const JSON_FORMAT = 'json';
    protected const PDF_FORMAT = 'pdf';
    protected const MP3_FORMAT = 'mp3';

    /**
     * 
     * @var \Laminas\ServiceManager\ServiceManager
     */
    protected ?\Laminas\ServiceManager\ServiceManager $serviceManager = null;

    /**
     * 
     * @return \Laminas\ServiceManager\ServiceManager
     */
    public function getServiceManager(): \Laminas\ServiceManager\ServiceManager {
        return $this->serviceManager;
    }

    /**
     * 
     * @param \Laminas\ServiceManager\ServiceManager $serviceManager
     * @return AbstractController
     */
    public function setServiceManager(\Laminas\ServiceManager\ServiceManager $serviceManager): AbstractController {
        $this->serviceManager = $serviceManager;
        return $this;
    }

    /**
     * 
     * @return \Laminas\View\Model\ViewModel
     */
    protected function notFound() {
        return $this->notFoundAction();
    }
    
    /**
     *
     * @see \STLib\Mvc\Controller\AbstractController::json()
     * @see \STLib\Mvc\Controller\AbstractController::csv()
     * @see \STLib\Mvc\Controller\AbstractController::pdf()
     */
    protected function output($data = [], int $statusCode = \Laminas\Http\Response::STATUS_CODE_200, $format = self::JSON_FORMAT, ?array $options = []): \Laminas\Http\Response
    {
        if ($statusCode > 599 || $statusCode < 100) {
            $statusCode = \Laminas\Http\Response::STATUS_CODE_500;
        }
        switch ($format) {
            case static::JSON_FORMAT:
                return $this->json($data, $statusCode);
            case static::CSV_FORMAT:
                return $this->csv($data, $options);
            case static::PDF_FORMAT:
                return $this->pdf($data, $options);
            case static::MP3_FORMAT:
                return $this->mp3($data, $options);
        }
        throw new \STApi\Entity\Exception\InvalidResponseFormatApiException();
    }
    
    /**
     * 
     * @param array $data
     * @param int $statusCode
     * @return \Laminas\Http\Response
     */
    protected function json(array $data = [], int $statusCode = \Laminas\Http\Response::STATUS_CODE_200): \Laminas\Http\Response {
        $this->getResponse()
                ->setContent(json_encode($data))
                ->setStatusCode($statusCode > 0 ? $statusCode : \Laminas\Http\Response::STATUS_CODE_200)
                ->getHeaders()
                ->addHeaderLine('Content-Type: application/json');
        return $this->getResponse();
    }
    
    /**
     * 
     * @param string $csv
     * @param array|null $options
     * @return \Laminas\Http\Response
     */
    protected function csv(string $csv, ?array $options = []): \Laminas\Http\Response {
        $this->getResponse()
                ->setContent($csv)
                ->getHeaders()
                ->addHeaderLine('Content-Type: text/csv; charset=UTF-8')
                ->addHeaderLine('Expires: 0')
                ->addHeaderLine('Pragma: no-cache');
        if (isset($options['fileName'])) {
            $this->getResponse()
                    ->getHeaders()
                    ->addHeaderLine('Content-Disposition: attachment; filename=' . $options['fileName'] . '.' . static::CSV);
        }
        return $this->getResponse();
    }
    
    /**
     * 
     * @param string $xlsx
     * @param array|null $options
     * @return \Laminas\Http\Response
     */
    protected function xlsx(string $xlsx, ?array $options = []): \Laminas\Http\Response {
        $this->getResponse()
                ->setContent($xlsx)
                ->getHeaders()
                ->addHeaderLine('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
                ->addHeaderLine('Expires: 0')
                ->addHeaderLine('Pragma: no-cache');
        if (isset($options['fileName'])) {
            $this->getResponse()
                    ->getHeaders()
                    ->addHeaderLine('Content-Disposition: attachment; filename=' . $options['fileName'] . '.' . static::XLSX);
        }
        return $this->getResponse();
    }
    
    /**
     * 
     * @param string $pdf
     * @param array|null $options
     * @return \Laminas\Http\Response
     */
    protected function pdf(string $pdf = null, ?array $options = []): \Laminas\Http\Response {
        $this->getResponse()
                ->getHeaders()
                ->addHeaderLine('Content-Type: application/pdf');
        if (!empty($pdf)) {
            $this
                    ->getResponse()
                    ->setContent($pdf);
        }
        if (isset($options['fileName'])) {
            $this->getResponse()
                    ->getHeaders()
                    ->addHeaderLine('Content-Disposition: attachment; filename=' . $options['fileName'] . '.' . static::PDF);
        }
        return $this->getResponse();
    }

    /**
     *
     * @param string $mp3
     * @param array $options
     * @return \Laminas\Http\Response
     */
    protected function mp3(string $mp3, array $options = []): \Laminas\Http\Response {
        $this->getResponse()
            ->getHeaders()
            ->addHeaderLine('Content-Type: audio/mpeg');

        if (!empty($mp3)) {
            $this
                ->getResponse()
                ->setContent($mp3);
        }

        if (isset($options['fileName'])) {
            $this->getResponse()
                ->getHeaders()
                ->addHeaderLine(
                    sprintf(
                        'Content-Disposition: attachment; filename=%s',
                        $options['fileName'] . '.' . static::MP3,
                    )
                );
        }

        return $this->getResponse();
    }
    
    /**
     *
     * @return \STLib\Expand\Collection
     */
    protected function getApiParams(): \STLib\Expand\Collection
    {
        if (is_null($this->apiParams)) {
            $routeParams = $this->params()->fromRoute();
            unset($routeParams['controller']);
            unset($routeParams['action']);
            unset($routeParams['access-checks']);
            unset($routeParams['permissions']);
            $bodyParams = (is_array(json_decode($this->getRequest()->getContent(), true)) ? json_decode($this->getRequest()->getContent(), true) : []);
            $queryParams = $this->params()->fromQuery();
            /**
             * Params priority:
             * 1. $queryParams (lowest priority)
             * 2. $bodyParams
             * 3. $routeParams (highest priority)
             */
            $params = array_merge($queryParams, $bodyParams, $routeParams);
            $this->apiParams = new \STLib\Expand\Collection($params);
        }
        return $this->apiParams;
    }

    /**
     *
     * @param string $key
     * @return bool
     */
    protected function hasApiParam(string $key): bool
    {
        return $this->getApiParams()->offsetExists($key);
    }

    /**
     *
     * @param string $key
     * @return mixed
     */
    protected function getApiParam(string $key, $default = null): mixed
    {
        return $this->getApiParams()->offsetGet($key) ?? $default;
    }
    
    /**
     *
     * @param \Throwable $e
     * @return string
     */
    protected function getErrorLogMessage(\Throwable $e): string
    {
        return
                'PHP THROWABLE: '
                . '[' . (\Carbon\Carbon::now())->toIso8601String() . '] '
                . $e->getMessage()
                . ' in '
                . $e->getFile()
                . ' on line '
                . $e->getLine()
                . '. Trace: '
                . $e->getTraceAsString();
    }
}
