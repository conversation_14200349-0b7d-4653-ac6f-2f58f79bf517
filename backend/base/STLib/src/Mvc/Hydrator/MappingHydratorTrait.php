<?php

namespace STLib\Mvc\Hydrator;

trait MappingHydratorTrait {
    
    /**
     *
     * @var \STLib\Mvc\Hydrator\MappingHydrator
     */
    protected ?\STLib\Mvc\Hydrator\MappingHydrator $mappingHydrator = null;
    
    /**
     * 
     * @param array $data
     * @param string $class
     * @param array $mapping
     * @param bool $withConstructor
     * @return object
     */
    protected function hydrate(array $data, string $class, array $mapping = [], bool $withConstructor = false): object {
        $reflectionClass = (new \ReflectionClass($class));
        return $this->getMapperHydrator()->hydrate(
            $data,
            $withConstructor ? $reflectionClass->newInstance() : $reflectionClass->newInstanceWithoutConstructor(),
            $mapping
        );
    }
    
    /**
     * 
     * @param object $object
     * @return array
     */
    protected function extract(object $object, array $mapping = []): array {
        return $this->getMapperHydrator()->extract($object, $mapping);
    }
    
    /**
     * 
     * @return \STLib\Mvc\Hydrator\MappingHydrator
     */
    private function getMapperHydrator(): MappingHydrator {
        if (is_null($this->mappingHydrator)) {
            $this->mappingHydrator = new MappingHydrator();
        }
        return $this->mappingHydrator;
    }
    
}
