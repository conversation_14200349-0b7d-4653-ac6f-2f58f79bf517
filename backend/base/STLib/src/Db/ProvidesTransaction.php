<?php

namespace STLib\Db;

trait ProvidesTransaction {
    
    /**
     * 
     * @var Connection|null
     */
    protected ?Connection $connection = null;
    
    /**
     * 
     * @param Connection $connection
     * @return void
     */
    protected function getConnection(): \Laminas\Db\Adapter\Driver\ConnectionInterface {
        if (is_null($this->connection)) {
            $this->connection = new Connection();
        }
        return $this->connection::getConnection();
    }

    /**
     * 
     * @return void
     */
    public function beginTransaction(): void {
        $this->getConnection()->beginTransaction();
    }
    
    /**
     * 
     * @return void
     */
    public function commit(): void {
        $this->getConnection()->commit();
    }
    
    /**
     * 
     * @return void
     */
    public function rollback(): void {
        $this->getConnection()->rollback();
    }

}
