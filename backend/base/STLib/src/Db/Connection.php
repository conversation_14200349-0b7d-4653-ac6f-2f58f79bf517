<?php

namespace STLib\Db;

final class Connection {

    /**
     * 
     * @var \Laminas\Db\Adapter\Driver\ConnectionInterface|null
     */
    protected static ?\Laminas\Db\Adapter\Driver\ConnectionInterface $connection = null;
    
    /**
     * 
     * @param \Laminas\Db\Adapter\Driver\ConnectionInterface $connection
     * @return void
     */
    public static function setConnection(\Laminas\Db\Adapter\Driver\ConnectionInterface $connection): void {
        self::$connection = $connection;
    }
    
    /**
     * 
     * @return \Laminas\Db\Adapter\Driver\ConnectionInterface
     */
    public static function getConnection(): \Laminas\Db\Adapter\Driver\ConnectionInterface {
        return self::$connection;
    }

}
