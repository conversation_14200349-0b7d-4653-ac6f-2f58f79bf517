<?php

declare(strict_types=1);

namespace STLib\Db;

use RuntimeException;
use STLib\Expand\Collection;

abstract class EntityCollection extends Collection
{
    abstract protected function getEntityClass(): string;

    /**
     * @param Entity $object
     * @param string|int|null $key
     * @return Collection
     * @throws RuntimeException
     */
    public function add(mixed $object, string|int|null $key = null): Collection
    {
        $entityName = $this->getEntityClass();

        if (!($object instanceof $entityName) || !($object instanceof Entity)) {
            throw new RuntimeException(
                sprintf(
                    'Object must be an instance of "%s" and "%s"',
                    $this->getEntityClass(),
                    Entity::class
                )
            );
        }

        parent::add($object, $key ?? $this->getDefaultKey($object));

        return $this;
    }

    public function getDefaultKey(Entity $object): ?int
    {
        return $object->getId();
    }

    /**
     * @param bool $asArray
     * @return Entity[]
     */
    public function toArray(bool $asArray = true): array
    {
        if (!$asArray) {
            return parent::toArray();
        }

        $result = [];
        foreach ($this as $object) {
            $result[] = $object->toArray();
        }
        return $result;
    }
}
