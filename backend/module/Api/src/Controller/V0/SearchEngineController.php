<?php

declare(strict_types=1);

namespace Api\Controller\V0;

use Carbon\Carbon;
use Exception;
use STClickhouse\Entity\Pagination\CandidatePagination;
use STReport\Validator\CallParagraphsSearchValidator;

class SearchEngineController extends BaseController
{
    public const int SEARCH_PARAGRAPHS_MAX_INTERVAL_IN_MONTHS = 4;

    /**
     * @return array
     * @throws Exception
     */
    public function searchClientsAction(): array
    {
        $user = $this->company()->user()->getUser($this->company->getId(), $this->auth()->getUser()->getId());

        $pagination = new CandidatePagination();

        $search = $this->getApiParam('search') ?? '';
        if (empty($search)) {
            return $pagination->toArray();
        }

        $filters['team_ids'] = $this->company()->user()->getUserTeamIds($this->company->getId(), $user->getId());

        $pagination
            ->setFilter($filters)
            ->getParams()
            ->setPageNumber((int) $this->getApiParam('page_number'))
            ->setItemsOnPage((int) $this->getApiParam('items_on_page'))
            ->setSort($this->getApiParam('sort') ?? []);

        $this->report()->client()->searchClients(
            $pagination,
            $this->company->getId(),
            $user->getRole()->getId(),
            $search,
        );

        return $pagination->toArray();
    }

    /**
     * @return array
     * @throws Exception
     */
    public function searchCallsAction(): array
    {
        $user = $this->company()->user()->getUser($this->company->getId(), $this->auth()->getUser()->getId());

        $pagination = new CandidatePagination();

        $search = $this->getApiParam('search') ?? '';
        if (empty($search)) {
            return $pagination->toArray();
        }

        $filters['team_id'] = $this->company()->user()->getUserTeamIds($this->company->getId(), $user->getId());

        $pagination
            ->setFilter($filters)
            ->getParams()
            ->setPageNumber((int) $this->getApiParam('page_number'))
            ->setItemsOnPage((int) $this->getApiParam('items_on_page'))
            ->setSort($this->getApiParam('sort') ?? []);

        $this->report()->call()->searchCalls(
            $pagination,
            $this->company->getId(),
            $user->getRole()->getId(),
            $search,
        );

        return $pagination->toArray();
    }

    /**
     * @return array
     * @throws Exception
     */
    public function searchAgentsAction(): array
    {
        $pagination = new CandidatePagination();
        $search = $this->getApiParam('search') ?? '';

        if (empty($search)) {
            return $pagination->toArray();
        }

        $pagination
            ->setResultCallback(function ($record) {
                if (isset($record['teams'])) {
                    $record['teams'] = array_map(static function ($team) {
                        $team['team_id'] = (int) $team['team_id'];

                        return $team;
                    }, $record['teams']);
                }

                return $record;
            })
            ->getParams()
            ->setAvailableColumns([
                'user_id',
                'user_name',
                'user_email',
                'today_calls_count',
            ])
            ->setPageNumber((int) $this->getApiParam('page_number'))
            ->setItemsOnPage((int) $this->getApiParam('items_on_page'))
            ->setSort($this->getApiParam('sort') ?? []);

        $this->report()->agent()->searchAgents(
            $pagination,
            $this->company->getId(),
            $search,
        );

        return $pagination->toArray();
    }

    /**
     * @return array
     * @throws Exception
     */
    public function searchParagraphsAction(): array
    {
        $pagination = new CandidatePagination();
        $search = $this->getApiParam('search') ?? '';

        $startDate = $this->getApiParam('start_date')
            ? new Carbon($this->getApiParam('start_date'))
            : (new Carbon())->subMonths(static::SEARCH_PARAGRAPHS_MAX_INTERVAL_IN_MONTHS);
        ;
        $endDate = $this->getApiParam('end_date')
            ? (new Carbon($this->getApiParam('end_date')))->endOfDay()
            : (new Carbon())->endOfDay()
        ;

        /** @var CallParagraphsSearchValidator $validator */
        $validator = $this->getServiceManager()->get(CallParagraphsSearchValidator::class);
        $validator->setInstance([
            'search' => $search,
            'start_date' => $startDate,
            'end_date' => $endDate,
        ]);
        $this->validate($validator);

        if (empty($search)) {
            return $pagination->toArray();
        }

        $pagination
            ->getParams()
            ->setPageNumber((int) $this->getApiParam('page_number'))
            ->setItemsOnPage((int) $this->getApiParam('items_on_page'))
            ->setSort($this->getApiParam('sort') ?? []);

        $this->report()->callParagpaph()->searchCallParagraphs(
            $pagination,
            $this->company,
            $search,
            $startDate,
            $endDate,
        );

        return $pagination->toArray();
    }
}
