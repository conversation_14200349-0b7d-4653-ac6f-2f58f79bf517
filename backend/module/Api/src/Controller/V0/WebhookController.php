<?php

declare(strict_types=1);

namespace Api\Controller\V0;

use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;
use STApi\Entity\Exception\NotFoundApiException;
use ST<PERSON>pi\Entity\Exception\ValidationApiException;
use STCall\Service\Webhooks\Interfaces\WebhookServiceInterface;
use STCompany\Validator\Webhook\SaveWebhookValidator;
use STCompany\Validator\Webhook\WebhookExistsValidator;
use stdClass;

use function Symfony\Component\Translation\t;

class WebhookController extends BaseController
{
    /**
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     * @throws NotFoundApiException
     * @throws ValidationApiException
     */
    public function createWebhookAction(): array
    {
        $type = $this->getApiParam('type');
        $isEnabled = $this->getApiParam('is_enabled');
        $url = $this->getApiParam('url');
        $headers = $this->getApiParam('headers');

        /** @var SaveWebhookValidator $validator */
        $validator = $this->getServiceManager()->get(SaveWebhookValidator::class);
        $validator->setInstance([
            'type' => $type,
            'is_enabled' => $isEnabled,
            'url' => $url,
            'headers' => $headers,
            'company_id' => $this->company->getId(),
        ]);
        $validator->validate();
        if ($validator->hasError()) {
            throw new ValidationApiException(json_encode($validator->getAllErrorsByGroups()));
        }

        $id = $this->company()->webhooksSettingsSaver()->createWebhookSettings(
            $type,
            $url,
            $headers,
            $isEnabled,
            $this->company->getId()
        );

        $settings = $this->company()->webhooksSettingsSelector()->getCompaniesWebhooksSettingsData(
            $id,
            $this->company->getId()
        );

        return ['webhook' => $this->formatWebhook($settings)];
    }

    /**
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function getWebhooksAction(): array
    {
        $result = $this
            ->company()
            ->webhooksSettingsSelector()
            ->getCompaniesWebhooksSettingsDataList($this->company->getId());

        $formattedResult = [];
        foreach ($result as $webhook) {
            $formattedResult[] = $this->formatWebhook($webhook);
        }

        return [
            'webhooks' => $formattedResult,
        ];
    }

    public function getTypesAction(): array
    {
        return [
            'types' => WebhookServiceInterface::TYPES,
        ];
    }

    /**
     * @throws NotFoundExceptionInterface
     * @throws NotFoundApiException
     * @throws ContainerExceptionInterface
     * @throws ValidationApiException
     */
    public function updateWebhookAction(): array
    {
        $id = (int) $this->getApiParam('id');
        $isEnabled = $this->getApiParam('is_enabled');
        $url = $this->getApiParam('url');
        $headers = $this->getApiParam('headers');

        /** @var SaveWebhookValidator $validator */
        $validator = $this->getServiceManager()->get(SaveWebhookValidator::class);
        $validator->setInstance([
            'id' => $id,
            'is_enabled' => $isEnabled,
            'url' => $url,
            'headers' => $headers,
            'company_id' => $this->company->getId(),
        ]);
        $validator->validate();
        if ($validator->hasError()) {
            throw new ValidationApiException(json_encode($validator->getAllErrorsByGroups()));
        }

        $this->company()->webhooksSettingsSaver()->updateWebhookSettings($id, $url, $headers, $isEnabled);

        $settings = $this->company()->webhooksSettingsSelector()->getCompaniesWebhooksSettingsData(
            $id,
            $this->company->getId()
        );

        return ['webhook' => $this->formatWebhook($settings)];
    }

    /**
     * @return string[]
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     * @throws ValidationApiException
     */
    public function deleteWebhookAction(): array
    {
        $id = (int) $this->getApiParam('id');

        /** @var WebhookExistsValidator $validator */
        $validator = $this->getServiceManager()->get(WebhookExistsValidator::class);
        $validator->setInstance(['id' => $id, 'company_id' => $this->company->getId()]);
        $validator->validate();
        if ($validator->hasError()) {
            throw new ValidationApiException(json_encode($validator->getAllErrorsByGroups()));
        }

        $this->company()->webhooksSettingsRemover()->deleteWebhookSettings($id);

        return [
            'is_deleted' => true,
            'message' => 'Webhook ' . $id . ' has been successfully deleted',
        ];
    }

    private function formatWebhook(array $settings): array
    {
        return [
            'id' => $settings['company_webhook_setting_id'],
            'type' => $settings['type'],
            'is_enabled' => (bool) $settings['is_enabled'],
            'url' => $settings['url'],
            'headers' => $settings['headers'] ?: new stdClass(),
            'company_id' => $settings['company_id'],
        ];
    }
}
