<?php

declare(strict_types=1);

namespace Api\Controller\V0;

use STLib\Mvc\Hydrator\BaseHydratorTrait;

class PermissionController extends BaseController
{
    use BaseHydratorTrait;

    /**
     *
     * @return array
     */
    public function getPermissionsAction(): array
    {
        return [
            'permissions' => $this->company()->permission()->getPermissionHierarchy()->toArray(),
        ];
    }
}
