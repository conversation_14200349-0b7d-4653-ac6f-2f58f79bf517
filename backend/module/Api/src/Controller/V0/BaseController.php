<?php

declare(strict_types=1);

namespace Api\Controller\V0;

use Laminas\Http\Response;
use Laminas\Mvc\MvcEvent;
use Laminas\Router\Http\RouteMatch;
use STApi\Entity\Exception\ForbiddenApiException;
use ST<PERSON><PERSON>\Entity\Exception\ValidationApiException;
use STCall\Service\Interfaces\TranslatorInterface;
use STCompany\Entity\Company;
use STLib\Expand\Collection;
use STLib\Validator\Validator;
use STMail\Service\MailService;
use Throwable;

/**
 *
 * @method \STAlgo\Controller\Plugin\Algo algo()
 * @method \STApi\Controller\Plugin\Api api()
 * @method \STApi\Controller\Plugin\ApiPermissionChecker apiPermissionChecker()
 * @method \STCall\Controller\Plugin\Call|\STCall\Service\CallService call()
 * @method \STCall\Controller\Plugin\Translator|TranslatorInterface translator()
 * @method \STClickhouse\Controller\Plugin\Clickhouse clickhouse()
 * @method \STConfiguration\Controller\Plugin\Configuration configuration()
 * @method \STCodeManagement\Controller\Plugin\CodeManagement codeManagement()
 * @method \STCompany\Controller\Plugin\Company|\STCompany\Service\CompanyService company()
 * @method \STCompany\Controller\Plugin\CompanyPermissionChecker companyPermissionChecker()
 * @method \STCompany\Controller\Plugin\TeamPermissionChecker teamPermissionChecker()
 * @method \STCompany\Controller\Plugin\UserNotification|\STCompany\Service\Notification\UserNotificationService userNotification()
 * @method \STDashboard\Controller\Plugin\Dashboard dashboard()
 * @method \STEms\Controller\Plugin\Ems ems()
 * @method \STFront\Controller\Plugin\Front|\STFront\Service\FrontService front()
 * @method \STLog\Controller\Plugin\Logger logger()
 * @method \STMail\Controller\Plugin\Mail|MailService mail()
 * @method \STRabbit\Controller\Plugin\Rabbit|\STRabbit\Service\RabbitService rabbit()
 * @method \STRedis\Controller\Plugin\Redis|\Predis\Client redis()
 * @method \STReport\Controller\Plugin\Report report()
 * @method \STRoboTruck\Controller\Plugin\RoboTruck roboTruck()
 * @method \STLlmEvent\Controller\Plugin\LlmEvent llmEvent()
 * @method \STIndustry\Controller\Plugin\Industry industry()
 * @method \STOnboarding\Controller\Plugin\Onboarding onboarding()
 * @method \STUser\Controller\Plugin\Auth|\STUser\Service\AuthService auth()
 * @method \STUser\Controller\Plugin\User|\STUser\Service\UserService user()
 * @method \STUser\Controller\Plugin\UserPermissionChecker userPermissionChecker()
 */
class BaseController extends \STLib\Mvc\Controller\AbstractController
{
    public const string CHECK_API_ACCESS = 'api-access';
    public const string CHECK_ROBONOTE_API_ONLY_ACCESS = 'robonote-api-only-access';
    public const string CHECK_ROBONOTE_CHILD_API_ONLY_ACCESS = 'robonote-child-api-only-access';
    public const string CHECK_AUTHENTICATION = 'authentification-access';
    public const string CHECK_COMPANY_ACCESS = 'company-access';
    public const string CHECK_GLOBAL_ADMIN_ACCESS = 'global-admin-access';
    public const string CHECK_GLOBAL_ADMIN_OR_FRONT_GLOBAL_ADMIN_ACCESS = 'global-admin-or-front-global-admin-access';
    public const string CHECK_ROLE_ACCESS = 'role-access';
    public const string CHECK_CALL_ACCESS = 'call-access';
    public const string CHECK_USER_ACCESS = 'user-access';
    public const string CHECK_CLIENT_SUMMARY_ACCESS = 'client-summary-access';

    /**
     *
     * @var Collection|null
     */
    protected ?Collection $apiParams = null;

    /**
     *
     * @var Company|null
     */
    protected ?Company $company = null;

    /**
     *
     * @param MvcEvent $event
     * @return Response
     */
    public function onDispatch(MvcEvent $event): Response
    {
        $this->initActiveFront();
        /** @var RouteMatch $route */
        $route = $this->getServiceManager()->get('application')->getMvcEvent()->getRouteMatch();
        $accessChecks = $route->getParam('access-checks');
        if (is_null($accessChecks)) {
            throw new \InvalidArgumentException('Access checks are not set for requested route');
        }

        try {
            $this->restoreUserToken();

            if (in_array(static::CHECK_API_ACCESS, $accessChecks)) {
                $this->company = $this->apiPermissionChecker()->checkApiAccess();
            }
            if (in_array(static::CHECK_ROBONOTE_API_ONLY_ACCESS, $accessChecks)) {
                $this->apiPermissionChecker()->checkRobonoteApiOnlyAccess();
            }
            if (in_array(static::CHECK_ROBONOTE_CHILD_API_ONLY_ACCESS, $accessChecks)) {
                $this->apiPermissionChecker()->checkRobonoteChildApiOnlyAccess();
            }
            if (in_array(static::CHECK_AUTHENTICATION, $accessChecks)) {
                $this->userPermissionChecker()->checkAuthentification();
            }
            if (in_array(static::CHECK_GLOBAL_ADMIN_ACCESS, $accessChecks)) {
                $this->userPermissionChecker()->checkGlobalAdminAccess();
            }
            if (in_array(static::CHECK_GLOBAL_ADMIN_OR_FRONT_GLOBAL_ADMIN_ACCESS, $accessChecks)) {
                $this->userPermissionChecker()->checkGlobalAdminOrFrontGlobalAdminAccess();
            }
            if (in_array(static::CHECK_COMPANY_ACCESS, $accessChecks)) {
                $this->checkCompanyAccess($route);
            }

            $this->checkFeaturesAccess($accessChecks);

            if (in_array(static::CHECK_ROLE_ACCESS, $accessChecks)) {
                $this->companyPermissionChecker()->checkRoleAccess();
            }
            if (in_array(static::CHECK_CALL_ACCESS, $accessChecks)) {
                $this->teamPermissionChecker()->checkCallAccess();
            }
            if (in_array(static::CHECK_USER_ACCESS, $accessChecks)) {
                $this->teamPermissionChecker()->checkUserAccess();
            }

            $result = parent::onDispatch($event);
        } catch (Throwable $e) {
            $appEnv = getenv('APP_ENV');
            if ($appEnv === 'console') {
                error_log($this->getErrorLogMessage($e));
            }

            $exceptionId = $this->roboTruck()->exceptionIdGenerator()->generatePseudoUniqueId();
            $this->roboTruck()->exceptionCollector()->collect($e, $exceptionId);

            $statusCode = $e->getCode();
            if ($statusCode > 599 || $statusCode < 100) {
                $statusCode = Response::STATUS_CODE_500;
            }

            //$this->getServiceManager()->get(\STCodeManagement\Service\AirbrakeService::class)->register($e);
            $debug = $this->configuration()->get('api')['debug'] ?? true;
            if ($debug) {
                $response = $this->getResponse()->setStatusCode($statusCode);
                throw $e;
            }
            $messages = is_array(json_decode($e->getMessage(), true)) ? json_decode(
                $e->getMessage(),
                true
            ) : $e->getMessage();
            $error = [
                'id' => $exceptionId,
                'code' => $statusCode,
                'messages' => $messages,
            ];
        } finally {
            $authUser = $this->auth()->isSigned() ? $this->auth()->getUser() : null;

            $this->roboTruck()->requestCollector()->collect(
                static::class,
                $event,
                $this->getApiParams(),
                $authUser,
                $this->company,
            );
        }
        if (isset($result) && $result instanceof Response) {
            return $result;
        }
        return $this->output([
            'result' => $result ?? null,
            'error' => $error ?? null,
        ], $statusCode ?? Response::STATUS_CODE_200);
    }

    /**
     *
     * @return void
     */
    public function initActiveFront(): void
    {
        $referer = $this->getRequest()->getHeaders()->has('referer') ? $this->getRequest()->getHeaders()->get(
            'referer'
        )->getFieldValue() : null;
        $refererUri = new \Laminas\Uri\Uri($referer);
        $this->front()->setDomain($refererUri->getHost());
    }

    /**
     *
     * @param array $params
     * @return Collection
     */
    public function setApiParams(array $params): Collection
    {
        $this->apiParams = new Collection($params);
        return $this->getApiParams();
    }

    /**
     *
     * @return void
     */
    protected function restoreUserToken(): void
    {
        $token = $this->getToken();
        $this->auth()->restore($token);
    }

    /**
     *
     * @return int|null
     */
    protected function getForcedRoleId(): ?int
    {
        return $this->getRequest()->getHeaders()->has('forced-role-id') ? (int) $this->getRequest()->getHeaders()->get(
            'forced-role-id'
        )->getFieldValue() : null;
    }

    /**
     *
     * @return string|null
     */
    protected function getToken(): ?string
    {
        return $this->getRequest()->getHeaders()->has('auth-token') ? $this->getRequest()->getHeaders()->get(
            'auth-token'
        )->getFieldValue() : null;
    }

    /**
     * @throws ValidationApiException
     */
    protected function validate(Validator $validator): void
    {
        $validator->validate();
        if ($validator->hasError()) {
            throw new ValidationApiException(json_encode($validator->getAllErrorsByGroups()));
        }
    }

    private function checkFeaturesAccess($accessChecks): void
    {
        if (in_array(static::CHECK_CLIENT_SUMMARY_ACCESS, $accessChecks, true)) {
            if (!$this->company->isClientSummaryEnabled()) {
                throw new ForbiddenApiException('Client summary is not enabled for this company');
            }
        }
    }

    private function checkCompanyAccess(RouteMatch $route): void
    {
        $this->company = $this->companyPermissionChecker()->getActiveCompany();
        $forcedRoleId = $this->getForcedRoleId();

        $user = $this->auth()->getUser();
        $role = $this->company()->user()->getUser($this->company->getId(), $user->getId())->getRole();

        if (
            $this->company instanceof Company
            && is_int($forcedRoleId)
            && $forcedRoleId > 0
            && $role->getId() !== $forcedRoleId
        ) {
            $requiredPermissions = $route->getParam('permissions') ?? [];

            // check if user has permissions to change role
            $route->setParam('permissions', [
                [
                    'permission' => \STCompany\Data\PermissionsTable::COMPANY_VIEW_LIKE_ANOTHER_ROLE,
                    'level' => \STCompany\Data\PermissionsTable::READ_PERMISSION,
                ]
            ]);
            $this->companyPermissionChecker()->checkRoleAccess();
            $permissionValue = $role->isAdmin() || $role->isCompanyAdmin()
                ? \STCompany\Data\PermissionsTable::WRITE_PERMISSION
                : $role->getPermissions()->offsetGet(
                    \STCompany\Data\PermissionsTable::COMPANY_VIEW_LIKE_ANOTHER_ROLE
                )->getAccessLevel();
            $this->company()->user()->setForcedRoleId($forcedRoleId);
            $this->company()->user()->setForcedReadOnlyPermissions(
                $permissionValue === \STCompany\Data\PermissionsTable::READ_PERMISSION
            );

            // set original permissions back
            $route->setParam('permissions', $requiredPermissions);
        }
    }
}
