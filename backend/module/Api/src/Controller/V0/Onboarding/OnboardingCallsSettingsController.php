<?php

declare(strict_types=1);

namespace Api\Controller\V0\Onboarding;

use Api\Controller\V0\BaseController;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;
use <PERSON><PERSON><PERSON>\Entity\Exception\NotFoundApiException;
use ST<PERSON>pi\Entity\Exception\ValidationApiException;
use STOnboarding\Validator\UpdateCallsSettingsValidator;

class OnboardingCallsSettingsController extends BaseController
{
    /**
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     * @throws NotFoundApiException
     * @throws ValidationApiException
     */
    public function updateCallsSettingsAction(): array
    {
        $externalId = $this->getApiParam('id');
        $callsSettings = $this->getApiParams()->toArray();

        /** @var UpdateCallsSettingsValidator $validator */
        $validator = $this->getServiceManager()->get(UpdateCallsSettingsValidator::class);
        $validator->setInstance([
            'external_id' => $externalId,
            'calls_settings' => $callsSettings,
        ]);
        $this->validate($validator);

        unset($callsSettings['id']);

        $this->onboarding()->onboardingFormSaver()->updateCallsSettings($externalId, $callsSettings);

        return [
            'form' => $this->onboarding()->onboardingFormSelector()->getFormByExternalId($externalId)->toArray(),
        ];
    }
}
