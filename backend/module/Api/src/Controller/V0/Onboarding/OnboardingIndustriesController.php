<?php

declare(strict_types=1);

namespace Api\Controller\V0\Onboarding;

use Api\Controller\V0\BaseController;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;
use <PERSON><PERSON><PERSON>\Entity\Exception\NotFoundApiException;
use ST<PERSON>pi\Entity\Exception\ValidationApiException;
use STIndustry\Validator\IndustryExistsValidator;
use STOnboarding\Validator\UpdateActiveIndustryValidator;

class OnboardingIndustriesController extends BaseController
{
    /**
     * @return array
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     * @throws ValidationApiException
     */
    public function getEventsAction(): array
    {
        $industryId = (int) $this->getApiParam('industry_id');

        /** @var IndustryExistsValidator $validator */
        $validator = $this->getServiceManager()->get(IndustryExistsValidator::class);
        $validator->setInstance($industryId);
        $this->validate($validator);

        $llmEventCollection = $this->onboarding()->onboardingIndustryLlmEventsSelector()->getPopularLlmEvents(
            $industryId
        );

        return [
            'events' => $llmEventCollection->toArray(),
        ];
    }

    /**
     * @throws NotFoundExceptionInterface
     * @throws NotFoundApiException
     * @throws ContainerExceptionInterface
     * @throws ValidationApiException
     */
    public function updateActiveIndustryAction(): array
    {
        $externalId = $this->getApiParam('id');
        $data = $this->getApiParams()->toArray();

        /** @var UpdateActiveIndustryValidator $validator */
        $validator = $this->getServiceManager()->get(UpdateActiveIndustryValidator::class);
        $validator->setInstance([
            'external_id' => $externalId,
            'data' => $data
        ]);
        $this->validate($validator);

        $this->onboarding()->onboardingFormSaver()->updateActiveIndustry($externalId, $data);

        return [
            'form' => $this->onboarding()->onboardingFormSelector()->getFormByExternalId($externalId)->toArray(),
        ];
    }
}
