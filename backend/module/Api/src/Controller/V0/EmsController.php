<?php

declare(strict_types=1);

namespace Api\Controller\V0;

use GuzzleHttp\Exception\GuzzleException;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;
use ST<PERSON><PERSON>\Entity\Exception\NotFoundApiException;
use ST<PERSON><PERSON>\Entity\Exception\ValidationApiException;
use STEms\Daemon\EmsDataSetAnalyzedCallsExamplesSearchDaemon;
use STEms\Daemon\EmsDataSetReviewedCallsExamplesSearchDaemon;
use STEms\Data\EmsDataSetsTable;
use STEms\Entity\DataSetExampleCollection;
use STEms\Entity\DataSet;
use STEms\Entity\DataSetExample;
use STEms\Entity\DataSetExampleEvent;
use STEms\Service\EmsDataSetExampleService;
use STEms\Validator\DataSetExampleEventValidator;
use STEms\Validator\DataSetExamplesSearchValidator;
use STEms\Validator\DataSetExamplesUploadValidator;
use STEms\Validator\DataSetExampleValidator;
use STEms\Validator\DataSetValidator;

class EmsController extends \Api\Controller\V0\BaseController
{
    use \STLib\Mvc\Hydrator\BaseHydratorTrait;

    /**
     * @return array
     * @throws NotFoundApiException
     */
    public function getDataSetAction(): array
    {
        return [
            'data_set' => $this->ems()->dataSet()->getDataSet($this->getApiParam('data_set_id'), $this->company->getId())->toArray(),
        ];
    }

    /**
     * @return array
     * @throws NotFoundApiException
     */
    public function getAnalyzedCallsEventsAction(): array
    {
        $dataSet = $this->ems()->dataSet()->getDataSet($this->getApiParam('data_set_id'));
        $eventId = $dataSet->getEventId();
        $event = $this->company()->event()->getEvent($this->company->getId(), $eventId);

        return [
            'events' => array_map(
                static function (array $event) {
                    unset($event['paragraphs'], $event['paragraphs_en']);
                    return $event;
                },
                $this->call()->event()->getEventsFromAnalyzedCalls(
                    $this->company,
                    $event
                )
            ),
        ];
    }

    /**
     * @return array
     */
    public function getDataSetByEventIdAction(): array
    {
        $eventId = (int) $this->getApiParam('event_id');
        $dataSet = $this->ems()->dataSet()->getDataSetByEventId($eventId, $this->company->getId());

        return [
            'data_set' => $dataSet?->toArray(),
        ];
    }

    /**
     * @return array
     * @throws NotFoundApiException
     */
    public function getDataSetExampleAction(): array
    {
        $dataSetExampleId = (string) $this->getApiParam('data_set_example_id');

        return [
            'data_set_example' => $this->ems()->dataSetExample()->getDataSetExample($dataSetExampleId, $this->company->getId())->toArray(),
        ];
    }

    /**
     * @return array
     * @throws NotFoundApiException
     */
    public function getDataSetExamplesAction(): array
    {
        $dataSetId = (string) $this->getApiParam('data_set_id');
        // check if user has access to data set
        $this->ems()->dataSet()->getDataSet($dataSetId, $this->company->getId());

        return [
            'data_set_examples' => $this->ems()->dataSetExample()->getDataSetExamples($dataSetId)->toArray(),
        ];
    }

    /**
     * @return array
     * @throws ContainerExceptionInterface
     * @throws NotFoundApiException
     * @throws NotFoundExceptionInterface
     * @throws ValidationApiException
     * @throws \JsonException
     * @throws \STApi\Entity\Exception\BadRequestApiException
     */
    public function createDataSetAction(): array
    {
        $companyId = $this->company->getId();
        $eventId = (int) $this->getApiParam('event_id');
        $isReopen = $this->hasApiParam('data_set_id');

        if ($isReopen) {
            $dataSet = $this->ems()->dataSet()->getDataSet((string) $this->getApiParam('data_set_id'), $this->company->getId());
            if ($dataSet->getStatus() !== EmsDataSetsTable::SENT_TO_TRAIN) {
                throw new \STApi\Entity\Exception\BadRequestApiException('Data set is not sent to train');
            }
        } else {
            // check if user has access to event
            $this->company()->event()->getEvent($companyId, $eventId);
            // check if data set exists for event
            $dataSet = $this->ems()->dataSet()->getDataSetByEventId($eventId, $this->company->getId());
            if ($dataSet instanceof DataSet) {
                throw new \STApi\Entity\Exception\BadRequestApiException('Data set is already exists');
            }
            $dataSet = $this->hydrate([
                'data_set_id' => uniqid('', true),
                'company_id' => $companyId,
                'user_id' => $this->auth()->getUser()->getId(),
                'name' => (string) $this->getApiParam('name'),
                'guideline' => (string) $this->getApiParam('guideline'),
                'event_id' => $eventId,
            ], DataSet::class);
        }

        $dataSet->setStatus(EmsDataSetsTable::NOT_SENT_TO_TRAIN);

        /** @var DataSetValidator $validator */
        $validator = $this->getServiceManager()->get(DataSetValidator::class);
        $validator->setInstance($dataSet);
        $validator->validate();

        if ($validator->hasError()) {
            throw new ValidationApiException(json_encode($validator->getAllErrorsByGroups()));
        }

        $this->ems()->dataSet()->saveDataSet($dataSet);

        return [
            'data_set_id' => $dataSet->toArray(),
        ];
    }

    /**
     * @return array
     * @throws ContainerExceptionInterface
     * @throws NotFoundApiException
     * @throws NotFoundExceptionInterface
     * @throws ValidationApiException
     */
    public function updateDataSetAction(): array
    {
        $dataSetId = $this->getApiParam('data_set_id');
        $dataSet = $this->ems()->dataSet()->getDataSet($dataSetId, $this->company->getId())
                ->setGuideline((string) $this->getApiParam('guideline'))
                ->setPositiveExampleText((string) $this->getApiParam('positive_example_text'))
                ->setPositiveExampleHighlight((string) $this->getApiParam('positive_example_highlight'))
                ->setNegativeExampleText((string) $this->getApiParam('negative_example_text'))
                ->setName((string) $this->getApiParam('name'));

        /** @var DataSetValidator $validator */
        $validator = $this->getServiceManager()->get(DataSetValidator::class);
        $validator->setInstance($dataSet);
        $validator->validate('name', 'guideline');
        if ($validator->hasError()) {
            throw new ValidationApiException(json_encode($validator->getAllErrorsByGroups()));
        }

        $this->ems()->dataSet()->saveDataSet($dataSet);

        return [
            'data_set' => $dataSet->toArray(),
        ];
    }

    /**
     * @return array
     * @throws NotFoundApiException
     * @throws \JsonException
     */
    public function runReviewedCallsExamplesSearchAction(): array
    {
        $dataSet = $this->ems()->dataSet()->getDataSet((string) $this->getApiParam('data_set_id'), $this->company->getId());

        $this->ems()->dataSet()->addDataSetToSearchQueue(
            $dataSet->getDataSetId(),
            EmsDataSetReviewedCallsExamplesSearchDaemon::QUEUE,
            EmsDataSetReviewedCallsExamplesSearchDaemon::EXAMPLES_LIMIT,
        );

        return [
            'data_set' => $dataSet->toArray(),
        ];
    }

    /**
     * @return array
     * @throws NotFoundApiException
     */
    public function runAnalyzedCallsExamplesSearchAction(): array
    {
        $dataSet = $this->ems()->dataSet()->getDataSet((string) $this->getApiParam('data_set_id'), $this->company->getId());

        /** @var DataSetValidator $validator */
        $validator = $this->getServiceManager()->get(DataSetExamplesSearchValidator::class);
        $validator->setInstance($this->getApiParams());
        $validator->validate();

        if ($validator->hasError()) {
            throw new ValidationApiException(json_encode($validator->getAllErrorsByGroups()));
        }

        $this->ems()->dataSet()->addDataSetToSearchQueue(
            $dataSet->getDataSetId(),
            EmsDataSetAnalyzedCallsExamplesSearchDaemon::QUEUE,
            (int) $this->getApiParam('examples_limit'),
        );

        return [
            'data_set' => $dataSet->toArray(),
        ];
    }

    /**
     * @return array
     * @throws NotFoundApiException
     * @throws \JsonException
     * @throws \STApi\Entity\Exception\BadRequestApiException
     */
    public function confirmDataSetAction(): array
    {
        $dataSetId = (string) $this->getApiParam('data_set_id');
        $dataSet = $this->ems()->dataSet()->getDataSet($dataSetId, $this->company->getId());

        if ($dataSet->getStatus() === EmsDataSetsTable::SENT_TO_TRAIN) {
            throw new \STApi\Entity\Exception\BadRequestApiException('Data set is already sent to train');
        }

//        $confirmedExamplesCount = $this->ems()->dataSetExample()->getExamplesCount($dataSetId, \STEms\Data\EmsDataSetExamplesTable::STATUS_CONFIRMED);
//        $neutralExamplesCount = $this->ems()->dataSetExample()->getExamplesCount($dataSetId, \STEms\Data\EmsDataSetExamplesTable::STATUS_NEUTRAL);
//        if ($confirmedExamplesCount < EmsDataSetExampleService::MINIMAL_CONFIRMED_EXAMPLES_COUNT_TO_CONFIRM) {
//            throw new \STApi\Entity\Exception\BadRequestApiException('Count of confirmed examples is less than minimal');
//        }
//        if ($neutralExamplesCount < EmsDataSetExampleService::MINIMAL_NEUTRAL_EXAMPLES_COUNT_TO_CONFIRM) {
//            throw new \STApi\Entity\Exception\BadRequestApiException('Count of neutral examples is less than minimal');
//        }

        $dataSet->setStatus(EmsDataSetsTable::SENT_TO_TRAIN);

        $this->ems()->dataSet()->saveDataSet($dataSet);

        $this->ems()->dataSet()->sendMessageOnDataSetConfirmed($dataSetId);

        return [
            'data_set' => $dataSet->toArray(),
        ];
    }

    /**
     * @return array
     * @throws NotFoundApiException
     * @throws ValidationApiException
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function saveDataSetExampleAction(): array
    {
        // check if user has access to data set
        $dataSetId = (string) $this->getApiParam('data_set_id');

        $initialDataSetExample = $this->hasApiParam('data_set_example_id')
            ? $this->ems()->dataSetExample()->getDataSetExample($this->getApiParam('data_set_example_id'), $this->company->getId())
            : null;
        $initialDataSetExampleArray = $initialDataSetExample
            ? $initialDataSetExample->toArray([
                'data_set_id',
                'data_set_example_id',
                'text',
                'en_text',
                'language',
                'call_id',
                'paragraph_number',
                'paragraph_start_time',
                'example_source',
                'status',
                'created_at',
                'updated_at',
                'is_deleted',
            ])
            : ['data_set_example_id' => uniqid('', true)]
        ;

        /* @var DataSetExample $dataSetExample */
        $dataSetExample = $this->hydrate(
            array_merge(
                $initialDataSetExampleArray,
                [
                    'data_set_id' => $dataSetId,
                    'language' => $this->getApiParam('language'),
                    'text' => $this->getApiParam('text'),
                    'en_text' => $this->getApiParam('en_text'),
                    'status' => $this->getApiParam('status'),
                ]
            ),
            DataSetExample::class
        );

        if ($initialDataSetExample !== null) {
            $dataSetExample->setEvents($initialDataSetExample->getEvents());
        }

        /** @var DataSetValidator $validator */
        $validator = $this->getServiceManager()->get(DataSetExampleValidator::class);
        $validator->setInstance($dataSetExample);
        $validator->validate();

        if ($validator->hasError()) {
            throw new ValidationApiException(json_encode($validator->getAllErrorsByGroups()));
        }

        $this->ems()->dataSetExample()->saveDataSetExample($dataSetExample);

        return [
            'data_set_example' => $dataSetExample->toArray(),
        ];
    }

    /**
     * @return array
     * @throws NotFoundApiException
     * @throws ValidationApiException
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function saveDataSetExampleEventAction(): array
    {
        // check if user has access to data set example event
        $dataSetExampleId = (string) $this->getApiParam('data_set_example_id');
        $this->ems()->dataSetExample()->getDataSetExample($dataSetExampleId, $this->company->getId());

        $initialDataSetExampleEventData = $this->hasApiParam('data_set_example_event_id')
            ? $this->ems()->dataSetExample()->getDataSetExampleEvent($this->getApiParam('data_set_example_event_id'))->toArray()
            : ['data_set_example_event_id' => uniqid('', true)];
        /* @var DataSetExampleEvent $dataSetExampleEvent */
        $dataSetExampleEvent = $this->hydrate(
            array_merge(
                $initialDataSetExampleEventData,
                [
                    'data_set_example_id' => $dataSetExampleId,
                    'event_id' => $this->getApiParam('event_id'),
                    'highlight' => (string) $this->getApiParam('highlight'),
                    'en_highlight' => (string) $this->getApiParam('en_highlight'),
                ]
            ),
            DataSetExampleEvent::class
        );

        /** @var DataSetExampleEventValidator $validator */
        $validator = $this->getServiceManager()->get(DataSetExampleEventValidator::class);
        $validator->setInstance($dataSetExampleEvent);
        $validator->validate();

        if ($validator->hasError()) {
            throw new ValidationApiException(json_encode($validator->getAllErrorsByGroups()));
        }

        $this->ems()->dataSetExample()->saveDataSetExampleEvent($dataSetExampleEvent);

        return [
            'data_set_example' => $dataSetExampleEvent->toArray(),
        ];
    }

    /**
     * @return array
     * @throws NotFoundApiException
     */
    public function deleteDataSetExampleEventAction(): array
    {
        $dataSetExampleEvent = $this->ems()->dataSetExample()->getDataSetExampleEvent(
            (string) $this->getApiParam('data_set_example_event_id')
        );

        $this->ems()->dataSetExample()->getDataSetExample($dataSetExampleEvent->dataSetExampleId, $this->company->getId());

        $dataSetExampleEvent->isDeleted(true);
        $this->ems()->dataSetExample()->saveDataSetExampleEvent($dataSetExampleEvent);

        return [
            'is_deleted' => true,
        ];
    }

    /**
     * @return array
     * @throws ContainerExceptionInterface
     * @throws NotFoundApiException
     * @throws NotFoundExceptionInterface
     * @throws ValidationApiException
     * @throws GuzzleException
     */
    public function uploadDataSetExamplesAction(): array
    {
        /** @var DataSetValidator $validator */
        $validator = $this->getServiceManager()->get(DataSetExamplesUploadValidator::class);
        $validator->setInstance($this->getApiParams());
        $validator->validate();

        if ($validator->hasError()) {
            throw new ValidationApiException(json_encode($validator->getAllErrorsByGroups()));
        }

        $dataSetId = (string) $this->getApiParam('data_set_id');
        $this->ems()->dataSet()->getDataSet($dataSetId, $this->company->getId());

        $examples = explode(PHP_EOL . PHP_EOL, $this->getApiParam('file_content'));
        $notImportedExamplesCount = 0;
        $language = $this->translator()->detectLanguage($examples[0]);

        $dataSetExamplesCollection = new DataSetExampleCollection();

        foreach ($examples as $example) {
            if (strlen($example) < EmsDataSetExampleService::CUSTOM_EXAMPLE_MIN_LENGTH) {
                $notImportedExamplesCount++;
                continue;
            }

            $dataSetExamplesCollection->add(
                $this->hydrate(
                    [
                        'data_set_example_id' => uniqid('', true),
                        'data_set_id' => $dataSetId,
                        'example_source' => DataSetExample::EXAMPLE_SOURCE_CUSTOM,
                        'language' => $language,
                        'text' => $example,
                    ],
                    DataSetExample::class
                )
            );
        }

        if (!$dataSetExamplesCollection->isEmpty()) {
            $this->ems()->dataSetExample()->saveDataSetExamples($dataSetExamplesCollection);
        }

        return [
            'imported_count' => count($examples) - $notImportedExamplesCount,
            'not_imported_count' => $notImportedExamplesCount,
        ];
    }

    /**
     * @return array
     * @throws NotFoundApiException
     */
    public function deleteDataSetAction(): array
    {
        $dataSet = $this->ems()->dataSet()->getDataSet((string) $this->getApiParam('data_set_id'), $this->company->getId());
        $dataSet->isDeleted(true);

        $this->ems()->dataSet()->saveDataSet($dataSet);

        return [
            'is_deleted' => true,
        ];
    }

    /**
     * @return array
     */
    public function deleteDataSetExamplesAction(): array
    {
        $this->ems()->dataSetExample()->deleteDataSetExamples(
            $this->getApiParam('data_set_id'),
            $this->getApiParam('statuses')
        );

        return [
            'is_deleted' => true,
        ];
    }

    /**
     * @return array
     */
    public function restoreDataSetExamplesAction(): array
    {
        $this->ems()->dataSetExample()->restoreDataSetExamples(
            $this->getApiParam('data_set_id'),
            $this->getApiParam('statuses'),
        );

        return [
            'is_deleted' => false,
        ];
    }

    /**
     * @return array
     * @throws NotFoundApiException
     */
    public function deleteDataSetExampleAction(): array
    {
        $dataSetExampleId = (string) $this->getApiParam('data_set_example_id');
        $dataSetExample = $this->ems()->dataSetExample()->getDataSetExample($dataSetExampleId, $this->company->getId());
        $dataSetExample->isDeleted(true);
        $this->ems()->dataSetExample()->saveDataSetExample($dataSetExample);

        return [
            'is_deleted' => true,
        ];
    }

    /**
     * @return array
     * @throws NotFoundApiException
     */
    public function restoreDataSetExampleAction(): array
    {
        $dataSetExampleId = (string) $this->getApiParam('data_set_example_id');
        $dataSetExample = $this->ems()->dataSetExample()->getDataSetExample(
            $dataSetExampleId,
            $this->company->getId(),
            true,
        );
        $dataSetExample->isDeleted(false);
        $this->ems()->dataSetExample()->saveDataSetExample($dataSetExample);

        return [
            'is_deleted' => false,
        ];
    }
}
