<?php

declare(strict_types=1);

namespace Api\Controller\V0\Admin;

use Api\Controller\V0\BaseController;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;
use STApi\Entity\Exception\ValidationApiException;
use STOnboarding\Validator\CreateOnboardingFormValidator;

class OnboardingFormController extends BaseController
{
    /**
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     * @throws ValidationApiException
     */
    public function createFormAction(): array
    {
        $companyName = empty($this->getApiParam('company_name')) ? null : $this->getApiParam('company_name');
        $frontFormPath = $this->getApiParam('front_form_path');
        $invitePath = $this->getApiParam('invite_path');

        /** @var CreateOnboardingFormValidator $validator */
        $validator = $this->getServiceManager()->get(CreateOnboardingFormValidator::class);
        $validator->setInstance([
            'front_form_path' => $frontFormPath,
            'invite_path' => $invitePath,
            'company_name' => $companyName
        ]);
        $this->validate($validator);

        $domain = $this->front()->getActiveFront()->getDomain();

        $form = $this->onboarding()->onboardingFormSaver()->createForm(
            $companyName,
            $domain,
            $frontFormPath,
            $invitePath
        );

        return [
            'form' => $form->toArray(),
        ];
    }

    /**
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function getFormsAction(): array
    {
        $forms = $this->onboarding()->onboardingFormSelector()->getForms();

        return $forms->toArray();
    }
}
