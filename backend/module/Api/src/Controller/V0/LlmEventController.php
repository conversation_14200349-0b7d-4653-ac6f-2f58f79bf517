<?php

declare(strict_types=1);

namespace Api\Controller\V0;

use Laminas\Stdlib\ResponseInterface;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;
use ST<PERSON><PERSON>\Entity\Exception\NotFoundApiException;
use ST<PERSON>pi\Entity\Exception\ThirdPartyApiException;
use STApi\Entity\Exception\ValidationApiException;
use STLlmEvent\Validator\DeleteLlmEventValidator;
use STLlmEvent\Validator\SaveLlmEventValidator;

class LlmEventController extends BaseController
{
    /**
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function getEventsAction(): array
    {
        return [
            'events' => $this->llmEvent()->llmEventSelector()->getLlmEvents()->toArray(),
        ];
    }

    /**
     * @return array
     * @throws ContainerExceptionInterface
     * @throws NotFoundApiException
     * @throws NotFoundExceptionInterface
     */
    public function getEventAction(): array
    {
        $id = (int) $this->getApiParam('id');

        return [
            'event' => $this->llmEvent()->llmEventSelector()->getLlmEvent($id)->toArray(),
        ];
    }

    /**
     * @return array
     * @throws ThirdPartyApiException
     */
    public function improveEventAction(): array|ResponseInterface
    {
        $name = $this->getApiParam('name');
        $description = $this->getApiParam('description');

        try {
            $result = $this->algo()->aiSolutionsCommutator()->improveLlmEvent($name, $description);
        } catch (ThirdPartyApiException $e) {
            $this->getResponse()->setStatusCode(502);
            $this->getResponse()->setContent(json_encode(['error' => ['messages' => $e->getMessage()]]));

            return $this->getResponse();
        }

        return [
            'event' => $result,
        ];
    }

    public function saveEventAction(): array
    {
        $id = $this->getApiParam('id');
        $name = $this->getApiParam('name');
        $description = $this->getApiParam('description');

        if (!is_null($id)) {
            $id = (int) $id;
        }

        /** @var SaveLlmEventValidator $validator */
        $validator = $this->getServiceManager()->get(SaveLlmEventValidator::class);
        $validator->setInstance(['id' => $id, 'name' => $name, 'description' => $description]);
        $validator->validate();
        if ($validator->hasError()) {
            throw new ValidationApiException(json_encode($validator->getAllErrorsByGroups()));
        }

        $event = $this->llmEvent()->llmEventSaver()->save($name, $description, $id);

        return [
            'event' => $event->toArray(),
        ];
    }

    /**
     * @throws ContainerExceptionInterface
     * @throws ValidationApiException
     * @throws NotFoundExceptionInterface
     */
    public function deleteEventAction(): array
    {
        $id = (int) $this->getApiParam('id');

        /** @var DeleteLlmEventValidator $validator */
        $validator = $this->getServiceManager()->get(DeleteLlmEventValidator::class);
        $validator->setInstance($id);
        $validator->validate();
        if ($validator->hasError()) {
            throw new ValidationApiException(json_encode($validator->getAllErrorsByGroups()));
        }

        $this->llmEvent()->llmEventRemover()->delete($id);

        return [
            'message' => 'Event ' . $id . ' has been successfully deleted',
        ];
    }
}
