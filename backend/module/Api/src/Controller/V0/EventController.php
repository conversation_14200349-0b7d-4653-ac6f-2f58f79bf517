<?php

declare(strict_types=1);

namespace Api\Controller\V0;

use Exception;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;
use ReflectionException;
use STApi\Entity\Exception\ValidationApiException;
use STCompany\Entity\Event\Event;
use STCompany\Validator\EventValidator;
use STIndustry\Data\IndustriesTable;
use STIndustry\Entity\Industry;
use STLib\Mvc\Hydrator\BaseHydratorTrait;

class EventController extends BaseController
{
    use BaseHydratorTrait;

    private const string INDUSTRY_NAME_LLM_FOR_FRONT = 'Custom Models';
    private const string ALGO_EVENT_TYPE_NER = 'ner';
    private const string ALGO_EVENT_TYPE_LLM = 'llm';

    /**
     * @return array
     * @throws Exception
     */
    public function getEventsAction(): array
    {
        $companyId = $this->company->getId();
        $categoryIds = $this->getApiParam('category_ids') ? (array) $this->getApiParam('category_ids') : null;
        $user = $this->company()->user()->getUser($companyId, $this->auth()->getUser()->getId());
        $roleId = $user->getRole()->getId();

        return [
            'events' => $this->company()->event()->getEvents($companyId, $categoryIds, $roleId)->toArray(),
        ];
    }

    /**
     * @return array
     */
    public function getEventAction(): array
    {
        return [
            'event' => $this->company()->event()->getEvent(
                $this->company->getId(),
                (int) $this->getApiParam('event_id')
            )->toArray(),
        ];
    }

    /**
     * @throws ReflectionException
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function getAlgoApisEventsAction(): array
    {
        $roleId = (int) $this->getApiParam('role_id');
        $usedAlgoEventNames = $this->company()->event()->getUsedAlgoEventNames($this->company->getId(), $roleId);
        $availableAlgoEvents = $this->algo()->algoEvent()->getAvailableNerAlgoEvents($this->company->getId());
        $directLlmEventsCollection = $this->company()->llmEventSelector()->getLlmEvents($this->company->getId());

        $industries = $this->company()->industrySelector()->getIndustries($this->company->getId());
        $industryIds = array_column($industries->toArray(), 'id');
        $industriesLlmEventsCollection = $this->industry()->llmEventSelector()->getLlmEvents($industryIds);

        $algoApis = $this->algo()->companyAlgoApiSelector()->getNerAlgoApis($this->company->getId());

        $result = [];
        foreach ($availableAlgoEvents as $availableAlgoEvent) {
            $algoApiId = $availableAlgoEvent->getAlgoApiId();
            $algoApi = $algoApis->offsetGet($algoApiId);

            if (!isset($result[$algoApiId])) {
                $result[$algoApiId] = $this->buildIndustryArray(
                    $algoApiId,
                    $algoApi->getName()
                );
            }

            $result[$algoApiId]['events'][] = $this->buildAlgoEventArray(
                $availableAlgoEvent->getAlgoEvent(),
                $usedAlgoEventNames,
                $availableAlgoEvent->getHint()
            );
        }

        if (!$directLlmEventsCollection->isEmpty()) {
            $llmIndustryData = $this->buildIndustryArray(
                IndustriesTable::LLM,
                self::INDUSTRY_NAME_LLM_FOR_FRONT
            );
            foreach ($directLlmEventsCollection as $llmEvent) {
                $llmEventName = $llmEvent->getName();

                $llmIndustryData['events'][] = $this->buildAlgoEventArray(
                    $llmEventName,
                    $usedAlgoEventNames,
                    $llmEvent->getDescription(),
                    self::ALGO_EVENT_TYPE_LLM,
                    $llmEvent->getId()
                );
            }

            $result[IndustriesTable::LLM] = $llmIndustryData;
        }

        if (!$industriesLlmEventsCollection->isEmpty()) {
            foreach ($industriesLlmEventsCollection as $industryLlmEvent) {
                /**
                 * @var Industry $industry
                 */
                $industry = $industries->offsetGet($industryLlmEvent->getIndustryId());
                $industryName = $industry->getName();

                if (!isset($result[$industryName])) {
                    $result[$industryName] = $this->buildIndustryArray(
                        $industry->getId(),
                        $industryName
                    );
                }

                $result[$industryName]['events'][] = $this->buildAlgoEventArray(
                    $industryLlmEvent->getName(),
                    $usedAlgoEventNames,
                    $industryLlmEvent->getDescription(),
                    self::ALGO_EVENT_TYPE_LLM,
                    $industryLlmEvent->getId()
                );
            }
        }

        return [
            'categories' => array_values($result),
        ];
    }

    /**
     * @return array
     * @throws ReflectionException
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function getAlgoEventsAction(): array
    {
        $roleId = (int) $this->getApiParam('role_id');
        $usedAlgoEventNames = $this->company()->event()->getUsedAlgoEventNames($this->company->getId(), $roleId);
        $availableAlgoEvents = $this->algo()->algoEvent()->getAvailableAlgoEvents($this->company->getId());
        $llmEventsCollection = $this->company()->llmEventSelector()->getLlmEvents($this->company->getId());

        $industries = $this->company()->industrySelector()->getIndustries($this->company->getId());
        $industryIds = array_column($industries->toArray(), 'id');
        $industriesLlmEventsCollection = $this->industry()->llmEventSelector()->getLlmEvents($industryIds);

        $result = [];
        foreach ($availableAlgoEvents as $availableAlgoEvent) {
            $industryId = $availableAlgoEvent->getIndustryId();

            if (!isset($result[$industryId])) {
                $result[$industryId] = $this->buildIndustryArray(
                    (int) $industryId,
                    $availableAlgoEvent->getIndustryName()
                );
            }

            $result[$industryId]['events'][] = $this->buildAlgoEventArray(
                $availableAlgoEvent->getAlgoEvent(),
                $usedAlgoEventNames,
                $availableAlgoEvent->getHint()
            );
        }

        if (!$llmEventsCollection->isEmpty()) {
            $llmIndustryData = $this->buildIndustryArray(
                IndustriesTable::LLM,
                self::INDUSTRY_NAME_LLM_FOR_FRONT
            );
            foreach ($llmEventsCollection as $llmEvent) {
                $llmEventName = $llmEvent->getName();

                $llmIndustryData['events'][] = $this->buildAlgoEventArray(
                    $llmEventName,
                    $usedAlgoEventNames,
                    $llmEvent->getDescription(),
                    self::ALGO_EVENT_TYPE_LLM,
                    $llmEvent->getId()
                );
            }

            $result[IndustriesTable::LLM] = $llmIndustryData;
        }

        if (!$industriesLlmEventsCollection->isEmpty()) {
            foreach ($industriesLlmEventsCollection as $industryLlmEvent) {
                /**
                 * @var Industry $industry
                 */
                $industry = $industries->offsetGet($industryLlmEvent->getIndustryId());
                $industryName = $industry->getName();

                if (!isset($result[$industryName])) {
                    $result[$industryName] = $this->buildIndustryArray(
                        $industry->getId(),
                        $industryName
                    );
                }

                $result[$industryName]['events'][] = $this->buildAlgoEventArray(
                    $industryLlmEvent->getName(),
                    $usedAlgoEventNames,
                    $industryLlmEvent->getDescription(),
                    self::ALGO_EVENT_TYPE_LLM,
                    $industryLlmEvent->getId()
                );
            }
        }

        return [
            'categories' => array_values($result),
        ];
    }

    private function buildIndustryArray(int $id, string $name): array
    {
        return [
            'id' => $id,
            'name' => $name,
            'events' => [],
        ];
    }

    private function buildAlgoEventArray(
        string $name,
        array $usedAlgoEventNames,
        ?string $hint,
        string $type = self::ALGO_EVENT_TYPE_NER,
        int $id = null
    ): array {
        $data = [
            'type' => $type,
            'name' => $name,
            'availabe' => !in_array($name, $usedAlgoEventNames),
            'hint' => $hint,
        ];
        if ($type === self::ALGO_EVENT_TYPE_LLM) {
            $data = array_merge(['id' => $id], $data);
        }

        return $data;
    }

    /**
     * @return array
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     * @throws ReflectionException
     * @throws ValidationApiException
     * @throws Exception
     */
    public function saveEventAction(): array
    {
        $eventId = (int) $this->getApiParam('event_id');
        $params = $this->getApiParams()->toArray();
        $params['name'] = trim($params['name']);
        /** @var Event $event */
        $event = $this->hydrate($params, Event::class, withConstructor: true);

        if (is_int($event->getCategoryId())) {
            $category = $this->company()->event()->getCategory($this->company->getId(), $event->getCategoryId());
            $event->getRole()->setId($category->getRole()->getId());
        }

        if ($eventId > 0) {
            // call to check access to event
            $this->company()->event()->getEvent($this->company->getId(), $eventId);
            $event->setId($eventId);
        }
        $event->getRole()->setCompanyId($this->company->getId());

        $validator = $this->getServiceManager()->get(EventValidator::class);
        $validator->setInstance($event);
        $validator->validate();
        if ($validator->hasError()) {
            throw new ValidationApiException(json_encode($validator->getAllErrorsByGroups()));
        }
        $savedEventId = $this->company()->event()->saveEvent($event);

        return [
            'event' => $this->company()->event()->getEvent($this->company->getId(), $savedEventId)->toArray(),
        ];
    }

    /**
     *
     * @return array
     */
    public function changeCategoryAction(): array
    {
        $eventId = (int) $this->getApiParam('event_id');
        $categoryId = (int) $this->getApiParam('category_id') > 0 ? (int) $this->getApiParam('category_id') : null;

        if (is_int($categoryId)) {
            // call to check access to category
            $this->company()->event()->getCategory($this->company->getId(), $categoryId);
        }

        $event = $this->company()->event()->getEvent($this->company->getId(), $eventId);
        $event->setCategoryId($categoryId);
        $savedEventId = $this->company()->event()->saveEvent($event);

        return [
            'event' => $this->company()->event()->getEvent($this->company->getId(), $savedEventId)->toArray(),
        ];
    }

    /**
     *
     * @return array
     */
    public function saveSearchWordsAction(): array
    {
        $eventId = (int) $this->getApiParam('event_id');
        // call to check access to event
        $event = $this->company()->event()->getEvent($this->company->getId(), $eventId);
        $searchWordId = (int) $this->getApiParam('search_word_id');
        $searchWordName = str_replace(['\\', '$', '%'], '', trim($this->getApiParam('name')));
        $companyId = $this->company->getId();

        /** @var \STCompany\Entity\Event\SearchWord $searchWord */
        $searchWord = $this->hydrate([
            'event_id' => $eventId,
            'name' => $searchWordName,
        ], \STCompany\Entity\Event\SearchWord::class);

        $validator = $this->getServiceManager()->get(\STCompany\Validator\EventSearchWordValidator::class);
        $validator->setInstance($searchWord);
        $validator->validate();
        if ($validator->hasError()) {
            throw new ValidationApiException(json_encode($validator->getAllErrorsByGroups()));
        }

        if ($searchWordId > 0) {
            $this->company()->event()->getSearchWord($companyId, $eventId, $searchWordId);
            $searchWord->setId($searchWordId);
        }

        $this->company()->event()->saveSearchWord($event->getRole(), $searchWord);
        return [
            'event' => $this->company()->event()->getEvent($this->company->getId(), $eventId)->toArray(),
        ];
    }

    /**
     *
     * @return array
     */
    public function deleteEventAction(): array
    {
        $eventId = (int) $this->getApiParam('event_id');
        // call to check access to event
        $event = $this->company()->event()->getEvent($this->company->getId(), $eventId);
        $this->company()->event()->deleteEvent($event->getRole(), $event->getId());

        return [
            'deleted' => true,
        ];
    }

    /**
     *
     * @return array
     */
    public function deleteSearchWordAction(): array
    {
        $eventId = (int) $this->getApiParam('event_id');
        $searchWordId = (int) $this->getApiParam('search_word_id');
        // call to check access to search word
        $searchWord = $this->company()->event()->getSearchWord($this->company->getId(), $eventId, $searchWordId);
        $event = $this->company()->event()->getEvent($this->company->getId(), $eventId);
        $this->company()->event()->deleteSearchWord($event->getRole(), $eventId, $searchWord->getId());

        return [
            'deleted' => true,
        ];
    }
}
