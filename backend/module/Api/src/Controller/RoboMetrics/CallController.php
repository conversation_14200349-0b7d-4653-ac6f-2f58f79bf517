<?php

declare(strict_types=1);

namespace Api\Controller\RoboMetrics;

use STCall\Entity\Call;
use STCall\Entity\ChatCall;
use STRoboMetrics\Request\Call\CallLogsRequest;
use STRoboMetrics\Request\Call\SummaryRequest;

class CallController extends BaseController
{
    /**
     *
     * @return array
     */
    public function getSummaryAction(): array
    {
        $summaryRequest = (new SummaryRequest())
                ->setCompanyId($this->hasApiParam('company_id') ? (int) $this->getApiParam('company_id') : null)
                ->setCallDatetimeParamDimension($this->getApiParam('call_datetime_param_dimension'))
                ->setTimeDimension($this->getApiParam('time_dimension'))
                ->setStartDate(\Carbon\Carbon::parse($this->getApiParam('start_date'))->startOfDay())
                ->setEndDate(\Carbon\Carbon::parse($this->getApiParam('end_date'))->endOfDay())
                ->setCallType(Call::CALL_TYPE)
                ->isUsingCompanyDimension((bool) $this->getApiParam('is_using_company_dimension'));
        return [
            'summary' => $this->roboMetrics()->call()->getCallsSummary($summaryRequest),
        ];
    }

    /**
     *
     * @return array
     */
    public function getChatCallsSummaryAction(): array
    {
        $summaryRequest = (new SummaryRequest())
                ->setCompanyId($this->hasApiParam('company_id') ? (int) $this->getApiParam('company_id') : null)
                ->setCallDatetimeParamDimension($this->getApiParam('call_datetime_param_dimension'))
                ->setTimeDimension($this->getApiParam('time_dimension'))
                ->setStartDate(\Carbon\Carbon::parse($this->getApiParam('start_date'))->startOfDay())
                ->setEndDate(\Carbon\Carbon::parse($this->getApiParam('end_date'))->endOfDay())
                ->isUsingCompanyDimension((bool) $this->getApiParam('is_using_company_dimension'))
                ->setCallType(ChatCall::CHAT_CALL_TYPE);
        return [
            'summary' => $this->roboMetrics()->call()->getChatCallsSummary($summaryRequest),
        ];
    }

    /**
     *
     * @return array
     */
    public function getCallLanguagesAction(): array
    {
        $request = (new \STRoboMetrics\Request\Call\CallLanguagesRequest())
                ->setCompanyId($this->hasApiParam('company_id') ? (int) $this->getApiParam('company_id') : null)
                ->setStartDate(\Carbon\Carbon::parse($this->getApiParam('start_date'))->startOfDay())
                ->setEndDate(\Carbon\Carbon::parse($this->getApiParam('end_date'))->endOfDay());
        return [
            'result' => $this->roboMetrics()->call()->getCallLanguages($request),
        ];
    }

    /**
     *
     * @return array
     */
    public function getCallLogsAction(): array
    {
        $request = (new CallLogsRequest())
                ->setCompanyId($this->hasApiParam('company_id') ? (int) $this->getApiParam('company_id') : null)
                ->setCallId($this->getApiParam('call_id'));

        return [
            'result' => $this->roboMetrics()->call()->getCallLogs($request),
        ];
    }

    /**
     *
     * @return array
     */
    public function getCallLogsByMessageAction(): array
    {
        $request = (new CallLogsRequest())
                ->setCompanyId($this->hasApiParam('company_id') ? (int) $this->getApiParam('company_id') : null)
                ->setCallId($this->getApiParam('call_id'));

        return [
            'result' => $this->roboMetrics()->call()->getCallLogsByMessage($request),
        ];
    }

    /**
     *
     * @return array
     */
    public function getBillingSummaryAction(): array
    {
        return [
            'result' => $this->roboMetrics()->call()->getBillingSummary(
                \Carbon\Carbon::parse($this->getApiParam('start_date'))->startOfDay(),
                \Carbon\Carbon::parse($this->getApiParam('end_date'))->endOfDay(),
            ),
        ];
    }

    /**
     * @return array
     */
    public function getUsageStatisticsAction(): array
    {
        $year = (int) $this->getApiParam('year');
        $month = $this->getApiParam('month');

        $startDate = \Carbon\Carbon::createFromFormat('F Y d', sprintf('%s %s 1', $month, $year))->startOfDay();
        $endDate = (clone $startDate)->endOfMonth()->endOfDay();

        return $this->roboMetrics()->call()->getUsageStatistics(
            $startDate,
            $endDate
        );
    }
}
