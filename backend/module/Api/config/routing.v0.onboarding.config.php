<?php

declare(strict_types=1);

namespace Api;

use Api\Controller\V0\Onboarding\OnboardingCallsSettingsController;
use Api\Controller\V0\Onboarding\OnboardingIndustriesController;
use Api\Controller\V0\Onboarding\OnboardingFormController;
use Api\Controller\V0\Onboarding\OnboardingUsersController;
use Laminas\Router\Http\Method;
use Laminas\Router\Http\Segment;

return [
    'router' => [
        'routes' => [
            'api' => [
                'type' => Segment::class,
                'options' => [
                    'route' => '/api[/]',
                    'defaults' => [
                        'controller' => Controller\V0\IndexController::class,
                        'action' => 'not-found',
                    ],
                ],
                'child_routes' => [
                    'v0' => [
                        'type' => Segment::class,
                        'options' => [
                            'route' => 'v0[/]',
                            'defaults' => [
                                'access-checks' => [
                                    Controller\V0\BaseController::CHECK_API_ACCESS,
                                    Controller\V0\BaseController::CHECK_ROBONOTE_API_ONLY_ACCESS
                                ],
                            ],
                        ],
                        'child_routes' => [
                            'onboarding' => [
                                'type' => Segment::class,
                                'options' => [
                                    'route' => 'onboarding[/]',
                                ],
                                'child_routes' => [
                                    'form' => [
                                        'type' => Segment::class,
                                        'options' => [
                                            'route' => 'form[/]',
                                        ],
                                        'child_routes' => [
                                            'form-id' => [
                                                'type' => Segment::class,
                                                'options' => [
                                                    'route' => ':id[/]',
                                                    'constraints' => [
                                                        'id' => '[^\/]*',
                                                    ],
                                                    'defaults' => [
                                                        'controller' => OnboardingFormController::class,
                                                        'access-checks' => [
                                                            Controller\V0\BaseController::CHECK_API_ACCESS,
                                                            Controller\V0\BaseController::CHECK_ROBONOTE_API_ONLY_ACCESS
                                                        ]
                                                    ],
                                                ],
                                                'child_routes' => [
                                                    'get-form' => [
                                                        'type' => Method::class,
                                                        'options' => [
                                                            'verb' => 'get',
                                                            'defaults' => [
                                                                'action' => 'get-form',
                                                            ],
                                                        ],
                                                    ],
                                                    'update-form' => [
                                                        'type' => Method::class,
                                                        'options' => [
                                                            'verb' => 'PUT',
                                                            'defaults' => [
                                                                'action' => 'update-form',
                                                            ],
                                                        ],
                                                    ],
                                                    'users' => [
                                                        'type' => Segment::class,
                                                        'options' => [
                                                            'route' => 'users[/]',
                                                            'defaults' => [
                                                                'controller' => OnboardingUsersController::class,
                                                                'access-checks' => [
                                                                    Controller\V0\BaseController::CHECK_API_ACCESS,
                                                                    Controller\V0\BaseController::CHECK_ROBONOTE_API_ONLY_ACCESS
                                                                ]
                                                            ],
                                                        ],
                                                        'child_routes' => [
                                                            'update-users' => [
                                                                'type' => Method::class,
                                                                'options' => [
                                                                    'verb' => 'POST',
                                                                    'defaults' => [
                                                                        'action' => 'update-users',
                                                                    ],
                                                                ],
                                                            ],
                                                        ],
                                                    ],
                                                    'active-industry' => [
                                                        'type' => Segment::class,
                                                        'options' => [
                                                            'route' => 'active-industry[/]',
                                                            'defaults' => [
                                                                'controller' => OnboardingIndustriesController::class,
                                                                'access-checks' => [
                                                                    Controller\V0\BaseController::CHECK_API_ACCESS,
                                                                    Controller\V0\BaseController::CHECK_ROBONOTE_API_ONLY_ACCESS
                                                                ]
                                                            ],
                                                        ],
                                                        'child_routes' => [
                                                            'update-active-industry' => [
                                                                'type' => Method::class,
                                                                'options' => [
                                                                    'verb' => 'POST',
                                                                    'defaults' => [
                                                                        'action' => 'update-active-industry',
                                                                    ],
                                                                ],
                                                            ],
                                                        ],
                                                    ],
                                                    'calls-settings' => [
                                                        'type' => Segment::class,
                                                        'options' => [
                                                            'route' => 'calls-settings[/]',
                                                            'defaults' => [
                                                                'controller' => OnboardingCallsSettingsController::class,
                                                                'access-checks' => [
                                                                    Controller\V0\BaseController::CHECK_API_ACCESS,
                                                                    Controller\V0\BaseController::CHECK_ROBONOTE_API_ONLY_ACCESS
                                                                ]
                                                            ],
                                                        ],
                                                        'child_routes' => [
                                                            'update-calls-settings' => [
                                                                'type' => Method::class,
                                                                'options' => [
                                                                    'verb' => 'POST',
                                                                    'defaults' => [
                                                                        'action' => 'update-calls-settings',
                                                                    ],
                                                                ],
                                                            ],
                                                        ],
                                                    ],
                                                    'submit-form' => [
                                                        'type' => Segment::class,
                                                        'options' => [
                                                            'route' => 'submit[/]',
                                                            'defaults' => [
                                                                'controller' => OnboardingFormController::class,
                                                                'access-checks' => [
                                                                    Controller\V0\BaseController::CHECK_API_ACCESS,
                                                                    Controller\V0\BaseController::CHECK_ROBONOTE_API_ONLY_ACCESS
                                                                ]
                                                            ],
                                                        ],
                                                        'child_routes' => [
                                                            'submit-form' => [
                                                                'type' => Method::class,
                                                                'options' => [
                                                                    'verb' => 'POST',
                                                                    'defaults' => [
                                                                        'action' => 'submit-form',
                                                                    ],
                                                                ],
                                                            ],
                                                        ],
                                                    ],
                                                    'need-help' => [
                                                        'type' => Segment::class,
                                                        'options' => [
                                                            'route' => 'need-help[/]',
                                                            'defaults' => [
                                                                'controller' => OnboardingFormController::class,
                                                                'access-checks' => [
                                                                    Controller\V0\BaseController::CHECK_API_ACCESS,
                                                                    Controller\V0\BaseController::CHECK_ROBONOTE_API_ONLY_ACCESS
                                                                ]
                                                            ],
                                                        ],
                                                        'child_routes' => [
                                                            'need-help' => [
                                                                'type' => Method::class,
                                                                'options' => [
                                                                    'verb' => 'POST',
                                                                    'defaults' => [
                                                                        'action' => 'need-help',
                                                                    ],
                                                                ],
                                                            ],
                                                        ],
                                                    ],
                                                ],
                                            ],
                                        ],
                                    ],
                                    'events' => [
                                        'type' => Segment::class,
                                        'options' => [
                                            'route' => 'events[/]',
                                            'defaults' => [
                                                'controller' => OnboardingIndustriesController::class,
                                                'access-checks' => [
                                                    Controller\V0\BaseController::CHECK_API_ACCESS,
                                                    Controller\V0\BaseController::CHECK_ROBONOTE_API_ONLY_ACCESS
                                                ]
                                            ],
                                        ],
                                        'child_routes' => [
                                            'get-events' => [
                                                'type' => Method::class,
                                                'options' => [
                                                    'verb' => 'get',
                                                    'defaults' => [
                                                        'action' => 'get-events',
                                                    ],
                                                ],
                                            ],
                                        ],
                                    ],
                                ],
                            ],
                        ],
                    ],
                ],
            ],
        ],
    ],
];
