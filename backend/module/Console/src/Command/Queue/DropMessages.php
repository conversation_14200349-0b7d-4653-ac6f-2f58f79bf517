<?php

declare(strict_types=1);

namespace Console\Command\Queue;

use Symfony\Component\Console\Input\InputOption;

class DropMessages extends \Console\Command\BaseCommand
{
    protected const DEFAULT_LIMIT = 1000;

    /**
     *
     * @var string
     */
    protected static $defaultName = 'queue:drop-messages';

    /**
     *
     * @var string
     */
    protected static $defaultDescription = 'Delete messages from queue by condition';

    /**
     *
     * @return void
     */
    protected function configure()
    {
        parent::configure();
        $this->addOption('queue', null, InputOption::VALUE_REQUIRED, 'queue');
        $this->addOption('conditions', null, InputOption::VALUE_IS_ARRAY | InputOption::VALUE_REQUIRED, 'conditions');
        $this->addOption('limit', null, InputOption::VALUE_OPTIONAL, 'limit', static::DEFAULT_LIMIT);
    }

    /**
     *
     * @param InputInterface $input
     * @param OutputInterface $output
     * @return int
     */
    protected function launch(\Symfony\Component\Console\Input\InputInterface $input, \Symfony\Component\Console\Output\OutputInterface $output): int
    {
        $queue = $input->getOption('queue');
        $conditions = $input->getOption('conditions');
        $limit = max((int) $input->getOption('limit'), 0);
        if (empty($queue) || empty($conditions)) {
            throw new \RuntimeException('Params "queue" and "conditions" are reqiured');
        }

        $parsedConditions = [];
        foreach ($conditions as $condition) {
            preg_match('/([^=]+)=([^=]+)/', $condition, $matches);
            if (!isset($matches[1]) || !isset($matches[2]) || empty($matches[1]) || empty($matches[2])) {
                throw new \RuntimeException('Invalid condition "' . $condition . '", correct format is param=value');
            }
            $parsedConditions[] = [
                'param' => $matches[1],
                'value' => $matches[2],
            ];
        }

        $channel = $this->rabbit()->getChannel();
        for ($i = 0; $i < $limit; $i++) {
            $data = $channel->basic_get($queue, true);
            try {
                if (is_null($data)) {
                    $output->writeln('Queue is empty');
                    break;
                }
                $messageBody = json_decode($data->body);
                $isMessageSuitByConditions = true;
                foreach ($parsedConditions as $parsedCondition) {
                    if (
                        !isset($messageBody->{$parsedCondition['param']})
                        || (string) $messageBody->{$parsedCondition['param']} !== $parsedCondition['value']
                    ) {
                        $isMessageSuitByConditions = false;
                    }
                }
                if ($isMessageSuitByConditions) {
                    $output->writeln('Deleted message is "' . json_encode($messageBody) . '"');
                } else {
                    $message = new \PhpAmqpLib\Message\AMQPMessage(json_encode($messageBody));
                    $channel->basic_publish($message, '', $queue);
                }
            } catch (\Exception $e) {
                $message = new \PhpAmqpLib\Message\AMQPMessage($data->body);
                $channel->basic_publish($message, '', $queue);
                throw $e;
            }
        }

        return static::SUCCESS;
    }
}
