<?php

declare(strict_types=1);

namespace Console\Command\Queue;

class RepeatQueueProcessingCommand extends \Console\Command\BaseCommand
{
    /**
     *
     * @var string
     */
    protected static $defaultName = 'queue:error:repeat';

    /**
     *
     * @var string
     */
    protected static $defaultDescription = 'Repeat processing of all messages in error queue';

    /**
     *
     * @return void
     */
    protected function configure()
    {
        $this->addOption('error_queue', null, \Symfony\Component\Console\Input\InputOption::VALUE_REQUIRED, 'Error queue name');
        $this->addOption('queue', null, \Symfony\Component\Console\Input\InputOption::VALUE_REQUIRED, 'Queue name');
    }

    /**
     *
     * @param InputInterface $input
     * @param OutputInterface $output
     * @return int
     */
    protected function launch(\Symfony\Component\Console\Input\InputInterface $input, \Symfony\Component\Console\Output\OutputInterface $output): int
    {
        $queue = $input->getOption('queue');
        $errorQueue = $input->getOption('error_queue');
        if (empty($queue) || empty($errorQueue)) {
            throw new \RuntimeException('Params "queue" and "error_queue" are reqiured');
        }

        $channel = $this->rabbit()->getChannel();
        $isQueueEmpty = false;
        while (!$isQueueEmpty) {
            $data = $channel->basic_get($errorQueue, true);
            if (is_null($data)) {
                $isQueueEmpty = true;
                continue;
            }
            $messageBody = json_decode($data->body);
            $messageBody->attempt = 0;
            unset($messageBody->error);
            $message = new \PhpAmqpLib\Message\AMQPMessage(json_encode($messageBody));
            $channel->basic_publish($message, '', $queue);
            $output->writeln('Added message is "' . json_encode($messageBody) . '"');
        }
        return static::SUCCESS;
    }
}
