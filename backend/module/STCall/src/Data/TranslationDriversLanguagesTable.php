<?php

declare(strict_types=1);

namespace STCall\Data;

use STTranslation\Service\Drivers\AmazonDriver;
use STTranslation\Service\Drivers\GoogleCloudDriver;
use STTranslation\Service\Drivers\GoogleDriver;
use STTranslation\Service\Drivers\AlgoDriver;

class TranslationDriversLanguagesTable extends AbstractDriversLanguagesTable
{
    public const array AVAILABLE_DRIVERS = [
        AmazonDriver::DRIVER_NAME,
        GoogleDriver::DRIVER_NAME,
        GoogleCloudDriver::DRIVER_NAME,
        AlgoDriver::DRIVER_NAME,
    ];
}
