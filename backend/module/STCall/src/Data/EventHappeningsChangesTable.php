<?php

declare(strict_types=1);

namespace STCall\Data;

use <PERSON><PERSON>all\Entity\Call;

class EventHappeningsChangesTable extends \STClickhouse\Entity\BaseTable
{
    /**
     *
     * @param \STCompany\Entity\Company $company
     * @param string|array $callId
     * @param int|array $roleId
     * @return array
     */
    public function getEventChanges(\STCompany\Entity\Company $company, string|array $callId, int|array $roleId): array
    {
        $callIds = is_string($callId) ? [$callId] : $callId;
        $roleIds = is_int($roleId) ? [$roleId] : $roleId;
        $sql = '
            SELECT
                ehc.company_id company_id,
                ehc.call_id call_id,
                ehc.paragraph_number paragraph_number,
                ehc.role_id role_id,
                ehc.original_event_id original_event_id,
                ehc.corrected_event_id corrected_event_id,
                ehc.user_id user_id,
                ehc.event_highlight event_highlight,
                ehc.event_en_highlight event_en_highlight,
                ehc.created created,
                u.user_name user_name
            FROM
                event_happenings_changes ehc
            INNER JOIN
                dictionary(users) u
                ON ehc.user_id = u.user_id
            LEFT JOIN
                (
                    SELECT DISTINCT
                        event_id,
                        role_id
                    FROM
                        dictionary(events)
                    GROUP BY
                        event_id,
                        role_id
                ) ce
                ON ce.event_id = ehc.corrected_event_id
            LEFT JOIN
                (
                    SELECT DISTINCT
                        event_id,
                        role_id
                    FROM
                        dictionary(events)
                    GROUP BY
                        event_id,
                        role_id
                ) oe
                ON oe.event_id = ehc.original_event_id
            WHERE
                call_id IN (\'' . implode('\',\'', $callIds) . '\')
                AND ehc.role_id IN (' . implode(',', $roleIds) . ')
                AND ehc.company_id = ' . $company->getId() . '
                AND (ce.role_id IN (' . implode(',', $roleIds) . ') OR ehc.corrected_event_id = 0)
                AND (oe.role_id IN (' . implode(',', $roleIds) . ') OR ehc.original_event_id = 0)
            ORDER BY
                created
        ';
        return $this->getClient()->selectAll($sql);
    }

    /**
     *
     * @param array $eventHappeningChangings
     * @return int
     */
    public function saveEventHappeningChange(array $eventHappeningChangings): int
    {
        $data = [];
        foreach ($eventHappeningChangings as $eventHappeningChanging) {
            $data[] = [
                'company_id' => $eventHappeningChanging->getCompany()->getId(),
                'call_id' => $eventHappeningChanging->getCallId(),
                'paragraph_number' => $eventHappeningChanging->getParagraphNumber(),
                'original_event_id' => $eventHappeningChanging->getOriginalEvent() instanceof \STCompany\Entity\Event\Event
                    ? $eventHappeningChanging->getOriginalEvent()->getId()
                    : 0,
                'corrected_event_id' => $eventHappeningChanging->getCorrectedEvent() instanceof \STCompany\Entity\Event\Event
                    ? $eventHappeningChanging->getCorrectedEvent()->getId()
                    : 0,
                'user_id' => $eventHappeningChanging->getUser()->getId(),
                'role_id' => $eventHappeningChanging->getRole()->getId(),
                'event_highlight' => $eventHappeningChanging->getEventHighlight(),
                'event_en_highlight' => $eventHappeningChanging->getEventEnHighlight(),
            ];
        }
        $columns = array_keys(current($data));
        $this->getClient()->insert($this->getTableName(), $data, $columns);
        return count($eventHappeningChangings);
    }

    /**
     *
     * @param Call $call
     * @return void
     */
    public function deleteByCall(Call $call): void
    {
        $this->getClient()->softDelete(
            $this->getTableName(),
            [
                [
                    'call_id',
                    '=',
                    $call->getId(),
                ],
                [
                    'company_id',
                    '=',
                    $call->getCompanyId(),
                ],
            ]
        );
    }
}
