<?php
// phpcs:ignoreFile

declare(strict_types=1);

namespace STCall\Data;

use STCompany\Entity\Company;

class EventsRepository extends \STClickhouse\Entity\BaseTable
{
    use \STClickhouse\Data\QueriesTrait;

    public const SEARCH_WORDS_SOURCE_EVENT = 'search_words';
    public const ALGO_EVENTS_SOURCE_EVENT = 'algo_events';

    /**
     *
     * @param Company $company
     * @param string|array $callId
     * @param int|array $roleId
     * @return array
     */
    public function getEvents(Company $company, string|array $callId, int|array $roleId): array
    {
        $callIds = is_string($callId) ? [$callId] : $callId;
        $roleIds = is_int($roleId) ? [$roleId] : $roleId;
        $sql = '
            SELECT
                e.event_id,
                any(e.role_id) role_id,
                e.call_id,
                e.paragraph_number,
                any(e.main_point_phrase) main_point_phrase,
                any(e.en_main_point_phrase) en_main_point_phrase,
                any(e.score) score,
                any(e.event_source) event_source
            FROM
            (
                ' . $this->getAlgoEventsSql($company, $roleIds, $callIds) . '
                UNION ALL
                ' . $this->getSearchWordsEventsSql($company, $roleIds, $callIds) . '
            ) e
            GROUP BY
                e.event_id,
                e.call_id,
                e.paragraph_number
        ';
        return $this->getClient()->selectAll($sql);
    }

    /**
     *
     * @param Company $company
     * @param \STCompany\Entity\Event\Event $event
     * @param \Carbon\Carbon|null $fromDate
     * @param int $limit
     * @return array
     */
    public function getEventsFromAnalyzedCalls(
        Company $company,
        \STCompany\Entity\Event\Event $event,
        \Carbon\Carbon $fromDate = null,
        int $limit = 10,
    ): array {
        $callsIds = $this->getCallIdsWithAlgoEvents($company, $event, $fromDate, $limit);

        if (empty($callsIds)) {
            return [];
        }

        $algoEvents = $event->getAlgoEvents();
        $searchWords = array_column($event->getSearchWord()->toArray(), 'name');

        $basicRequestsSqlParts = [];
        if (!empty($algoEvents)) {
            $basicRequestsSqlParts[] = $this->getEventsByAlgoEventSql($company, $callsIds, $algoEvents);
        }
        if (!empty($searchWords)) {
            $basicRequestsSqlParts[] = $this->getEventsBySearchWordsSql($company, $callsIds, $searchWords);
        }
        if (empty($basicRequestsSqlParts)) {
            return [];
        }
        $basicRequestsSql = implode(' UNION ALL ', $basicRequestsSqlParts);

        $sql = <<<SQL
            SELECT
                event_highlight,
                event_en_highlight,
                paragraph_number,
                events.call_id call_id,
                paragraphs,
                paragraphs_en,
                calls.call_language language,
                arrayElement(paragraphs, events.paragraph_number + 1) event_text,
                arrayElement(paragraphs_en, events.paragraph_number + 1) event_en_text,
                arrayElement(paragraphs_start_time, events.paragraph_number + 1) paragraph_start_time,
                array(
                    map
                    (
                         'paragraph', toString(paragraph_number),
                         'event_id', toString({$event->getId()}),
                         'event_highlight', event_highlight,
                         'event_en_highlight', event_en_highlight
                    )
                ) additional_events
            FROM (
                {$basicRequestsSql}
            ) events
            INNER JOIN (
                SELECT
                    call_id,
                    groupArray(decrypt('aes-256-ofb', text, '{$company->getEncryptionKey()}')) paragraphs,
                    groupArray(decrypt('aes-256-ofb', en_text, '{$company->getEncryptionKey()}')) paragraphs_en,
                    groupArray(start_time) paragraphs_start_time
                FROM
                (
                    {$this->getFinalTableSqlUsingGroupBy(
                        'calls_paragraphs',
                        [
                            'company_id',
                            'call_id',
                            'paragraph_number',
                        ],
                        'created',
                        [
                            'text',
                            'en_text',
                            'start_time',
                        ],
                        [
                            'call_id' => $callsIds,
                            'company_id' => $company->getId(),
                        ]
                    )}
                    ORDER BY
                        paragraph_number
                )
                GROUP BY
                    call_id
            ) cp
            ON cp.call_id = events.call_id            
            INNER JOIN (
                SELECT
                    call_language,
                    call_id
                FROM
                (
                    {$this->getFinalTableSqlUsingGroupBy(
                        'calls',
                        [
                            'company_id',
                            'call_id'
                        ],
                        'created',
                        [
                            'call_language',
                        ],
                        [
                            'call_id' => $callsIds,
                            'company_id' => $company->getId(),
                        ]
                    )}
                )
            ) calls
            ON calls.call_id = events.call_id
            LIMIT {$limit}
        SQL;

        return $this->getClient()->selectAll($sql);
    }

    /**
     *
     * @param Company $company
     * @param array $roleIds
     * @param array $callIds
     * @return string
     */
    protected function getSearchWordsEventsSql(
        Company $company,
        array $roleIds,
        array $callIds,
    ): string {
        $sql = '
            SELECT
                event_id,
                role_id,
                1 score,
                call_id,
                paragraph_number,
                search_words[arrayFirst(x -> 1, match_index)] main_point_phrase,
                main_point_phrase en_main_point_phrase,
                \'' . static::SEARCH_WORDS_SOURCE_EVENT . '\' event_source
            FROM
            (
                SELECT 
                    *,
                    multiMatchAllIndices(needle, regex) AS match_index
                FROM
                (
                    SELECT
                        e.event_id,
                        e.role_id,
                        cp.call_id,
                        cp.paragraph_number,
                        decrypt(\'aes-256-ofb\', text, \'' . $company->getEncryptionKey() . '\') text,
                        decrypt(\'aes-256-ofb\', en_text, \'' . $company->getEncryptionKey() . '\') en_text,
                        search_words,
                        lower(concat(text, \' \', COALESCE(en_text, \'\'))) needle,
                        multiSearchFirstPosition(needle, e.search_words) search_index,
                        e.regex
                    FROM
                    (
                        SELECT
                            event_id,
                            role_id,
                            groupUniqArray(search_word_name) search_words,
                            splitByChar(\'%\', concat(\'(^|\W)\', arrayStringConcat(search_words, \'(\W|$)%(^|\W)\'), \'(\W|$)\')) regex
                        FROM
                            dictionary(events) e
                        WHERE
                            search_word_name IS NOT NULL
                            AND role_id IN (' . implode(',', $roleIds) . ')
        ';

        $sql .= '
                    GROUP BY
                            event_id,
                            role_id
                    ) e
                    CROSS JOIN
                    (
                        SELECT
                            cp.call_id,
                            cp.paragraph_number,
                            cp.text,
                            cp.en_text
                        FROM
                        (
                            '
                                .  $this->getFinalTableSqlUsingFinal('calls_paragraphs') . '
                        ) cp
                        WHERE
                            company_id = ' . $company->getId() . '
                            AND call_id IN (\'' . implode('\',\'', $callIds) . '\')
                    ) cp
                    WHERE 
                        search_index > 0
                )
            WHERE
                notEmpty(match_index)
            )
        ';
        return $sql;
    }

    /**
     *
     * @param Company $company
     * @param array $roleIds
     * @param array $callIds
     * @return string
     */
    protected function getAlgoEventsSql(
        Company $company,
        array $roleIds,
        array $callIds,
    ): string {
        $sql = '
            SELECT
                DISTINCT
                    e.event_id event_id,
                    e.role_id role_id,
                    cae.score score,
                    cae.call_id,
                    cae.paragraph_number,
                    decrypt(\'aes-256-ofb\', cae.main_point_phrase, \'' . $company->getEncryptionKey() . '\') main_point_phrase,
                    decrypt(\'aes-256-ofb\', cae.en_main_point_phrase, \'' . $company->getEncryptionKey() . '\') en_main_point_phrase,
                    \'' . static::ALGO_EVENTS_SOURCE_EVENT . '\' event_source
                FROM
                    (
                        ' . $this->getFinalTableSqlUsingGroupBy('calls_algo_events', [
                            'company_id',
                            'call_id',
                            'paragraph_number',
                            'algo_api_id',
                            'event',
                        ], 'created', [
                            'score',
                            'main_point_phrase',
                            'en_main_point_phrase',
                        ], $this->getCallsAlgoEventsWhereConditions($company, $callIds)) . '
                    ) cae
                INNER JOIN
                    (
                        SELECT
                            *
                        FROM
                            dictionary(events)
        ';
        if (count($roleIds) > 0) {
            $sql .= '
                        WHERE
                            role_id IN (' . implode(',', $roleIds) . ')
            ';
        }
        $sql .= '
                    ) e
                    ON lower(cae.event) = lower(e.algo_event)
                ';
        return $sql;
    }

    /**
     *
     * @param Company $company
     * @param array $callIds
     * @return array
     */
    protected function getCallsAlgoEventsWhereConditions(Company $company, array $callIds): array
    {
        $result = [
            'company_id' => $company->getId(),
            [
                'type' => 'expression',
                'value' => 'score > ' . $company->getThresholdBar()
            ],
            [
                'type' => 'compare',
                'column' => 'event',
                'value' => CallsAlgoEventsTable::NEUTRAL_EVENT,
                'compare' => '<>',
            ],
        ];
        if (!empty($callIds)) {
            $result['call_id'] = $callIds;
        }
        return $result;
    }

    /**
     * @param Company $company
     * @param array $callsIds
     * @param array $searchWords
     * @return string
     */
    private function getEventsBySearchWordsSql(Company $company, array $callsIds, array $searchWords): string
    {
        $searchWordsToMatch = addslashes(implode('|', $searchWords));
        $highlightMatch = '';
        $highlightEnMatch = '';

        foreach ($searchWords as $searchWord) {
            $searchWord = addslashes($searchWord);

            $highlightMatch .= sprintf(
                'WHEN match(text, \'(?i)(^|\\W)%s($|\\W)\') THEN \'%s\'' . PHP_EOL,
                $searchWord,
                $searchWord
            );
            $highlightEnMatch .= sprintf(
                'WHEN match(en_text, \'(?i)(^|\\W)%s($|\\W)\') THEN \'%s\'' . PHP_EOL,
                $searchWord,
                $searchWord
            );
        }

        return <<<SQL
            SELECT
                call_id,
                paragraph_number,
                decrypt('aes-256-ofb', text, '{$company->getEncryptionKey()}') text,
                decrypt('aes-256-ofb', en_text, '{$company->getEncryptionKey()}') en_text,
                CASE {$highlightMatch} END AS event_highlight,
                CASE {$highlightEnMatch} END AS event_en_highlight
            FROM
                ({$this->getFinalTableSqlUsingGroupBy(
                    'calls_paragraphs',
                    [
                        'company_id',
                        'call_id',
                        'paragraph_number',
                    ],
                    'created',
                    [
                        'text',
                        'en_text',
                        'start_time',
                    ],
                    [
                        'call_id' => $callsIds,
                        'company_id' => $company->getId(),
                        [
                            'type' => 'expression',
                            'value' => <<<SQL
                                match(
                                    decrypt('aes-256-ofb', text, '{$company->getEncryptionKey()}'), '(?i)(^|\\W)({$searchWordsToMatch})($|\\W)'
                                ) != 0
                                OR 
                                match(
                                    decrypt('aes-256-ofb', en_text, '{$company->getEncryptionKey()}'), '(?i)(^|\\W)({$searchWordsToMatch})($|\\W)'
                                ) != 0
                            SQL,
                        ],
                    ]
                )})
            GROUP BY 
                call_id, 
                paragraph_number, 
                text, 
                en_text
            SQL;
    }

    /**
     * @param Company $company
     * @param array $callsIds
     * @param array $algoEvents
     * @return string
     */
    private function getEventsByAlgoEventSql(Company $company, array $callsIds, array $algoEvents): string
    {
        return <<<SQL
            SELECT
                call_id,
                paragraph_number,
                '' text,
                '' en_text,
                decrypt('aes-256-ofb', main_point_phrase, '{$company->getEncryptionKey()}') event_highlight,
                decrypt('aes-256-ofb', en_main_point_phrase, '{$company->getEncryptionKey()}') event_en_highlight
            FROM
                ({$this->getFinalTableSqlUsingGroupBy(
                    'calls_algo_events',
                    [
                        'company_id',
                        'call_id',
                        'paragraph_number',
                        'algo_api_id',
                        'event',
                    ],
                    'created',
                    [
                        'main_point_phrase',
                        'industry_id',
                        'call_time',
                        'score',
                        'main_point_phrase',
                        'en_main_point_phrase',
                        'created',
                    ],
                    [
                        'call_id' => $callsIds,
                        'company_id' => $company->getId(),
                        'event' => $algoEvents,

                    ]
                )})
            GROUP BY 
                call_id, 
                paragraph_number, 
                text, 
                en_text, 
                main_point_phrase, 
                en_main_point_phrase
        SQL;
    }

    /**
     *
     * @param Company $company
     * @param \STCompany\Entity\Event\Event $event
     * @param \Carbon\Carbon|null $fromDate
     * @param int $limit
     * @return array
     */
    private function getCallIdsWithAlgoEvents(
        Company $company,
        \STCompany\Entity\Event\Event $event,
        \Carbon\Carbon $fromDate = null,
        int $limit = 10,
    ): array {
        $fromDateSql = $fromDate ? ' AND created > \'' . $fromDate . '\'' : '';
        $inAlgoEventsSql = '\'' . implode('\',\'', $event->getAlgoEvents()) . '\'';
        $searchWords = array_column($event->getSearchWord()->toArray(), 'name');

        $callsIdsSql = <<<SQL
            SELECT
                DISTINCT call_id
            FROM
                calls_algo_events
            WHERE 
                company_id = {$company->getId()}
                AND event IN ({$inAlgoEventsSql})
                {$fromDateSql}
            LIMIT {$limit}     
        SQL;

        if (!empty($searchWords)) {
            $searchWordsToMatch = addslashes(implode('|', $searchWords));

            $callsIdsSql .= <<<SQL
                UNION ALL
                SELECT
                    DISTINCT call_id
                FROM
                    calls_paragraphs
                WHERE 
                    company_id = {$company->getId()}
                    AND (
                        match(
                            decrypt('aes-256-ofb', text, '{$company->getEncryptionKey()}'), '(?i)(^|\\s)({$searchWordsToMatch})(\\s|$)'
                        ) != 0
                        OR
                        match(
                            decrypt('aes-256-ofb', en_text, '{$company->getEncryptionKey()}'), '(?i)(^|\\s)({$searchWordsToMatch})(\\s|$)'
                        ) != 0
                    )
                    {$fromDateSql}
                LIMIT {$limit}   
            SQL;
        }

        return $this->getClient()->selectColumn($callsIdsSql, 'call_id');
    }
}
