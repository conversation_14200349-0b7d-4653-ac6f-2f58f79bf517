<?php

declare(strict_types=1);

namespace STCall\Entity;

use STLib\Expand\Collection;
use Traversable;

/**
 * @extends Traversable<array-key,AlgoEvent>
 */
class AlgoEventCollection extends Collection
{
    /**
     * @param mixed $algoEvent
     * @param string|int|null $key
     * @return Collection
     */
    public function add(mixed $algoEvent, string|int|null $key = null): Collection
    {
        if (!($algoEvent instanceof AlgoEvent)) {
            throw new \RuntimeException('AlgoEvent must be an instace of "\STCall\Entity\AlgoEvent"');
        }
        parent::add($algoEvent, $key ?? $algoEvent->getAlgoApiId() . '-' . $algoEvent->getCallId() . '-' . $algoEvent->getParagraphNumber() . '-' . $algoEvent->getEvent());
        return $this;
    }

    /**
     *
     * @return array
     */
    public function toArray(): array
    {
        $result = [];
        foreach ($this as $algoEvent) {
            $result[] = $algoEvent->toArray();
        }
        return $result;
    }
}
