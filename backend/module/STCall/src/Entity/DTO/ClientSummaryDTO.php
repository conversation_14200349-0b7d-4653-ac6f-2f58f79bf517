<?php

declare(strict_types=1);

namespace STCall\Entity\DTO;

class ClientSummaryDTO
{
    public function __construct(
        public readonly string $primaryPurpose = '',
        public readonly string $dealSize = '',
        public readonly array $timeline = [],
        public readonly string $dealStatus = '',
        public readonly string $nextStep = '',
        public readonly string $customerSentiment = '',
        public readonly array $clientDiscoveryParameters = [],
        public readonly array $customerProblems = [],
        public readonly array $businessOpportunities = [],
        public readonly string $risks = '',
        public readonly string $agentPerformance = '',
        public readonly array $interactionNotes = []
    ) {
    }

    public static function fromArray(array $data): self
    {
        return new self(
            primaryPurpose: $data['primary_purpose'] ?? '',
            dealSize: $data['deal_size'] ?? '',
            timeline: $data['timeline'] ?? [],
            dealStatus: $data['deal_status'] ?? '',
            nextStep: $data['next_step'] ?? '',
            customerSentiment: $data['customer_sentiment'] ?? '',
            clientDiscoveryParameters: $data['client_discovery_parameters'] ?? [],
            customerProblems: $data['customer_problems'] ?? [],
            businessOpportunities: $data['business_opportunities'] ?? [],
            risks: $data['risks'] ?? '',
            agentPerformance: $data['agent_performance'] ?? '',
            interactionNotes: $data['interaction_notes'] ?? []
        );
    }
}
