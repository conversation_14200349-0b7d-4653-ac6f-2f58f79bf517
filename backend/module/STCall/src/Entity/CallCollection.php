<?php

declare(strict_types=1);

namespace STCall\Entity;

class CallCollection extends \STLib\Expand\Collection
{
    /**
     *
     * @param mixed $call
     * @param string|int|null $key
     * @return \STLib\Expand\Collection
     * @throws \RuntimeException
     */
    public function add(mixed $call, string|int|null $key = null): \STLib\Expand\Collection
    {
        if (!($call instanceof Call)) {
            throw new \RuntimeException('Call must be an instace of "\STCall\Entity\Call"');
        }
        parent::add($call, $key ?? $call->getId());
        return $this;
    }

    /**
     *
     * @return array
     */
    public function getCallIds(): array
    {
        $result = [];
        foreach ($this as $call) {
            $result[] = $call->getId();
        }
        return $result;
    }

    /**
     *
     * @return array
     */
    public function toArray(): array
    {
        $result = [];
        foreach ($this as $call) {
            $result[] = $call->toArray();
        }
        return $result;
    }
}
