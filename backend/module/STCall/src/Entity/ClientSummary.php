<?php

declare(strict_types=1);

namespace STCall\Entity;

use Carbon\Carbon;
use STLib\Mvc\Hydrator\BaseHydratorTrait;

class ClientSummary
{
    use BaseHydratorTrait;

    /**
     * @var int
     */
    protected int $companyId;

    /**
     * @var string
     */
    protected string $clientId;

    /**
     * @var string
     */
    protected string $primaryPurpose;

    /**
     * @var string
     */
    protected string $dealSize;

    /**
     * @var array
     */
    protected array $timeline = [];

    /**
     * @var string
     */
    protected string $dealStatus;

    /**
     * @var string
     */
    protected string $nextStep;

    /**
     * @var string
     */
    protected string $customerSentiment;

    /**
     * @var array
     */
    protected array $clientDiscoveryParameters = [];

    /**
     * @var array
     */
    protected array $customerProblems = [];

    /**
     * @var array
     */
    protected array $businessOpportunities = [];

    /**
     * @var string
     */
    protected string $risks;

    /**
     * @var string
     */
    protected string $agentPerformance;

    /**
     * @var array
     */
    protected array $interactionNotes = [];

    /**
     * @var Carbon
     */
    protected Carbon $lastCallTime;

    /**
     * @var Carbon|null
     */
    protected ?Carbon $created = null;

    /**
     * @return int
     */
    public function getCompanyId(): int
    {
        return $this->companyId;
    }

    /**
     * @param int $companyId
     * @return ClientSummary
     */
    public function setCompanyId(int $companyId): ClientSummary
    {
        $this->companyId = $companyId;
        return $this;
    }

    /**
     * @return string
     */
    public function getClientId(): string
    {
        return $this->clientId;
    }

    /**
     * @param string $clientId
     * @return ClientSummary
     */
    public function setClientId(string $clientId): ClientSummary
    {
        $this->clientId = $clientId;
        return $this;
    }

    /**
     * @return string
     */
    public function getPrimaryPurpose(): string
    {
        return $this->primaryPurpose;
    }

    /**
     * @param string $primaryPurpose
     * @return ClientSummary
     */
    public function setPrimaryPurpose(string $primaryPurpose): ClientSummary
    {
        $this->primaryPurpose = $primaryPurpose;
        return $this;
    }

    /**
     * @return string
     */
    public function getDealSize(): string
    {
        return $this->dealSize;
    }

    /**
     * @param string $dealSize
     * @return ClientSummary
     */
    public function setDealSize(string $dealSize): ClientSummary
    {
        $this->dealSize = $dealSize;
        return $this;
    }

    /**
     * @return array
     */
    public function getTimeline(): array
    {
        return $this->timeline;
    }

    /**
     * @param array|string $timeline
     * @return ClientSummary
     */
    public function setTimeline(array|string $timeline): ClientSummary
    {
        if (is_string($timeline)) {
            $this->timeline = json_decode($timeline, true) ?: [];
        } else {
            $this->timeline = $timeline;
        }
        return $this;
    }

    /**
     * @return string
     */
    public function getDealStatus(): string
    {
        return $this->dealStatus;
    }

    /**
     * @param string $dealStatus
     * @return ClientSummary
     */
    public function setDealStatus(string $dealStatus): ClientSummary
    {
        $this->dealStatus = $dealStatus;
        return $this;
    }

    /**
     * @return string
     */
    public function getNextStep(): string
    {
        return $this->nextStep;
    }

    /**
     * @param string $nextStep
     * @return ClientSummary
     */
    public function setNextStep(string $nextStep): ClientSummary
    {
        $this->nextStep = $nextStep;
        return $this;
    }

    /**
     * @return string
     */
    public function getCustomerSentiment(): string
    {
        return $this->customerSentiment;
    }

    /**
     * @param string $customerSentiment
     * @return ClientSummary
     */
    public function setCustomerSentiment(string $customerSentiment): ClientSummary
    {
        $this->customerSentiment = $customerSentiment;
        return $this;
    }

    /**
     * @return array
     */
    public function getClientDiscoveryParameters(): array
    {
        return $this->clientDiscoveryParameters;
    }

    /**
     * @param array|string $clientDiscoveryParameters
     * @return ClientSummary
     */
    public function setClientDiscoveryParameters(array|string $clientDiscoveryParameters): ClientSummary
    {
        if (is_string($clientDiscoveryParameters)) {
            $this->clientDiscoveryParameters = json_decode($clientDiscoveryParameters, true) ?: [];
        } else {
            $this->clientDiscoveryParameters = $clientDiscoveryParameters;
        }
        return $this;
    }

    /**
     * @return array
     */
    public function getCustomerProblems(): array
    {
        return $this->customerProblems;
    }

    /**
     * @param array|string $customerProblems
     * @return ClientSummary
     */
    public function setCustomerProblems(array|string $customerProblems): ClientSummary
    {
        if (is_string($customerProblems)) {
            $this->customerProblems = json_decode($customerProblems, true) ?: [];
        } else {
            $this->customerProblems = $customerProblems;
        }
        return $this;
    }

    /**
     * @return array
     */
    public function getBusinessOpportunities(): array
    {
        return $this->businessOpportunities;
    }

    /**
     * @param array|string $businessOpportunities
     * @return ClientSummary
     */
    public function setBusinessOpportunities(array|string $businessOpportunities): ClientSummary
    {
        if (is_string($businessOpportunities)) {
            $this->businessOpportunities = json_decode($businessOpportunities, true) ?: [];
        } else {
            $this->businessOpportunities = $businessOpportunities;
        }
        return $this;
    }

    /**
     * @return string
     */
    public function getRisks(): string
    {
        return $this->risks;
    }

    /**
     * @param string $risks
     * @return ClientSummary
     */
    public function setRisks(string $risks): ClientSummary
    {
        $this->risks = $risks;
        return $this;
    }

    /**
     * @return string
     */
    public function getAgentPerformance(): string
    {
        return $this->agentPerformance;
    }

    /**
     * @param string $agentPerformance
     * @return ClientSummary
     */
    public function setAgentPerformance(string $agentPerformance): ClientSummary
    {
        $this->agentPerformance = $agentPerformance;
        return $this;
    }

    /**
     * @return array
     */
    public function getInteractionNotes(): array
    {
        return $this->interactionNotes;
    }

    /**
     * @param array|string $interactionNotes
     * @return ClientSummary
     */
    public function setInteractionNotes(array|string $interactionNotes): ClientSummary
    {
        if (is_string($interactionNotes)) {
            $this->interactionNotes = json_decode($interactionNotes, true) ?: [];
        } else {
            $this->interactionNotes = $interactionNotes;
        }
        return $this;
    }

    /**
     * @return Carbon
     */
    public function getLastCallTime(): Carbon
    {
        return $this->lastCallTime;
    }

    /**
     * @param string|Carbon $lastCallTime
     * @return ClientSummary
     */
    public function setLastCallTime(string|Carbon $lastCallTime): ClientSummary
    {
        $this->lastCallTime = is_string($lastCallTime) ? Carbon::parse($lastCallTime) : $lastCallTime;
        return $this;
    }

    /**
     * @return Carbon
     */
    public function getCreated(): Carbon
    {
        if (is_null($this->created)) {
            $this->created = Carbon::now();
        }
        return $this->created;
    }

    /**
     * @param string|Carbon $created
     * @return ClientSummary
     */
    public function setCreated(string|Carbon $created): ClientSummary
    {
        $this->created = is_string($created) ? Carbon::parse($created) : $created;
        return $this;
    }

    /**
     * @return array
     */
    public function toArray(): array
    {
        return $this->extract($this);
    }
}
