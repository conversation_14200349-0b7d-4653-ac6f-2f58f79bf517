<?php

declare(strict_types=1);

namespace STCall\Entity\CalculatedEvent;

class CalculatedEventsCollection extends \STLib\Expand\Collection
{
    /**
     *
     * @param mixed $event
     * @param string|int|null $key
     * @return \STLib\Expand\Collection
     * @throws \RuntimeException
     */
    public function add(mixed $event, string|int|null $key = null): \STLib\Expand\Collection
    {
        if (!($event instanceof \STCompany\Entity\Event\Event)) {
            throw new \RuntimeException('event must be an instance of "\STCompany\Entity\Event\Event"');
        }
        $key = $key ?? $event->getId();
        if (!$this->offsetExists($key)) {
            $calculatedEvent = new CalculatedEvent($event);
            parent::add($calculatedEvent, $key);
        }
        $this->offsetGet($key)->increment();
        return $this;
    }

    /**
     *
     * @return array
     */
    public function toArray(): array
    {
        $result = [];
        foreach ($this as $calculatedEvent) {
            $result[] = $calculatedEvent->toArray();
        }
        return $result;
    }
}
