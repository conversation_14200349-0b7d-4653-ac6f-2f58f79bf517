<?php

declare(strict_types=1);

namespace STCall\Entity\EventHappening;

class HistoryRecordCollection extends \STLib\Expand\Collection
{
    /**
     *
     * @param mixed $historyRecord
     * @param string|int|null $key
     * @return \STLib\Expand\Collection
     * @throws \RuntimeException
     */
    public function add(mixed $historyRecord, string|int|null $key = null): \STLib\Expand\Collection
    {
        if (!($historyRecord instanceof HistoryRecord)) {
            throw new \RuntimeException('HistoryRecord must be an instace of "\STCall\Entity\EventHappening\HistoryRecord"');
        }
        parent::add($historyRecord, $key);
        return $this;
    }

    /**
     *
     * @return array
     */
    public function toArray(): array
    {
        $result = [];
        foreach ($this as $historyRecord) {
            $result[] = $historyRecord->toArray();
        }
        return $result;
    }
}
