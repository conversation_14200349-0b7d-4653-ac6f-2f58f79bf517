<?php

declare(strict_types=1);

namespace STCall\Entity;

class CallRole
{
    /**
     *
     * @var Call
     */
    protected Call $call;

    /**
     *
     * @var int
     */
    protected int $roleId;

    /**
     *
     * @return Call
     */
    public function getCall(): Call
    {
        return $this->call;
    }

    /**
     *
     * @return int
     */
    public function getRoleId(): int
    {
        return $this->roleId;
    }

    /**
     *
     * @param Call $call
     * @return CallRole
     */
    public function setCall(Call $call): CallRole
    {
        $this->call = $call;
        return $this;
    }

    /**
     *
     * @param int $roleId
     * @return CallRole
     */
    public function setRoleId(int $roleId): CallRole
    {
        $this->roleId = $roleId;
        return $this;
    }
}
