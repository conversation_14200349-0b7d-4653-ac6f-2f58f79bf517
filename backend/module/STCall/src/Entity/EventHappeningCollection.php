<?php

declare(strict_types=1);

namespace STCall\Entity;

class EventHappeningCollection extends \STLib\Expand\Collection
{
    /**
     *
     * @param mixed $eventHappening
     * @param string|int|null $key
     * @return \STLib\Expand\Collection
     * @throws \RuntimeException
     */
    public function add(mixed $eventHappening, string|int|null $key = null): \STLib\Expand\Collection
    {
        if (!($eventHappening instanceof EventHappening)) {
            throw new \RuntimeException('Event must be an instace of "\STCall\Entity\EventHappening"');
        }
        parent::add($eventHappening, $key ?? $eventHappening->getEvent()->getId());
        return $this;
    }

    /**
     *
     * @return array
     */
    public function toArray(): array
    {
        $result = [];
        foreach ($this as $eventHappening) {
            $result[] = $eventHappening->toArray();
        }
        return $result;
    }
}
