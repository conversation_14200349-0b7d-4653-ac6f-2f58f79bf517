<?php

declare(strict_types=1);

namespace STCall\Daemon\ClientApi\Handler;

class ChatCallHandler extends AbstractHandler
{
    /**
     *
     * @param array $requestBody
     * @return bool
     * @throws \Exception
     */
    public function run(array $requestBody): bool
    {
        /** @var \STCall\Service\Import\UploadParams\UploadParams $uploadParams */
        $uploadParams = $this->hydrate(
            [
                'driver_name' => 'chat-api-upload',
                'company' => $this->company,
                'options' => $requestBody,
            ],
            \STCall\Service\Import\UploadParams\UploadParams::class
        );
        $result = $this->uploadService->uploadCall($uploadParams);
        $nextStep = match ($result->isUpdate()) {
            true => \STCall\Service\CallAnalysis\TranslationStep::CALL_TRANSLATION_QUEUE,
            default => \STCall\Service\CallAnalysis\LanguageDetectionStep::CALL_LANGUAGE_DETECTION_QUEUE,
        };
        $this->callAnalysisService->addToQueue(
            $this->company->getId(),
            $result->getCall(),
            $nextStep,
            '',
            [
                'request_id' => $this->requestId,
            ]
        );
        return true;
    }
}
