<?php

declare(strict_types=1);

namespace STCall\Daemon\Precalculation;

use STCall\Entity\Call;

class PrecalculateCallsDaemon extends \STRabbit\Entity\AbstractDaemon
{
    use \STCompany\Service\ClientPrecalculation\ClientPrecalculationManagerServiceTrait;
    use \STCompany\Service\AgentPrecalculation\AgentPrecalculationManagerServiceTrait;

    public const PRECALCULATION_QUEUE = 'precalculation';
    public const PRECALCULATION_QUEUE_ERROR = 'precalculation_error';

    /**
     *
     * @param string $message
     * @return void
     * @throws \ReflectionException
     * @throws \STApi\Entity\Exception\NotFoundApiException
     */
    public function handle(string $message): void
    {
        $data = json_decode($message);
        $companyId = (int) $data->company_id;
        $callIds = $data->call_ids;
        $roleId = $data->role_id ?? null;

        /** @var \STCall\Service\Precalculation\CallPrecalculationService $callPrecalculationService */
        $callPrecalculationService = $this->params()->offsetGet(\STCall\Service\Precalculation\CallPrecalculationService::class);
        $callPrecalculationService->precalculateCalls($companyId, $callIds, $roleId);

        // add affected clients and agents to precalculations
        /** @var \STCall\Service\CallService $callService */
        $callService = $this->params()->offsetGet(\STCall\Service\CallService::class);
        $calls = $callService->getBasicCalls($companyId, $callIds, true);
        $callsIdsToDelete = [];

        /** @var Call $call */
        foreach ($calls as $call) {
            if ($call->getIsDeleted()) {
                $callsIdsToDelete[] = $call->getId();
            }

            // add client to precalculations
            $this->getClientPrecalculationManager()->addClientsFromCallToPrecalculateQueue(
                $call,
                $roleId,
                \STCompany\Service\ClientPrecalculation\ClientPrecalculationManagerService::CALL_PRECALCULATED_PRIORITY,
            );
            //
            $this->getAgentPrecalculationManager()->addAgentsFromCallToPrecalculateQueue(
                $call,
                $roleId,
                \STCompany\Service\AgentPrecalculation\AgentPrecalculationManagerService::CALL_PRECALCULATED_PRIORITY,
            );
        }

        $callPrecalculationService->deleteByCallsIds($callsIdsToDelete);
    }

    /**
     *
     * @return \STRabbit\Entity\AbstractDaemon
     */
    public function init(): \STRabbit\Entity\AbstractDaemon
    {
        $this->getChannel()->queue_declare(
            queue: $this->getQueueName(),
            passive: false,
            durable: true,
            exclusive: false,
            auto_delete: false,
            nowait: false,
            arguments: new \PhpAmqpLib\Wire\AMQPTable([
                'x-max-priority' => 100,
            ]),
        );
        $this->getChannel()->queue_declare(
            queue: $this->getErrorQueueName(),
            passive: false,
            durable: true,
            exclusive: false,
            auto_delete: false,
        );
        return $this;
    }
}
