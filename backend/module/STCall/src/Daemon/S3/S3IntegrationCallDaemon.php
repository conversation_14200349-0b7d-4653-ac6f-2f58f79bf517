<?php

declare(strict_types=1);

namespace STCall\Daemon\S3;

use STCall\Service\AwsTrait;
use STCall\Service\CallAnalysis\LanguageDetectionStep;
use STCall\Service\CallAnalysisService;
use STCall\Service\Import\UploadService;
use STCompany\Service\CompanyService;

class S3IntegrationCallDaemon extends \STRabbit\Entity\AbstractDaemon
{
    use AwsTrait;
    use \STLib\Mvc\Hydrator\BaseHydratorTrait;

    public const QUEUE = 's3-integration-call';
    public const QUEUE_ERROR = 's3-integration-call-error';

    public const UNPROCESSED_DIR = 'unprocessed/';
    public const PROCESSING_DIR = 'processing/';
    public const PROCESSED_DIR = 'processed/';

    /**
     *
     * @param string $message
     * @return void
     * @throws \Exception
     */
    public function handle(string $message): void
    {
        $data = json_decode($message);

        $awsConfig = $this->params()->offsetGet('aws_config');
        /** @var CompanyService $companyService */
        $companyService = $this->params()->offsetGet(CompanyService::class);
        /** @var UploadService $uploadService */
        $uploadService = $this->params()->offsetGet(UploadService::class);
        /** @var CallAnalysisService $callAnalysisService */
        $callAnalysisService = $this->params()->offsetGet(CallAnalysisService::class);

        $this->setAwsConfig($awsConfig['api']);
        $this->setEnv($awsConfig['env']);

        $company = $companyService->getCompany((int) $data->company_id);
        $this->setCompany($company);

        $processingFilePath = $data->file_path;
        $fileName = pathinfo($processingFilePath)['basename'];
        $processedFilePath = self::PROCESSED_DIR . $fileName;

        /** @var \STCall\Service\Import\UploadParams\UploadParams $uploadParams */
        $uploadParams = $this->hydrate(
            [
                'driver_name' => 's3-bucket-upload',
                'content' => $this->getFileFromS3ByObjectPath($this->getBucketName() . '/' . $processingFilePath),
                'company' => $this->company,
                'user' => null,
                'options' => [
                    'file_name' => $fileName,
                    's3_file_path' => $fileName,
                ],
            ],
            \STCall\Service\Import\UploadParams\UploadParams::class
        );
        $call = $uploadService->uploadCall($uploadParams)->getCall();
        $this->moveObject(
            $processingFilePath,
            $processedFilePath
        );
        $callAnalysisService->addToQueue(
            $company->getId(),
            $call,
            LanguageDetectionStep::CALL_LANGUAGE_DETECTION_QUEUE
        );
    }
}
