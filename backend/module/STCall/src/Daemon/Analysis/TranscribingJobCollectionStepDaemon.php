<?php

declare(strict_types=1);

namespace STCall\Daemon\Analysis;

use PhpAmqpLib\Exchange\AMQPExchangeType;
use PhpAmqpLib\Wire\AMQPTable;
use STApi\Entity\Exception\CallUpload\FileIsTooBigException;
use ST<PERSON><PERSON>\Entity\Exception\CallUpload\IdenticalFileException;
use STA<PERSON>\Entity\Exception\CallUpload\InvalidContentException;
use STApi\Entity\Exception\CallUpload\TooLowBalanceForAnalyzeException;
use STApi\Entity\Exception\InvalidCompanyApiRequestException;
use STCall\Service\CallAnalysis\Exception\StepIsAlreadyFinishedException;
use STCall\Service\CallAnalysis\Exception\StepIsFailedWithErrorFromTranscribingDriverException;
use STCall\Service\CallAnalysis\Transcribing\TwoSteps\NotReadyException;
use STRabbit\Entity\AbstractDaemon;
use STRabbit\Service\Daemon\NotThrowableException;
use STRoboTruck\Service\DataCollection\DataCollector;

class TranscribingJobCollectionStepDaemon extends BaseAnalysisStepDaemon
{
    public const RABBITMQ_EXCHANGE_NAME = 'delay';

    /**
     *
     * @var int
     */
    protected int $attemptsNumber = 120;

    /**
     *
     * @var int
     */
    protected int $delay = 60 * 1000;

    /**
     *
     * Class constructor
     */
    public function __construct(DataCollector $dataCollector)
    {
        parent::__construct($dataCollector);

        $this->exchangeName = static::RABBITMQ_EXCHANGE_NAME;
    }

    /**
     *
     * @param string $message
     * @return void
     * @throws NotThrowableException
     * @throws FileIsTooBigException
     * @throws IdenticalFileException
     * @throws InvalidContentException
     * @throws TooLowBalanceForAnalyzeException
     * @throws InvalidCompanyApiRequestException
     * @throws StepIsAlreadyFinishedException
     * @throws StepIsFailedWithErrorFromTranscribingDriverException
     */
    public function handle(string $message): void
    {
        try {
            parent::handle($message);
        } catch (NotReadyException $e) {
            throw new NotThrowableException($e->getMessage(), $e->getCode());
        }
    }

    /**
     *
     * @return AbstractDaemon
     */
    public function init(): AbstractDaemon
    {
        $this->getChannel()->exchange_declare(
            exchange: static::RABBITMQ_EXCHANGE_NAME,
            type: 'x-delayed-message',
            passive: false,
            durable: true,
            auto_delete: false,
            internal: false,
            nowait: false,
            arguments: new AMQPTable([
                'x-delayed-type' => AMQPExchangeType::DIRECT,
            ]),
        );
        $this->getChannel()->queue_declare(
            queue: $this->getQueueName(),
            passive: false,
            durable: true,
            exclusive: false,
            auto_delete: false,
            nowait: false,
            arguments: new AMQPTable([
                'x-dead-letter-exchange' => 'delayed',
                'x-max-priority' => 100,
            ]),
        );
        $this->getChannel()->queue_declare(
            queue: $this->getErrorQueueName(),
            passive: false,
            durable: true,
            exclusive: false,
            auto_delete: false,
        );
        $this->getChannel()->queue_bind($this->getQueueName(), $this->getExchangeName(), $this->getQueueName());
        return $this;
    }
}
