<?php

declare(strict_types=1);

namespace STCall\Service\Export;

use Carbon\Carbon;
use RuntimeException;
use STCall\Service\Interfaces\ConfigurationInterface;
use STCompany\Entity\Company;

class FileNameGenerator
{
    public function __construct(private readonly ConfigurationInterface $configuration)
    {
    }

    public function generate(Company $company, string $exportType, Carbon $startDate, Carbon $endDate): string
    {
        if (!$company->getAwsS3ExportBucketRegion() || !$company->getAwsS3ExportBucketName()) {
            $message = sprintf(
                'Fields %s or %s are not filled in for the company. Company ID: %d',
                'aws_s3_export_bucket_name',
                'aws_s3_export_bucket_region',
                $company->getId()
            );

            throw new RuntimeException($message);
        }

        if (!$company->getAwsS3ExportBucketDir()) {
            return sprintf(
                '%s/%s/%s-%s',
                $this->configuration->get('api')['env'],
                $exportType,
                $startDate->format('Y_m_d-H:i:s'),
                $endDate->format('Y_m_d-H:i:s')
            ) . '.csv';
        }

        return sprintf(
            '%s/%s/%s/%s-%s',
            $this->configuration->get('api')['env'],
            $company->getAwsS3ExportBucketDir(),
            $exportType,
            $startDate->format('Y_m_d-H:i:s'),
            $endDate->format('Y_m_d-H:i:s')
        ) . '.csv';
    }
}
