<?php

declare(strict_types=1);

namespace STCall\Service\Webhooks;

use Laminas\Http\Client;
use STApi\Entity\Exception\ThirdPartyApiException;

class WebhookSender
{
    /**
     * @param string $url
     * @param array $data
     * @param array|null $headers
     * @return void
     * @throws ThirdPartyApiException
     */
    public function send(string $url, array $data, ?array $headers = null): void
    {
        $client = $this->createClient();
        $client->setUri($url);
        $client->setHeaders($headers ?? ['Content-type' => 'application/json']);
        $client->setMethod('POST');
        $client->setRawBody(json_encode($data));
        $client->setOptions(['timeout' => 3, 'connect_timeout' => 1]);

        $response = $client->send();

        if (!$response->isSuccess()) {
            throw new ThirdPartyApiException(
                sprintf(
                    'Failed to send webhook: response code: %d, response body: %s',
                    $response->getStatusCode(),
                    $response->getBody()
                )
            );
        }
    }

    protected function createClient(): Client
    {
        return new Client();
    }
}
