<?php

declare(strict_types=1);

namespace STCall\Service\CallAnalysis;

use Exception;
use GuzzleHttp\Exception\GuzzleException;
use RuntimeException;
use ST<PERSON><PERSON>\Entity\Exception\CallUpload\FileIsTooBigException;
use ST<PERSON><PERSON>\Entity\Exception\NoAccessToFileApiException;
use ST<PERSON>all\Data\CallsTable;
use STCall\Entity\CallFactory;
use STCall\Service\AwsTrait;
use STCall\Service\CallAnalysis\Downloading\FileDownloader;
use STCall\Service\Import\UploadParams\UploadParams;
use STCall\Service\Import\UploadService;
use STCompany\Service\CompanyService;
use STLib\Mvc\Hydrator\Hydrator;
use STRoboTruck\Service\DataCollection\DataCollector;

class FileDownloadStep extends BaseStep
{
    use AwsTrait;

    public const CALL_FILE_DOWNLOAD_QUEUE = 'call-file-download-step';
    public const CALL_FILE_DOWNLOAD_ERROR_QUEUE = 'call-file-download-step-error';

    /**
     *
     * @var UploadService
     */
    protected UploadService $uploadService;

    /**
     *
     * @var CompanyService
     */
    protected CompanyService $companyService;


    /**
     *
     * @var array
     */
    protected array $callParams = [];

    /**
     *
     * @var string|null
     */
    protected ?string $requestId = null;

    /**
     *
     * @var bool
     */
    protected bool $hasNextStep;

    /**
     *
     * @param CallsTable $callsTable
     * @param FileDownloader $fileDownloader
     * @param Hydrator $hydrator
     * @param DataCollector $dataCollector
     * @param UploadService $uploadService
     * @param CompanyService $companyService
     */
    public function __construct(
        CallsTable $callsTable,
        CallFactory $callFactory,
        private FileDownloader $fileDownloader,
        private Hydrator $hydrator,
        private DataCollector $dataCollector,
        UploadService $uploadService,
        CompanyService $companyService,
    ) {
        parent::__construct($callsTable, $callFactory);
        $this->uploadService = $uploadService;
        $this->companyService = $companyService;
    }

    /**
     *
     * @param int $companyId
     * @return bool
     * @throws NoAccessToFileApiException
     * @throws Exception
     * @throws GuzzleException
     */
    public function run(int $companyId): bool
    {
        $company = $this->companyService->getCompany($companyId);

        if (isset($this->callParams['recording_file'])) {
            $driverName = 'api-upload';
            $content = $this->fileDownloader->getCallFileContent($this->callParams['recording_file']);
            $sourcedContent = $content;
        } else {
            throw new RuntimeException('Incorrect request. There is no "recording_file" param');
        }
        try {
            /** @var UploadParams $uploadParams */
            $uploadParams = $this->hydrator->hydrateClass(
                [
                    'driver_name' => $driverName,
                    'content' => $content,
                    'sourced_content' => $sourcedContent,
                    'company' => $company,
                    'user' => null,
                    'options' => $this->callParams,
                ],
                UploadParams::class
            );
            $uploadResult = $this->uploadService->uploadCall($uploadParams);
            $call = $uploadResult->getCall();
            $this->hasNextStep = $call->isSentToTranscribing() && !$uploadResult->isUpdate();
        } catch (Exception $e) {
            $this->dataCollector->collect(
                DataCollector::EVENT_CALL_UPLOAD_FILE_DOWNLOAD_STEP_ERROR,
                json_encode(['code' => $e->getCode(), 'error' => $e->getMessage()]),
                [
                    'id' => $this->requestId,
                    'company_id' => $company->getId(),
                    'call_id' => null,
                ],
                'api-calls-logs'
            );

            throw $e;
        }

        $this->call = $call;

        $this->dataCollector->collect(
            DataCollector::EVENT_CALL_UPLOAD_FILE_DOWNLOAD_STEP_SUCCESS,
            json_encode($call->toArray()),
            [
                'id' => $this->requestId,
                'company_id' => $companyId,
                'call_id' => null,
            ],
            'api-calls-logs'
        );

        return true;
    }

    /**
     *
     * @param array $options
     * @return StepInterface
     */
    public function applyOptions(array $options = []): StepInterface
    {
        $this->callParams = (array) $options['params'] ?? [];
        $this->requestId = $options['request_id'];
        return $this;
    }

    /**
     *
     * @return string
     */
    public function getNextStepQueue(): string
    {
        return LanguageDetectionStep::CALL_LANGUAGE_DETECTION_QUEUE;
    }

    /**
     *
     * @return string
     */
    public function getQueueName(): string
    {
        return static::CALL_FILE_DOWNLOAD_QUEUE;
    }

    /**
     *
     * @return string
     */
    public function getErrorQueueName(): string
    {
        return static::CALL_FILE_DOWNLOAD_ERROR_QUEUE;
    }

    /**
     *
     * @return bool
     */
    public function hasNextStep(): bool
    {
        return $this->hasNextStep;
    }
}
