<?php

declare(strict_types=1);

namespace STCall\Service\CallAnalysis;

use ReflectionException;
use ST<PERSON><PERSON>\Entity\Exception\BadRequestApiException;
use ST<PERSON><PERSON>\Entity\Exception\CallUpload\TooLowBalanceForAnalyzeException;
use ST<PERSON><PERSON>\Entity\Exception\NotFoundApiException;
use ST<PERSON>all\Data\CallsParagraphsTable;
use STCall\Data\CallsTable;
use ST<PERSON>all\Entity\CallFactory;
use STCall\Entity\ParagraphCollection;
use STCall\Service\CallAnalysis\TranscribingDriver\OneStepDriverInterface;
use STCall\Service\CallAnalysis\TranscribingDriver\Provider\DriverProvider;
use STCall\Service\CallAnalysisService;
use STCall\Service\EventTriggerService;
use STCompany\Data\CompaniesTable;
use STCompany\Entity\Company;
use STCompany\Service\CompanyVocabularyService;
use STLib\Mvc\Hydrator\BaseHydratorTrait;
use STLib\Mvc\Hydrator\Hydrator;
use STRabbit\Service\ProvidesRabbit;
use STRoboTruck\Service\DataCollection\DataCollector;

class TranscribingStep extends BaseTranscribingStep
{
    use ProvidesRabbit;
    use BaseHydratorTrait;

    public const CALL_TRANSCRIBING_QUEUE = 'call-transcribing-step';
    public const CALL_TRANSCRIBING_ERROR_QUEUE = 'call-transcribing-step-error';

    /**
     * @var array
     */
    protected array $deepgramConfig = [];

    /**
     * @var array
     */
    protected array $deepgramWhisperConfig = [];

    /**
     * @var array
     */
    protected array $deepgramNova2Config = [];

    /**
     * @var array
     */
    protected array $deepgramNova3Config = [];

    /**
     * @param CallsTable $callsTable
     * @param CallFactory $callFactory
     * @param CompaniesTable $companiesTable
     * @param Hydrator $hydrator
     * @param DriverProvider $driverProvider
     * @param CallsParagraphsTable $callsParagraphsTable
     * @param CompanyVocabularyService $companyVocabularyService
     * @param EventTriggerService $eventTrigger
     * @param DataCollector $dataCollector
     * @param array $awsConfig
     * @param array $deepgramConfig
     */
    public function __construct(
        CallsTable $callsTable,
        CallFactory $callFactory,
        CompaniesTable $companiesTable,
        Hydrator $hydrator,
        DriverProvider $driverProvider,
        CallsParagraphsTable $callsParagraphsTable,
        CompanyVocabularyService $companyVocabularyService,
        private readonly EventTriggerService $eventTrigger,
        private readonly DataCollector $dataCollector,
        array $awsConfig,
        array $deepgramConfig,
    ) {
        parent::__construct(
            $callsTable,
            $callFactory,
            $companiesTable,
            $hydrator,
            $driverProvider,
            $callsParagraphsTable,
            $companyVocabularyService,
            $awsConfig
        );
        $this->deepgramConfig = $deepgramConfig;
        $this->deepgramWhisperConfig = $deepgramConfig;
        $this->deepgramNova2Config = $deepgramConfig;
        $this->deepgramNova3Config = $deepgramConfig;
    }

    /**
     * @return string
     */
    public function getQueueName(): string
    {
        return static::CALL_TRANSCRIBING_QUEUE;
    }

    /**
     * @return string
     */
    public function getErrorQueueName(): string
    {
        return static::CALL_TRANSCRIBING_ERROR_QUEUE;
    }

    /**
     * @param int $companyId
     * @return bool
     * @throws BadRequestApiException
     * @throws Exception\StepIsAlreadyFinishedException
     * @throws NotFoundApiException
     * @throws ReflectionException
     * @throws TooLowBalanceForAnalyzeException
     */
    public function run(int $companyId): bool
    {
        $this->clearObject();

        $this->nextStepQueue = TranslationStep::CALL_TRANSLATION_QUEUE;
        $call = $this->getCall();
        if ($call->isTranscribed()) {
            throw new Exception\StepIsAlreadyFinishedException('Call is already transcribed');
        }

        $company = $this->getCompany();
        if ($company->getPaidTranscribingTime() < $call->getDuration()) {
            throw new TooLowBalanceForAnalyzeException('Company does not have enough time to transcribe this call');
        }

        $driver = $this->getDriver($this->driverName);

        $this->dataCollector->collect(
            DataCollector::EVENT_CALL_ANALYZE_TRANSCRIBING_STEP_STARTED,
            'Transcribing is started',
            [
                'id' => '',
                'company_id' => $this->getCompany()->getId(),
                'call_id' => $call->getId(),
            ],
            'api-calls-logs'
        );

        /** @var OneStepDriverInterface $driver */
        $paragraphCollection = $driver->transcribe();

        $this->saveTranscribedData($paragraphCollection);
        $this->reduceBalance();

        $this->dataCollector->collect(
            DataCollector::EVENT_CALL_ANALYZE_TRANSCRIBING_STEP_ENDED,
            'Transcribing is ended',
            [
                'id' => '',
                'company_id' => $this->getCompany()->getId(),
                'call_id' => $call->getId(),
            ],
            'api-calls-logs'
        );

        $this->triggerStepFinishedEvent($call->getId(), $companyId, $paragraphCollection);

        return true;
    }

    private function triggerStepFinishedEvent(string $callId, int $companyId, ParagraphCollection $paragraphs): void
    {
        $paragraphsData = [];
        foreach ($paragraphs as $paragraph) {
            $paragraphsData[] = $paragraph->toArray();
        }

        $this->eventTrigger->trigger(
            CallAnalysisService::EVENT_CALL_TRANSCRIBING_STEP_FINISHED,
            [
                'queue_name' => self::CALL_TRANSCRIBING_QUEUE,
                'company_id' => $companyId,
                'call_id' => $callId,
                'data' => $paragraphsData
            ]
        );
    }
}
