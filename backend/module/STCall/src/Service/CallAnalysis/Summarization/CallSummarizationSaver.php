<?php

declare(strict_types=1);

namespace STCall\Service\CallAnalysis\Summarization;

use STCall\Data\CallsSummarizationsTable;
use STCall\Entity\CallSummarization;

class CallSummarizationSaver
{
    public function __construct(private readonly CallsSummarizationsTable $callSummarizationsTable)
    {
    }

    public function saveCallSummarization(CallSummarization $callSummarization): void
    {
        $this->callSummarizationsTable->saveCallSummarization($callSummarization);
    }
}
