<?php

namespace STCall\Service\CallAnalysis\TranscribingDriver\Provider;

use <PERSON><PERSON>\Filter\Word\DashToCamelCase;
use LogicException;
use ST<PERSON>all\Entity\Call;
use STCall\Service\CallAnalysis\TranscribingDriver\AbstractDriver;
use STCall\Service\CallAnalysis\TranscribingDriver\OneStepDriverInterface;
use STCall\Service\CallAnalysis\TranscribingDriver\TwoStepsDriverInterface;
use STCompany\Entity\Company;
use STCompany\Entity\CompanyVocabularyCollection;
use STRoboTruck\Service\DataCollection\DataCollector;

class DriverProvider
{
    public function __construct(private readonly DataCollector $dataCollector)
    {
    }

    public function provide(
        string $driverName,
        array $awsConfig,
        array $driverConfig,
        Company $company,
        Call $call,
        CompanyVocabularyCollection $companyVocabularyCollection
    ): AbstractDriver|TwoStepsDriverInterface|OneStepDriverInterface {
        $dashToCamelCase = new DashToCamelCase();
        $namespace = '\STCall\Service\CallAnalysis\TranscribingDriver\Factory\\' . $dashToCamelCase->filter(
            $driverName
        ) . 'DriverFactory';
        if (!class_exists($namespace)) {
            throw new LogicException('Can\'t find transcribing driver "' . $namespace . '"');
        }
        $driver = (new $namespace())->create($this->dataCollector);
        $driver->setAwsConfig($awsConfig['api']);
        $driver->setEnv($awsConfig['env']);
        $driver->setDriverConfig($driverConfig);
        $driver->setCompany($company);
        $driver->setCall($call);
        $driver->setCompanyVocabulary($companyVocabularyCollection);

        return $driver;
    }
}
