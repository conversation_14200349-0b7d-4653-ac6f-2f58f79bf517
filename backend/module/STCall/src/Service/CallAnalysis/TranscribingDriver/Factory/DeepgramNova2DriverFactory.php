<?php

declare(strict_types=1);

namespace STCall\Service\CallAnalysis\TranscribingDriver\Factory;

use STCall\Service\CallAnalysis\TranscribingDriver\AbstractDriver;
use STCall\Service\CallAnalysis\TranscribingDriver\DeepgramNova2Driver;
use STRoboTruck\Service\DataCollection\DataCollector;

class DeepgramNova2DriverFactory implements FactoryInterface
{
    /**
     *
     * @return AbstractDriver
     */
    public function create(DataCollector $dataCollector): AbstractDriver
    {
        return new DeepgramNova2Driver($dataCollector);
    }
}
