<?php

declare(strict_types=1);

namespace STCall\Service\CallAnalysis\TranscribingDriver\Factory;

use STCall\Service\CallAnalysis\TranscribingDriver\AbstractDriver;
use STCall\Service\CallAnalysis\TranscribingDriver\AwsDriver;
use STRoboTruck\Service\DataCollection\DataCollector;

class AwsDriverFactory implements FactoryInterface
{
    /**
     *
     * @return AbstractDriver
     */
    public function create(DataCollector $dataCollector): AbstractDriver
    {
        return new AwsDriver($dataCollector);
    }
}
