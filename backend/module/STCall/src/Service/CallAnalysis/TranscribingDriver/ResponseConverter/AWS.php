<?php

declare(strict_types=1);

namespace STCall\Service\CallAnalysis\TranscribingDriver\ResponseConverter;

class AWS extends AbstractWordsToParagraphResponseConverter
{
    /**
     * @param mixed $response
     * @return iterable
     */
    protected function getWordsItems(mixed $response): iterable
    {
        return $response->results->items;
    }

    /**
     * @param $responseItem
     * @return ?int
     */
    protected function getSpeaker($responseItem): ?int
    {
        return isset($responseItem->speaker_label) ? (int) \mb_substr($responseItem->speaker_label, -1) : null;
    }
}
