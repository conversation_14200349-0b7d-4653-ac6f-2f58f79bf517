<?php

declare(strict_types=1);

namespace STCall\Service\CallAnalysis\TranscribingDriver\ResponseConverter;

use ST<PERSON>all\Entity\Paragraph;
use ST<PERSON>all\Entity\ParagraphCollection;
use STLib\Mvc\Hydrator\BaseHydratorTrait;

class Wordcab extends AbstractParagraphResponseConverter
{
    use BaseHydratorTrait;

    /**
     *
     * @inheritdoc
     */
    public function convert(mixed $response): ParagraphCollection
    {
        $paragraphCollection = new ParagraphCollection();

        foreach ($response->transcript as $paragraphNumber => $transcriptItem) {
            $speaker = $this->letterToNumber($transcriptItem->speaker);
            $paragraphCollection->add($this->hydrate([
                'paragraph_number' => $paragraphNumber,
                'start_time' => $transcriptItem->words[0]->start,
                'end_time' => $transcriptItem->words[\count($transcriptItem->words) - 1]->end,
                'speaker_number' => $speaker,
                'text' => $transcriptItem->text,
            ], Paragraph::class));
        }

        return $paragraphCollection;
    }
}
