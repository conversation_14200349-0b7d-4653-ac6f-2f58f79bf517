<?php

declare(strict_types=1);

namespace STCall\Service\CallAnalysis\TranscribingDriver;

use STCall\Service\CallAnalysis\TranscribingDistributionStep;

class WordcabDriver extends AbstractDriver implements TwoStepsDriverInterface
{
    use \STLib\Mvc\Hydrator\BaseHydratorTrait;

    protected const BASE_URL = 'https://wordcab.com/api/v1/';

    protected const STATUS_COMPLETED = 'TranscriptComplete';
    protected const STATUS_IN_PROGRESS = 'Transcribing';
    protected const STATUS_ERROR = 'Error';

    protected const FILE_ACCESS_DURATION_IN_MIN = 180;

    /**
     *
     * @return string
     */
    public function createJob(): string
    {
        $params = [
            'url' => $this->getFileLink(static::FILE_ACCESS_DURATION_IN_MIN),
            'url_type' => 'audio_url',
            //'alignment' => 'true',
            'source_lang' => $this->getCall()->getLanguage(),
            'num_speakers' => 2,
            'diarization' => 'true',
        ];

        if (!$this->getCompanyVocabulary()->isEmpty()) {
            $params['vocab'] = implode(',', array_column($this->getCompanyVocabulary()->toArray(), 'word'));
        }

        $jobId = $this->call('post', 'transcribe', $params)->job_name;
        $this->createTranscribingRequestLog($jobId, TranscribingDistributionStep::TRANSCRIBING_WORDCAB_DRIVER);

        return $jobId;
    }

    /**
     *
     * @param string $transcriptionName
     * @return \STCall\Entity\ParagraphCollection
     * @throws \STCall\Service\CallAnalysis\Transcribing\TwoSteps\NotReadyException
     * @throws \RuntimeException
     */
    public function collectData(string $transcriptionName): \STCall\Entity\ParagraphCollection
    {
        $jobResponse = $this->call('get', 'jobs' . '/' . $transcriptionName);
        $transcriptionJobStatus = $jobResponse->job_status;

        if ($transcriptionJobStatus === static::STATUS_IN_PROGRESS) {
            throw new \STCall\Service\CallAnalysis\Transcribing\TwoSteps\NotReadyException();
        }
        if ($transcriptionJobStatus === static::STATUS_ERROR) {
            throw new \STCall\Service\CallAnalysis\Exception\StepIsFailedWithErrorFromTranscribingDriverException('Wordcab transcribing error for job "' . $transcriptionName . '" with text "' . $jobResponse->error_message . '"');
        }
        if ($transcriptionJobStatus !== static::STATUS_COMPLETED) {
            throw new \RuntimeException('Unknown status "' . $transcriptionJobStatus . '" for job "' . $transcriptionName . '"');
        }

        $transcribingResponse = $this->call('get', 'transcripts' . '/' . $jobResponse->transcript_id);

        return $this->getResponseToCollectionConverter()->convertDriverResponseToParagraphCollection(
            $transcribingResponse,
            ResponseConverter\ResponseToCollectionConverter::WORDCAB,
            $this->call
        );
    }

    /**
     *
     * @param string $method
     * @param string $path
     * @param array $params
     * @return \ArrayObject
     */
    protected function call(string $method, string $path, array $params = []): \stdClass
    {
        $client = new \GuzzleHttp\Client();
        $result = $client->request($method, static::BASE_URL . $path, [
            'headers' => [
                'Authorization' => 'Bearer ' . ' ' . $this->driverConfig['api']['token'],
            ],
            'query' => $params,
        ]);
        return json_decode($result->getBody()->getContents());
    }
}
