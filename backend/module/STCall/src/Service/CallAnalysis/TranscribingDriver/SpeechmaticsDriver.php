<?php

declare(strict_types=1);

namespace STCall\Service\CallAnalysis\TranscribingDriver;

use STCall\Service\CallAnalysis\TranscribingDistributionStep;

class SpeechmaticsDriver extends AbstractDriver implements TwoStepsDriverInterface
{
    use \STLib\Mvc\Hydrator\BaseHydratorTrait;

    protected const BASE_URL = 'https://eu.asr.api.speechmatics.com/v2/';

    protected const STATUS_COMPLETED = 'done';
    protected const STATUS_IN_PROGRESS = 'running';
    protected const STATUS_ERROR = 'rejected';

    protected const FILE_ACCESS_DURATION_IN_MIN = 180;

    /**
     *
     * @return string
     */
    public function createJob(): string
    {
        $params = [
            'type' => 'transcription',
            'fetch_data' => ['url' => $this->getFileLink(static::FILE_ACCESS_DURATION_IN_MIN)],
            'transcription_config' => [
                'language' => $this->getCall()->getLanguage(),
                'diarization' => 'speaker',
                'operating_point' => 'enhanced',
            ],
        ];

        if (!$this->getCompanyVocabulary()->isEmpty()) {
            $params['transcription_config']['additional_vocab'] = array_map(
                static fn($value): array => ['content' => $value],
                array_column($this->getCompanyVocabulary()->toArray(), 'word'),
            );
        }

        $jobId = $this->callCreateJob('post', $params)->id;
        $this->createTranscribingRequestLog($jobId, TranscribingDistributionStep::TRANSCRIBING_SPEECHMATICS_DRIVER);

        return $jobId;
    }

    /**
     *
     * @param string $transcriptionName
     * @return \STCall\Entity\ParagraphCollection
     * @throws \STCall\Service\CallAnalysis\Transcribing\TwoSteps\NotReadyException
     * @throws \RuntimeException
     */
    public function collectData(string $transcriptionName): \STCall\Entity\ParagraphCollection
    {
        $jobResponse = $this->call('get', 'jobs' . '/' . $transcriptionName);
        $transcriptionJobStatus = $jobResponse->job->status;

        if ($transcriptionJobStatus === static::STATUS_IN_PROGRESS) {
            throw new \STCall\Service\CallAnalysis\Transcribing\TwoSteps\NotReadyException();
        }
        if ($transcriptionJobStatus === static::STATUS_ERROR) {
            throw new \STCall\Service\CallAnalysis\Exception\StepIsFailedWithErrorFromTranscribingDriverException('Speechmatics transcribing error for job"' . $transcriptionName . '"');
        }
        if ($transcriptionJobStatus !== static::STATUS_COMPLETED) {
            throw new \RuntimeException('Unknown status "' . $transcriptionJobStatus . '" for job"' . $transcriptionName . '"');
        }

        $transcribingResponse = $this->call('get', sprintf('/jobs/%s/transcript', $transcriptionName));

        return $this->getResponseToCollectionConverter()->convertDriverResponseToParagraphCollection(
            $transcribingResponse,
            ResponseConverter\ResponseToCollectionConverter::SPEECHMATICS,
            $this->call
        );
    }

    /**
     *
     * @param string $method
     * @param array $params
     * @return \stdClass
     */
    protected function callCreateJob(string $method, array $params = []): \stdClass
    {
        $client = new \GuzzleHttp\Client();
        $result = $client->request($method, static::BASE_URL . 'jobs', [
            'headers' => [
                'Authorization' => 'Bearer' . ' ' . $this->driverConfig['api']['token'],
            ],
            'multipart' => [
                [
                    'name' => 'config',
                    'contents' => json_encode($params, JSON_THROW_ON_ERROR),
                ],
            ],
        ]);

        return json_decode($result->getBody()->getContents(), false, 512, JSON_THROW_ON_ERROR);
    }

    /**
     *
     * @param string $method
     * @param string $path
     * @return \stdClass
     */
    protected function call(string $method, string $path): \stdClass
    {
        $client = new \GuzzleHttp\Client();
        $result = $client->request($method, static::BASE_URL . $path, [
            'headers' => [
                'Authorization' => 'Bearer' . ' ' . $this->driverConfig['api']['token'],
            ],
        ]);

        return json_decode($result->getBody()->getContents(), false, 512, JSON_THROW_ON_ERROR);
    }
}
