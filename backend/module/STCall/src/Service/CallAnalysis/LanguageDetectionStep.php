<?php

declare(strict_types=1);

namespace STCall\Service\CallAnalysis;

use FFMpeg\Coordinate\TimeCode;
use FFMpeg\FFMpeg;
use FFMpeg\Format\Audio\Mp3;
use GuzzleHttp\Exception\GuzzleException;
use ReflectionException;
use ST<PERSON><PERSON>\Entity\Exception\NotFoundApiException;
use ST<PERSON>all\Data\CallsParagraphsTable;
use STCall\Data\CallsTable;
use STCall\Entity\Call;
use STCall\Entity\CallFactory;
use STCall\Entity\ChatCall;
use STCall\Entity\Paragraph;
use STCall\Service\AwsTrait;
use STCall\Service\Interfaces\TranslatorInterface;
use STCompany\Data\CompaniesLanguagesTable;
use STCompany\Data\CompaniesTable;
use STCompany\Data\UsersCompaniesLanguagesTable;
use STCompany\Entity\Company;
use STLib\Mvc\Hydrator\BaseHydratorTrait;

class LanguageDetectionStep extends BaseStep
{
    use BaseHydratorTrait;
    use AwsTrait;

    public const string CALL_LANGUAGE_DETECTION_QUEUE = 'call-language-detection-step';
    public const string CALL_LANGUAGE_DETECTION_ERROR_QUEUE = 'call-language-detection-step-error';

    protected const int DEFAULT_START_TIMESTAMP_FOR_LANGUAGE_DETECTION = 20;
    protected const int DEFAULT_WHISPER_CROPPED_AUDIO_LENGTH_FOR_LANGUAGE_DETECTION = 30;

    protected const int TEXT_SIZE_FOR_TEXT_LANGUAGE_DETECT = 100;

    /**
     *
     * @var CompaniesTable
     */
    protected CompaniesTable $companiesTable;

    /**
     *
     * @var CompaniesLanguagesTable
     */
    protected CompaniesLanguagesTable $companiesLanguagesTable;

    /**
     *
     * @var UsersCompaniesLanguagesTable
     */
    protected UsersCompaniesLanguagesTable $usersCompaniesLanguagesTable;

    /**
     *
     * @var CallsParagraphsTable
     */
    protected CallsParagraphsTable $callsParagraphsTable;

    /**
     *
     * @var string
     */
    protected string $nextStepQueue;

    /**
     * @param CallsTable $callsTable
     * @param CallFactory $callFactory
     * @param CompaniesTable $companiesTable
     * @param CompaniesLanguagesTable $companiesLanguagesTable
     * @param UsersCompaniesLanguagesTable $usersCompaniesLanguagesTable
     * @param CallsParagraphsTable $callsParagraphsTable
     * @param TranslatorInterface $translator
     * @param array $awsConfig
     */
    public function __construct(
        CallsTable $callsTable,
        CallFactory $callFactory,
        CompaniesTable $companiesTable,
        CompaniesLanguagesTable $companiesLanguagesTable,
        UsersCompaniesLanguagesTable $usersCompaniesLanguagesTable,
        CallsParagraphsTable $callsParagraphsTable,
        private readonly TranslatorInterface $translator,
        array $awsConfig,
    ) {
        parent::__construct($callsTable, $callFactory);

        $this->companiesTable = $companiesTable;
        $this->companiesLanguagesTable = $companiesLanguagesTable;
        $this->usersCompaniesLanguagesTable = $usersCompaniesLanguagesTable;
        $this->callsParagraphsTable = $callsParagraphsTable;
        $this->awsConfig = $awsConfig['api'];
        $this->env = $awsConfig['env'];
    }

    /**
     * @param int $companyId
     * @return bool
     * @throws Exception\StepIsAlreadyFinishedException
     * @throws GuzzleException
     * @throws NotFoundApiException
     * @throws ReflectionException
     */
    public function run(int $companyId): bool
    {
        $call = $this->getCall();
        if (!empty($call->getLanguage())) {
            throw new Exception\StepIsAlreadyFinishedException('Call language is already detected');
        }

        // skip step if call without file
        if ($call->getCallType() === Call::CALL_TYPE && is_null($call->getS3FilePath())) {
            return true;
        }

        $companyData = $this->companiesTable->getCompany($call->getCompanyId());
        /** @var Company $company */
        $company = $this->hydrate((array) $companyData->current(), Company::class);
        $this->setCompany($company);

        $language = $this->getLanguage($company, $call);
        $call->setLanguage($language);
        $this->callsTable->saveCall($call);

        $this->nextStepQueue = $this->getNextStep($call);

        return true;
    }

    /**
     * @param Company $company
     * @param Call $call
     * @return string
     * @throws GuzzleException
     * @throws ReflectionException
     */
    protected function getLanguage(Company $company, Call $call): string
    {
        $availableLanguages = array_column(
            $this->usersCompaniesLanguagesTable->getUserLanguages($call->getCompanyId(), $call->getAgentId())->toArray(
            ),
            'language'
        );
        if (empty($availableLanguages)) {
            $availableLanguages = array_column(
                $this->companiesLanguagesTable->getCompanyLanguages($call->getCompanyId())->toArray(),
                'language'
            );
        }

        if (count($availableLanguages) === 1) {
            return current($availableLanguages);
        }

        $detectedLanguage = match ($call->getCallType()) {
            Call::CALL_TYPE => $this->getAudioLanguage(
                $this->getFileFromS3ByFileName($call->getS3FilePath()),
                $call->getDuration()
            ),
            ChatCall::CHAT_CALL_TYPE => $this->getTextLanguage($company, $call),
            default => throw new \RuntimeException('Invalid type of call "' . $call->getId() . '"'),
        };

        return $this->getLanguageFromAvailableLanguages($detectedLanguage, $availableLanguages);
    }

    /**
     * @param string $detectedLanguage
     * @param array $availableLanguages
     * @return string
     */
    protected function getLanguageFromAvailableLanguages(string $detectedLanguage, array $availableLanguages): string
    {
        foreach ($availableLanguages as $language) {
            if (str_starts_with($language, $detectedLanguage . '-')) {
                return $language;
            }
        }

        return in_array(
            $detectedLanguage,
            $availableLanguages,
            true
        ) || empty($availableLanguages) ? $detectedLanguage : current($availableLanguages);
    }

    /**
     *
     * @param string $content
     * @param float $duration
     * @return string
     * @throws GuzzleException
     */
    protected function getAudioLanguage(string $content, float $duration): string
    {
        if ($duration < static::DEFAULT_WHISPER_CROPPED_AUDIO_LENGTH_FOR_LANGUAGE_DETECTION) {
            $croppedFrom = 0;
        } elseif ($duration < static::DEFAULT_WHISPER_CROPPED_AUDIO_LENGTH_FOR_LANGUAGE_DETECTION + static::DEFAULT_START_TIMESTAMP_FOR_LANGUAGE_DETECTION) {
            $croppedFrom = $duration - static::DEFAULT_WHISPER_CROPPED_AUDIO_LENGTH_FOR_LANGUAGE_DETECTION;
        } else {
            $croppedFrom = static::DEFAULT_START_TIMESTAMP_FOR_LANGUAGE_DETECTION;
        }

        $filePath = sys_get_temp_dir() . '/' . uniqid();
        $croppedFilePath = getcwd() . '/data/cropped-audio/' . uniqid() . '.mp3';
        file_put_contents($filePath, $content);
        $ffmpeg = FFMpeg::create();
        $audio = $ffmpeg->open($filePath);
        $audio->filters()->clip(
            TimeCode::fromSeconds($croppedFrom),
            TimeCode::fromSeconds(
                static::DEFAULT_WHISPER_CROPPED_AUDIO_LENGTH_FOR_LANGUAGE_DETECTION
            ),
        );
        $audio->save(new Mp3(), $croppedFilePath);
        $croppedContent = file_get_contents($croppedFilePath);
        unlink($filePath);
        unlink($croppedFilePath);

        return $this->translator->detectContentLanguage($croppedContent);
    }

    /**
     * @param Company $company
     * @param Call $call
     * @return string
     * @throws GuzzleException
     * @throws ReflectionException
     */
    protected function getTextLanguage(Company $company, Call $call): string
    {
        $paragraphsData = $this->callsParagraphsTable->getParagraphs(
            $company->getId(),
            $call->getId(),
            $company->getEncryptionKey()
        );
        $detectedText = '';
        foreach ($paragraphsData as $paragraphData) {
            /** @var Paragraph $paragraph * */
            $paragraph = $this->hydrate($paragraphData, Paragraph::class);
            $detectedText .= $paragraph->getText() . " ";
            if (mb_strlen($detectedText) > static::TEXT_SIZE_FOR_TEXT_LANGUAGE_DETECT) {
                break;
            }
        }
        return $this->translator->detectLanguage($detectedText);
    }

    /**
     *
     * @param Call $call
     * @return string
     */
    protected function getNextStep(Call $call): string
    {
        return match ($call->getCallType()) {
            Call::CALL_TYPE => TranscribingDistributionStep::CALL_DISTRIBUITION_TRANSCRIBING_QUEUE,
            ChatCall::CHAT_CALL_TYPE => TranslationStep::CALL_TRANSLATION_QUEUE,
            default => throw new \RuntimeException('Unknown call type "' . $call->getCallType() . '"'),
        };
    }

    /**
     *
     * @return string
     */
    public function getNextStepQueue(): string
    {
        return $this->nextStepQueue;
    }

    /**
     *
     * @return string
     */
    public function getQueueName(): string
    {
        return static::CALL_LANGUAGE_DETECTION_QUEUE;
    }

    /**
     *
     * @return string
     */
    public function getErrorQueueName(): string
    {
        return static::CALL_LANGUAGE_DETECTION_ERROR_QUEUE;
    }
}
