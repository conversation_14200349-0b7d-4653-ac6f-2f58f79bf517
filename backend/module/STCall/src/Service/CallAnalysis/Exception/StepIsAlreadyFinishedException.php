<?php

declare(strict_types=1);

namespace STCall\Service\CallAnalysis\Exception;

use Laminas\Http\Response;
use STRabbit\Service\Daemon\NotThrowableException;

class StepIsAlreadyFinishedException extends NotThrowableException
{
    /**
     *
     * @var int
     */
    protected $code = Response::STATUS_CODE_500;

    /**
     *
     * @var string
     */
    protected $message = 'Step is already finished';
}
