<?php

declare(strict_types=1);

namespace STClickhouse\Entity;

abstract class BaseTable
{
    public const DISTRIBUTED_DATA_TABLES_POSTFIX = '';

    /**
     *
     * @var \STClickhouse\Client\Client
     */
    protected \STClickhouse\Client\Client $client;

    /**
     *
     * @var string
     */
    protected string $tableName = '';

    /**
     *
     * @var array|null
     */
    protected ?array $tableColumns = null;


    /**
     *
     * @param \STClickhouse\Client\Client $client
     */
    public function __construct(\STClickhouse\Client\Client $client)
    {
        $this->client = $client;
    }

    /**
     *
     * @return \STClickhouse\Client\Client
     */
    public function getClient(): \STClickhouse\Client\Client
    {
        return $this->client;
    }

    /**
     *
     * @return array
     */
    public function getTableColumns(): array
    {
        if (is_null($this->tableColumns)) {
            $this->loadTableColumns();
        }
        return array_keys($this->tableColumns);
    }

    /**
     *
     * @param string $column
     * @param mixed $value
     * @return mixed
     */
    public function castValue(string $column, mixed $value): mixed
    {
        if (is_null($value)) {
            return null;
        }
        if (is_null($this->tableColumns)) {
            $this->loadTableColumns();
        }
        if (is_array($value) && !isset($value['type'])) {
            $result = [];
            foreach ($value as $option) {
                $result[] = $this->castValue($column, $option);
            }
            return $result;
        }
        $type = $this->tableColumns[$column]['type'] ?? '';
        if (str_contains($type, 'String') && is_string($value)) {
            return (string) $value;
        }
        if (str_contains($type, 'Float')) {
            return (float) $value;
        }
        if (str_contains($type, 'Int') || str_contains($type, 'Bool')) {
            return (int) $value;
        }
        if (str_contains($type, 'DateTime') && is_string($value)) {
            return \Carbon\Carbon::parse($value);
        }
        return $value;
    }

    /**
     *
     * @return string
     */
    protected function getTableName(): string
    {
        if (empty($this->tableName)) {
            $reflect = new \ReflectionClass(get_class($this));
            $className = $reflect->getShortName();
            $tableNameCamelCase = preg_replace('/Table$/', '', $className);
            $filter = new \Laminas\Filter\Word\CamelCaseToUnderscore();
            $this->tableName = strtolower($filter->filter($tableNameCamelCase));
        }
        return $this->tableName;
    }

    /**
     *
     * @return string
     */
    protected function getTableDataName(): string
    {
        return $this->getTableName() . static::DISTRIBUTED_DATA_TABLES_POSTFIX;
    }

    /**
     *
     * @description Old version of pagination. Delete method after move all to BaseTable::paginate()
     * @see BaseTable::paginate()
     *
     * @param string $sql
     * @param Pagination\Pagination $pagination
     * @param string|null $countSql
     * @return Pagination\Pagination
     */
    protected function selectWithPagination(
        string $sql,
        \STClickhouse\Entity\Pagination\Pagination $pagination,
        ?string $countSql = null,
    ): \STClickhouse\Entity\Pagination\Pagination {
        $pagination->getParams()->addAvailableColumns($this->getTableColumns());
        $pagination->normalizeFilter();
        $pagination->setFilter($this->normalizeValues($pagination->getFilter()));
        $this->getClient()->selectWithPagination($sql, $pagination, $countSql);
        return $pagination;
    }

    /**
     *
     * @description Improved version of pagination. Main advantage is getting list if ids of searched records and then getting data only for them.
     * @description Old version is BaseTable::selectWithPagination()
     * @see BaseTable::selectWithPagination()
     *
     * @param string $candidatesSql
     * @param string $candidatesIdColumnName
     * @param Pagination\CandidatePagination $pagination
     * @return \STClickhouse\Entity\Pagination\CandidatePagination
     */
    protected function paginate(
        string $candidatesSql,
        string $candidatesIdColumnName,
        \STClickhouse\Entity\Pagination\CandidatePagination $pagination,
    ): \STClickhouse\Entity\Pagination\CandidatePagination {
        $pagination->setFilter($this->normalizeValues($pagination->getFilter()));
        return $this->getClient()->paginate($candidatesSql, $candidatesIdColumnName, $pagination);
    }

    /**
     *
     * @param array $filter
     * @return array
     * @throws \RuntimeException
     */
    protected function normalizeValues(array $filter): array
    {
        $normalizedFilter = [];
        foreach ($filter as $column => $value) {
            if (!in_array($column, $this->getTableColumns()) && !in_array($value['column'] ?? null, $this->getTableColumns())) {
                //throw new \RuntimeException('Invalid column "' . $column .  '"');
            }
            $value = $this->castValue($value['column'] ?? $column, $value);
            $normalizedFilter[$column] = $value;
        }
        return $normalizedFilter;
    }

    /**
     *
     * @return array
     */
    protected function loadTableColumns(): array
    {
        $this->tableColumns = [];
        $sql = '
            SELECT
                name,
                type
            FROM
                system.columns
            WHERE
                database = \'' . $this->client->getDatabase() . '\'
                AND `table` = \'' . $this->getTableName() . '\'
        ';
        $columns = $this->client->selectAll($sql);
        foreach ($columns as $column) {
            $this->tableColumns[$column['name']] = $column;
        }
        return $this->tableColumns;
    }

    /**
     *
     * @param string|null $tableName
     * @return bool
     */
    protected function truncate(?string $tableName = null): bool
    {
        $this->getClient()->truncate($tableName ?? $this->getTableName());
        return true;
    }
}
