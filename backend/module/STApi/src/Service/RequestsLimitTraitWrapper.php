<?php

declare(strict_types=1);

namespace STApi\Service;

class RequestsLimitTraitWrapper
{
    /**
     *
     * @var \Laminas\Http\Request
     */
    private static \Laminas\Http\Request $request;

    /**
     *
     * @param \Laminas\Http\Request $request
     * @return void
     */
    public static function setRequest(\Laminas\Http\Request $request): void
    {
        static::$request = $request;
    }

    /**
     *
     * @return \Laminas\Http\Request
     */
    public static function getRequest(): \Laminas\Http\Request
    {
        return self::$request;
    }
}
