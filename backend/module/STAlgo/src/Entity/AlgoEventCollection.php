<?php

declare(strict_types=1);

namespace STAlgo\Entity;

use RuntimeException;
use STLib\Expand\Collection;
use Traversable;

/**
 * @extends Traversable<array-key,AlgoEvent>
 */
class AlgoEventCollection extends Collection
{
    /**
     *
     * @param mixed $algoApi
     * @param string|int|null $key
     * @return Collection
     * @throws RuntimeException
     */
    public function add(mixed $algoEvent, string|int|null $key = null): Collection
    {
        if (!($algoEvent instanceof AlgoEvent)) {
            throw new RuntimeException('Algo<PERSON><PERSON> must be an instance of "\STAlgo\Entity\AlgoEvent"');
        }
        parent::add($algoEvent, $key ?? $algoEvent->getAlgoEvent() . '-' . $algoEvent->getAlgoApiId());
        return $this;
    }
}
