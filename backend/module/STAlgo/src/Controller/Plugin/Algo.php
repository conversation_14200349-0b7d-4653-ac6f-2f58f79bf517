<?php

declare(strict_types=1);

namespace STAlgo\Controller\Plugin;

use Laminas\Mvc\Controller\Plugin\AbstractPlugin;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;
use STAlgo\Service\AiSolutionsCommutatorService;
use STAlgo\Service\AlgoApi\AlgoApiUpdaterService;
use STAlgo\Service\AlgoApi\CompanyAlgoApisSelectorService;
use STAlgo\Service\AlgoApiService;
use STAlgo\Service\AlgoEventService;
use STAlgo\Service\EventsAlgoApiRequestCreatorService;
use STAlgo\Service\Industry\IndustryConnectorService;
use STAlgo\Service\Industry\IndustrySelectorService;
use STLib\Mvc\Controller\AbstractController;

/**
 * @method AbstractController getController()
 */
class Algo extends AbstractPlugin
{
    /**
     *
     * @return AlgoApiService
     */
    public function api(): AlgoApiService
    {
        return $this->getController()->getServiceManager()->get(AlgoApiService::class);
    }

    /**
     *
     * @return AlgoEventService
     */
    public function algoEvent(): AlgoEventService
    {
        return $this->getController()->getServiceManager()->get(AlgoEventService::class);
    }

    /**
     *
     * @return AiSolutionsCommutatorService
     */
    public function aiSolutionsCommutator(): AiSolutionsCommutatorService
    {
        return $this->getController()->getServiceManager()->get(AiSolutionsCommutatorService::class);
    }

    /**
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function eventsAlgoApiRequestCreator(): EventsAlgoApiRequestCreatorService
    {
        return $this->getController()->getServiceManager()->get(EventsAlgoApiRequestCreatorService::class);
    }

    /**
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function industryConnector(): IndustryConnectorService
    {
        return $this->getController()->getServiceManager()->get(IndustryConnectorService::class);
    }

    /**
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function industrySelector(): IndustrySelectorService
    {
        return $this->getController()->getServiceManager()->get(IndustrySelectorService::class);
    }

    /**
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function algoApiUpdater(): AlgoApiUpdaterService
    {
        return $this->getController()->getServiceManager()->get(AlgoApiUpdaterService::class);
    }

    /**
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function companyAlgoApiSelector(): CompanyAlgoApisSelectorService
    {
        return $this->getController()->getServiceManager()->get(CompanyAlgoApisSelectorService::class);
    }
}
