<?php

declare(strict_types=1);

namespace STAlgo\Service\AlgoEvents\RequestCreation;

use STAlgo\Service\ParamsBuilding\RequestParams;
use STIndustry\Data\IndustriesTable;

class LlmEventsAlgoApiRequest implements EventsAlgoApiRequestInterface
{
    private const string URL_PATH = '/api/algo/promptV1';

    public function __construct(
        private readonly string $apiUrl,
        private readonly RequestParams $params,
    ) {
    }

    public function getEnv(): ?string
    {
        return null;
    }

    public function getUrl(): string
    {
        return $this->apiUrl . self::URL_PATH;
    }

    public function getParams(): array
    {
        return $this->params->toArray();
    }

    public function getAlgoApiId(): int
    {
        return 0;
    }

    public function getIndustryId(): int
    {
        return IndustriesTable::LLM;
    }
}
