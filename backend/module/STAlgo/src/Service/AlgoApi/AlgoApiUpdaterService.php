<?php

declare(strict_types=1);

namespace STAlgo\Service\AlgoApi;

use Exception;
use GuzzleHttp\Exception\GuzzleException;
use ReflectionException;
use STAlgo\Data\AlgoApisTable;
use STAlgo\Service\AlgoApi\Update\AiAlgoApisGetter;
use STAlgo\Service\AlgoApi\Update\AlgoApisAnalyzer;
use STApi\Entity\Exception\NotFoundApiException;

final readonly class AlgoApiUpdaterService
{
    public function __construct(
        private AiAlgoApisGetter $aiAlgoApisGetter,
        private AlgoApisAnalyzer $algoApisAnalyzer,
        private AlgoApisTable $algoApisTable,
    ) {
    }

    /**
     * @return void
     * @throws GuzzleException
     * @throws ReflectionException
     * @throws NotFoundApiException
     * @throws Exception
     */
    public function update(): void
    {
        $providedAlgoApis = $this->aiAlgoApisGetter->get();
        $analyzedAlgoApis = $this->algoApisAnalyzer->analyze($providedAlgoApis);

        $this->algoApisTable->saveAlgoApis($analyzedAlgoApis);
    }
}
