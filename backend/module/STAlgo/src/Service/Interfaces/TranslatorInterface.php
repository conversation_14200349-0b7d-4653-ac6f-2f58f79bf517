<?php

declare(strict_types=1);

namespace STAlgo\Service\Interfaces;

use GuzzleHttp\Exception\GuzzleException;
use STApi\Entity\Exception\ThirdPartyApiException;
use STTranslation\Service\Drivers\GoogleDriver;

interface TranslatorInterface
{
    /**
     * @throws GuzzleException
     * @throws ThirdPartyApiException
     */
    public function translate(?string $text, string $driverName = GoogleDriver::DRIVER_NAME): string;
}
