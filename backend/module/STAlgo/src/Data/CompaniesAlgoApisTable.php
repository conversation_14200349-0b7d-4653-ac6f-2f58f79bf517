<?php

declare(strict_types=1);

namespace STAlgo\Data;

use Lam<PERSON>\Db\ResultSet\ResultSetInterface;
use Laminas\Db\Sql\Select;
use STApi\Entity\Exception\NotFoundApiException;
use STCompany\Entity\AlgoApi\AlgoApi;
use STLib\Db\AbstractTable;

class CompaniesAlgoApisTable extends AbstractTable
{
    public static string $table = 'companies_algo_apis';

    public const string NER_ANALYZE_METHOD = '/textV2';

    /**
     *
     * @param int $companyId
     * @return ResultSetInterface
     */
    public function getCompanyAlgoApiIds(int $companyId): ResultSetInterface
    {
        $select = $this->tableGateway->getSql()->select();
        $this->joinNotDeletedAlgoApis($select);
        $select
            ->columns([
                'algo_api_id',
            ])
            ->where([
                'company_id' => $companyId,
            ]);
        return $this->tableGateway->selectWith($select);
    }

    /**
     *
     * @param int $companyId
     * @param array $algoApiIds
     * @return int
     */
    public function saveCompanyAlgoApiIds(int $companyId, array $algoApiIds): int
    {
        if (empty($algoApiIds)) {
            return 0;
        }
        $data = [];
        foreach ($algoApiIds as $algoApiId) {
            $data[] = [
                'algo_api_id' => $algoApiId,
                'company_id' => $companyId,
            ];
        }
        return $this->multiInsert($data, useTransaction: false);
    }

    /**
     *
     * @param int $companyId
     * @return int
     */
    public function deleteCompaniesAlgoApis(int $companyId): int
    {
        return $this->tableGateway->delete([
            'company_id' => $companyId,
        ]);
    }

    public function getCompaniesAlgoApisByAlgoApiId(int $algoApiId): ResultSetInterface
    {
        $select = $this->tableGateway->getSql()->select();
        $select->where(['algo_api_id' => $algoApiId]);

        return $this->tableGateway->selectWith($select);
    }

    /**
     * @throws NotFoundApiException
     */
    public function getAlgoApi(int $algoApiId, int $companyId): ResultSetInterface
    {
        $select = $this->tableGateway->getSql()
            ->select()
            ->columns(['company_id'])
            ->where([
                self::$table . '.company_id' => $companyId,
                'aa.algo_api_id' => $algoApiId
            ]);
        $this->joinNotDeletedAlgoApis($select);

        $result = $this->tableGateway->selectWith($select);
        if ($result->count() === 0) {
            throw new NotFoundApiException('Company algo api not found');
        }

        return $result;
    }

    public function saveAlgoApi(AlgoApi $algoApi): void
    {
        $data = [
            'algo_api_id' => $algoApi->getId(),
            'company_id' => $algoApi->getCompanyId(),
        ];

        $this->tableGateway->insert($data);
    }

    public function deleteAlgoApi(int $algoApiId, int $companyId): void
    {
        $this->tableGateway->delete([
            'algo_api_id' => $algoApiId,
            'company_id' => $companyId,
        ]);
    }

    public function getDirectNerAlgoApis(int $companyId): ResultSetInterface
    {
        $select = $this->tableGateway->getSql()
            ->select()
            ->columns(['company_id'])
            ->where([
                self::$table . '.company_id' => $companyId,
                'analyze_method' => self::NER_ANALYZE_METHOD
            ]);
        $this->joinNotDeletedAlgoApis($select);

        return $this->tableGateway->selectWith($select);
    }

    private function joinNotDeletedAlgoApis(Select $select): void
    {
        $select
            ->join(
                [
                    'aa' => 'algo_apis',
                ],
                'aa.algo_api_id = ' . self::$table . '.algo_api_id'
            )
            ->where([
                'aa.is_deleted' => 0
            ]);
    }
}
