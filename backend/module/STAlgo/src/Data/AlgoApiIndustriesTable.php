<?php

namespace STAlgo\Data;

use Laminas\Db\Sql\Select;
use STAlgo\Entity\Industry\Industry;
use STAlgo\Entity\Industry\IndustryCollection;
use STApi\Entity\Exception\NotFoundApiException;
use STLib\Db\HydratedAbstractTable;
use STLib\Mvc\Data\CollectionResultSet;

class AlgoApiIndustriesTable extends HydratedAbstractTable
{
    public static string $table = 'algo_apis_industries';
    public static string $entityName = Industry::class;
    public static string $collectionName = IndustryCollection::class;

    public static function getTableName(): string
    {
        return self::$table;
    }

    public static function getEntityName(): string
    {
        return self::$entityName;
    }

    public static function getCollectionName(): string
    {
        return self::$collectionName;
    }

    public function getIndustries(int $algoApiId): IndustryCollection
    {
        $select = $this->tableGateway->getSql()
            ->select()
            ->columns(['algo_api_id']);
        $this->joinIndustries($select);
        $select
            ->where([
                self::$table . '.algo_api_id' => $algoApiId,
            ]);

        /**
         * @var CollectionResultSet $result
         */
        $result = $this->tableGateway->selectWith($select);

        return $result->getCollection();
    }

    /**
     * @throws NotFoundApiException
     */
    public function getIndustry(int $industryId, int $algoApiId): Industry
    {
        $select = $this->tableGateway->getSql()
            ->select()
            ->columns(['algo_api_id']);
        $this->joinIndustries($select);
        $select
            ->where([
                self::$table . '.algo_api_id' => $algoApiId,
                'i.industry_id' => $industryId
            ]);

        $result = $this->tableGateway->selectWith($select);
        if ($result->count() === 0) {
            throw new NotFoundApiException('Algo api industry not found');
        }

        return $result->current();
    }

    public function saveIndustry(Industry $industry): void
    {
        $data = [
            'industry_id' => $industry->getId(),
            'algo_api_id' => $industry->getAlgoApiId(),
        ];

        $this->tableGateway->insert($data);
    }

    public function deleteIndustry(int $industryId, int $algoApiId): void
    {
        $this->tableGateway->delete([
            'industry_id' => $industryId,
            'algo_api_id' => $algoApiId,
        ]);
    }

    private function joinIndustries(Select $select): void
    {
        $select->join(
            [
                'i' => 'industries',
            ],
            'i.industry_id = ' . self::$table . '.industry_id'
        );
    }
}
