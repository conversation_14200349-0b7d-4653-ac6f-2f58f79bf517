<?php

declare(strict_types=1);

namespace STTranslation\Service\Drivers;

use GuzzleHttp\Exception\GuzzleException;
use RuntimeException;
use STTranslation\Service\Drivers\Interfaces\AlgoClientInterface;
use STTranslation\Service\LanguageDetector;

class AlgoDriver implements DriverInterface
{
    public const string DRIVER_NAME = 'algo';

    public function __construct(
        private readonly AlgoClientInterface $algoClient,
        private readonly LanguageDetector $languageDetector,
    ) {
    }

    public function translateBatch(array $texts, string $languageCode): array
    {
        $paragraphsData = [];
        foreach ($texts as $key => $text) {
            $paragraphsData[] = ['id' => $key, 'text' => $text];
        }

        $requestData = [
            'language' => $languageCode,
            'paragraphs' => $paragraphsData,
        ];

        $responseData = $this->algoClient->translate($requestData);
        if (!$this->isResponseOk($responseData)) {
            throw new RuntimeException('Unexpected Algo translator response: ' . json_encode($responseData));
        }

        $translatedTexts = [];
        foreach ($responseData['results']['segments'] as $translation) {
            $translatedTexts[] = $translation;
        }

        return $translatedTexts;
    }

    /**
     * @throws GuzzleException
     * @throws ThirdPartyApiException
     */
    public function translate(string $text): string
    {
        $languageCode = $this->languageDetector->detectLanguageByText($text);
        $result = $this->translateBatch([$text], $languageCode);

        return $result ? current($result) : '';
    }

    private function isResponseOk(array $responseData): bool
    {
        if (!array_key_exists('status', $responseData)) {
            return false;
        }
        if ($responseData['status'] !== 'ok') {
            return false;
        }
        if (!array_key_exists('results', $responseData)) {
            return false;
        }
        if (!is_array($responseData['results'])) {
            return false;
        }
        if (!array_key_exists('segments', $responseData['results'])) {
            return false;
        }
        if (!is_array($responseData['results']['segments'])) {
            return false;
        }

        return true;
    }

    public function isSupportBatchTranslation(): bool
    {
        return true;
    }

    public function needTextsCombine(): bool
    {
        return false;
    }

    public function getMaxTextLength(): int
    {
        throw new RuntimeException('getMaxTextLength() is not implemented in Algo driver.');
    }
}
