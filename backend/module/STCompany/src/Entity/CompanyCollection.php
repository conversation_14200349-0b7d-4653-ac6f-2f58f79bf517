<?php

declare(strict_types=1);

namespace STCompany\Entity;

class CompanyCollection extends \STLib\Expand\Collection
{
    /**
     *
     * @param \STCompany\Entity\Company $company
     * @param mixed $key
     * @return \STCompany\Entity\Company
     * @throws \RuntimeException
     */
    public function add(mixed $company, string|int|null $key = null): \STLib\Expand\Collection
    {
        if (!($company instanceof Company)) {
            throw new \RuntimeException('Company must be an instance of "\STCompany\Entity\Company"');
        }
        parent::add($company, $key ?? $company->getId());
        return $this;
    }

    /**
     *
     * @return array
     */
    public function toArray(): array
    {
        $result = [];
        foreach ($this as $company) {
            $result[] = $company->toArray();
        }
        return $result;
    }
}
