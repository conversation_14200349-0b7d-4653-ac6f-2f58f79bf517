<?php

declare(strict_types=1);

namespace STCompany\Data;

use STApi\Entity\Exception\NotFoundApiException;

class ClientsTable extends \STClickhouse\Entity\BaseTable
{
    use \STClickhouse\Data\QueriesTrait;
    use \STApi\Service\RequestsLimitTrait;

    /**
     * @todo Drop or rebuild this search
     *
     * @param int $companyId
     * @param string $clientSearch
     * @param int $limit
     * @param array $teamIds
     * @return array
     */
    public function getClientsByIdOrName(int $companyId, string $clientSearch, int $limit, array $teamIds = []): array
    {
        // phpcs:disable
        $sql = '
            SELECT
                client_id,
                client_name,
                client_status,
                client_value,
                client_source
            FROM
            (
                SELECT DISTINCT
                    POSITION(lowerUTF8(client_name), \'' . strtolower($clientSearch) . '\') `position`,
                    cl.client_id client_id,
                    cl.client_name client_name,
                    cl.status client_status,
                    cl.value client_value,
                    cl.source client_source
                FROM
                    (
                        ' . $this->getFinalTableSqlUsingGroupBy(
                            'clients',
                            [
                                'company_id',
                                'client_id',
                            ],
                            'created',
                            [
                                'client_name',
                                'status',
                                'value',
                                'source',
                            ],
                            [
                                'company_id' => $companyId,
                                [
                                    'type' => 'expression',
                                    'value' => 'client_name LIKE \'%' . $clientSearch . '%\' OR client_id LIKE \'%' . $clientSearch . '%\'',
                                ],
                            ]
                        ) . '
                    ) cl
                INNER JOIN
                    (
                        ' . $this->getFinalTableSqlUsingGroupBy(
                            'calls',
                            [
                                'company_id',
                                'client_id',
                                'agent_id',
                            ],
                            'created',
                            [
                                'company_id' => $companyId,
                            ]
                        ) . '
                    ) c
                    ON c.client_id = cl.client_id
                INNER JOIN
                    (
                        SELECT
                            *
                        FROM
                            dictionary(users_teams)
                    ) ut
                    ON ut.user_id = c.agent_id
                WHERE
                    cl.company_id = \'' . $companyId . '\'
        ';
        if (count($teamIds) > 0) {
            $sql .= '
                    AND ut.team_id IN (' . implode(',', $teamIds) . ')
            ';
        }
        $sql .= '
            ORDER BY
                `position`
            LIMIT ' . $limit . '
        )';
        // phpcs:enable
        return $this->getClient()->selectAll($sql);
    }

    public function getClientIdsByCompanyId(int $companyId): array
    {
        $sql = '
            SELECT DISTINCT
                client_id
            FROM
                clients
            WHERE
                company_id = ' . $companyId . '
        ';

        return $this->getClient()->selectColumn($sql, 'client_id');
    }

    /**
     *
     * @param int $companyId
     * @param string $clientId
     * @return array
     * @throws NotFoundApiException
     */
    public function getClientById(int $companyId, string $clientId): array
    {
        // phpcs:disable
        $sql = '
            SELECT
                client_id,
            FROM
            (
                SELECT DISTINCT
                    cl.client_id client_id
                FROM
                    (
                        ' . $this->getFinalTableSqlUsingGroupBy(
                            'clients',
                            [
                                'company_id',
                                'client_id',
                            ],
                            'created',
                            [],
                            [
                                'company_id' => $companyId,
                            ]
                        ) . '
                    ) cl
                WHERE
                    cl.company_id = \'' . $companyId . '\'
                    AND lowerUTF8(cl.client_id) = \'' . strtolower($clientId) . '\'
            )
        ';
        // phpcs:enable

        $client = $this->getClient()->selectOne($sql);

        if ($client === null) {
            throw new \STApi\Entity\Exception\NotFoundApiException('Client not found');
        }

        return $client;
    }

    /**
     *
     * @param int $companyId
     * @return array
     */
    public function getClientStatusesByCompanyId(int $companyId): array
    {
        $sql = '
            SELECT
                DISTINCT(cl.status) status
            FROM
                clients cl
            WHERE
                cl.company_id = ' . $companyId . '
                AND cl.status IS NOT NULL
                AND cl.status != \'\'
        ';

        return array_values(
            array_filter(
                $this->getClient()->selectColumn($sql, 'status')
            )
        );
    }

    /**
     *
     * @param int $companyId
     * @return array
     */
    public function getClientCountriesByCompanyId(int $companyId): array
    {
        $sql = '
            SELECT
                DISTINCT(cl.country) country
            FROM
                clients cl
            WHERE
                cl.company_id = ' . $companyId . '
                AND cl.country IS NOT NULL
                AND cl.country != \'\'
        ';

        return array_values($this->getClient()->selectColumn($sql, 'country'));
    }

    /**
     *
     * @param int $companyId
     * @return array
     */
    public function getClientSourcesByCompanyId(int $companyId): array
    {
        $sql = '
            SELECT
                DISTINCT(cl.source) source
            FROM
                clients cl
            WHERE
                cl.company_id = ' . $companyId . '
                AND cl.source IS NOT NULL
                AND cl.source != \'\'
        ';

        return array_values($this->getClient()->selectColumn($sql, 'source'));
    }

    /**
     *
     * @param int $companyId
     * @return array
     */
    public function getClientCampaignIdsByCompanyId(int $companyId): array
    {
        $sql = '
            SELECT
                DISTINCT(cl.campaign_id) campaign_id
            FROM
                clients cl
            WHERE
                cl.company_id = ' . $companyId . '
                AND cl.campaign_id IS NOT NULL
                AND cl.campaign_id != \'\'
        ';

        return array_values($this->getClient()->selectColumn($sql, 'campaign_id'));
    }

    /**
     *
     * @param \STCompany\Entity\Client $client
     * @return void
     */
    public function saveClient(\STCompany\Entity\Client $client): void
    {
        $clientData = $client->toArray();
        $clientData['client_id'] = $clientData['id'];
        $clientData['client_name'] = $clientData['name'];
        $clientData['is_converted'] = (int) $clientData['is_converted'];
        unset($clientData['id']);
        unset($clientData['name']);

        $columns = array_keys($clientData);

        $this->getClient()->insert($this->getTableName(), [$clientData], $columns);
    }
}
