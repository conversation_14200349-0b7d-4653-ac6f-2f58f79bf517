<?php

declare(strict_types=1);

namespace STCompany\Data;

use Laminas\Db\ResultSet\ResultSet;
use STLib\Db\AbstractTable;

class PermissionsTable extends AbstractTable
{
    public const string READ_PERMISSION = 'read';
    public const string WRITE_PERMISSION = 'write';
    public const int COMPANY = 1;
    public const int CALL_STATISTICS = 3;
    public const int CLIENTS = 5;
    public const int USERS = 7;
    public const int EVENTS = 8;
    public const int ROLES = 20;
    public const int MANUAL_CALL_UPLOAD = 21;
    public const int COMPANY_VIEW_LIKE_ANOTHER_ROLE = 22;
    public const int REPORTS = 23;
    public const int SEARCH = 24;
    public const int CALL = 25;
    public const int CALL_REVIEW_CALL = 26;
    public const int CALL_COMMENTS = 27;
    public const int CALL_SPEAKERS = 28;
    public const int CALL_PARAGRAPHS = 29;
    public const int CALL_AUDIO = 30;
    public const int EMS = 31;
    public const int DOWNLOAD_CSV_REPORT = 32;
    public const int FLOWS = 33;
    public const int WEBHOOKS = 34;

    /**
     *
     * @return ResultSet
     */
    public function getPermissions(): ResultSet
    {
        return $this->tableGateway->select();
    }
}
