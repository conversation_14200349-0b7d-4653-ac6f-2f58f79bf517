<?php

declare(strict_types=1);

namespace STCompany\Service\CompanyCreation\Onboarding;

use Exception;
use STCompany\Entity\Company;
use STCompany\Service\CompanyService;
use STCompany\Service\Interfaces\ApplicationCreatorInterface;
use STOnboarding\Entity\OnboardingForm;

class CompanyCreator
{
    public function __construct(
        private readonly CompanyBuilder $companyBuilder,
        private readonly CompanyService $companyService,
        private readonly PermissionsCreator $permissionsCreator,
        private readonly UsersCreator $usersCreator,
        private readonly EventsCreator $eventsCreator,
        private readonly ApplicationCreatorInterface $applicationCreator,
    ) {
    }

    /**
     * @throws Exception
     */
    public function createFromOnboardingForm(OnboardingForm $onboardingForm): Company
    {
        $company = $this->companyBuilder->createFromOnboardingForm($onboardingForm);
        $this->companyService->saveCompany($company);

        $company->initStartBalance();
        $this->companyBuilder->setAwsData($company);

        $users = $onboardingForm->getUsers();
        $events = $onboardingForm->getActiveIndustryEvents();

        $this->permissionsCreator->createPermissions($company);
        $this->usersCreator->createUsers($company, $users, $onboardingForm->getInviteLink());
        $this->eventsCreator->createEvents($company, $events);

        $this->applicationCreator->generateApplication($company);

        $this->companyService->saveCompany($company);

        return $company;
    }
}
