<?php

declare(strict_types=1);

namespace STEms\Daemon;

use STApi\Entity\Exception\NotFoundApiException;
use STCall\Service\AwsTrait;
use STCompany\Service\CompanyService;
use STCompany\Service\EventService;
use STEms\Data\EmsDataSetExamplesTable;
use STEms\Entity\DataSet;
use STEms\Service\EmsDataSetService;
use STEms\Service\EmsDataSetExampleService;
use STSlack\Service\SlackService;

class EmsDataSetExportDaemon extends \STRabbit\Entity\AbstractDaemon
{
    use AwsTrait;
    use \STLib\Expand\CsvConverter;

    use \STLib\Mvc\Hydrator\BaseHydratorTrait;

    public const QUEUE = 'ems-data-set-export';
    public const QUEUE_ERROR = 'ems-data-set-export-error';
    private const ONE_WEEK_IN_MINUTES = 60 * 24 * 7;

    private const COLUMN_MAIN_EVENT = 'main_event';
    private const COLUMN_MAIN_HIGHLIGHT = 'main_highlight';
    private const COLUMN_MAIN_EN_HIGHLIGHT = 'main_en_highlight';
    private const COLUMN_TEXT = 'text';
    private const COLUMN_EN_TEXT = 'en_text';
    private const COLUMN_LANGUAGE = 'language';
    private const COLUMN_STATUS = 'status';
    private const COLUMN_ADDITIONAL_EVENT = 'event';
    private const COLUMN_ADDITIONAL_EVENT_HIGHLIGHT = 'highlight';
    private const COLUMN_ADDITIONAL_EVENT_EN_HIGHLIGHT = 'en_highlight';
    private const CSV_COLUMNS = [
        self::COLUMN_MAIN_EVENT,
        self::COLUMN_MAIN_HIGHLIGHT,
        self::COLUMN_MAIN_EN_HIGHLIGHT,
        self::COLUMN_TEXT,
        self::COLUMN_EN_TEXT,
        self::COLUMN_LANGUAGE,
        self::COLUMN_STATUS,
    ];
    private const ADDITIONAL_EVENTS_COUNT = 4;

    /**
     *
     * @param string $message
     * @return void
     * @throws \JsonException
     * @throws NotFoundApiException
     */
    public function handle(string $message): void
    {
        /** @var EmsDataSetExampleService $emsDataSetExampleService */
        $emsDataSetExampleService = $this->params()->offsetGet(EmsDataSetExampleService::class);

        $data = json_decode($message, false, 512, JSON_THROW_ON_ERROR);
        $dataSetIds = $data->data_set_ids;
        $statuses = $data->statuses;

        $awsConfig = $this->params()->offsetGet('aws_config');
        $this->setAwsConfig($awsConfig['api']);
        $this->setEnv($awsConfig['env']);

        $dataSetExamples = $emsDataSetExampleService->exportDataSetExamples(
            $dataSetIds,
            $statuses
        );

        if (empty($dataSetExamples)) {
            return;
        }

        $this->handleDataSetConfirmation($dataSetExamples, $dataSetIds);
    }

    /**
     * @param array $dataSetExamples
     * @param array $dataSetIds
     * @return void
     * @throws NotFoundApiException
     */
    private function handleDataSetConfirmation(array $dataSetExamples, array $dataSetIds): void
    {
        /** @var SlackService $slackService */
        $slackService = $this->params()->offsetGet(SlackService::class);
        $dataSet = null;
        $company = null;
        $event = null;
        // if only one data set id added, we can get info about company and dataset and add to slack message
        if (count($dataSetIds) === 1) {
            $dataSetId = current($dataSetIds);

            /** @var EmsDataSetService $emsDataSetService */
            $emsDataSetService = $this->params()->offsetGet(EmsDataSetService::class);
            /** @var CompanyService $companyService */
            $companyService = $this->params()->offsetGet(CompanyService::class);
            /** @var EventService $eventService */
            $eventService = $this->params()->offsetGet(EventService::class);

            $dataSet = $emsDataSetService->getDataSet($dataSetId);
            $company = $companyService->getCompany($dataSet->getCompanyId());
            $event = $eventService->getEvent($company->getId(), $dataSet->getEventId());
        }

        $dataSetFileLink = $this->saveDataSetFile(
            $this->convertDataSetExamplesToCsv($dataSetExamples),
            $dataSet
        );

        $slackService->sendEmsDataSetConfirmedNotification(
            $dataSetFileLink,
            $company,
            $event
        );
    }

    /**
     * @param string $dataSetFileContent
     * @param DataSet|null $dataSet
     * @return string
     */
    private function saveDataSetFile(string $dataSetFileContent, ?DataSet $dataSet): string
    {
        $bucketName = 'ems-data-set-examples';
        $fileName = $this->getSavingDataSetFileName($dataSet);

        $this->saveFileToBucket($dataSetFileContent, $fileName, $bucketName);

        return $this->getPreSignedUrlForFile($fileName, $bucketName, self::ONE_WEEK_IN_MINUTES);
    }

    /**
     * @param DataSet|null $dataSet
     * @return string
     */
    private function getSavingDataSetFileName(?DataSet $dataSet): string
    {
        $fileName = '';
        if ($dataSet instanceof DataSet) {
            $fileName .=
                $dataSet->getDataSetId()
                . '-'
                . filter_var($dataSet->getName(), FILTER_SANITIZE_URL)
                . '-';
        }
        $fileName .=
            \Carbon\Carbon::now()->format('YmdHis')
            . '.csv';
        return $fileName;
    }

    /**
     * @param array $dataSetExamples
     * @return string|null
     */
    private function convertDataSetExamplesToCsv(array $dataSetExamples): ?string
    {
        $dataSetExamplesRows = [];

        foreach ($dataSetExamples as $dataSetExampleIndex => $dataSetExample) {
            $additionalEvents = $dataSetExample['additional_events'];
            $mainHighlight = '';
            $mainEnHighlight = '';

            foreach ($additionalEvents as $additionalEvent) {
                if (
                    $additionalEvent['event_id'] === $dataSetExample['event_id']
                    && $dataSetExample['status'] !== EmsDataSetExamplesTable::STATUS_NEUTRAL
                ) {
                    $mainHighlight = $additionalEvent['highlight'];
                    $mainEnHighlight = $additionalEvent['en_highlight'];
                }
            }

            foreach (self::CSV_COLUMNS as $i => $csvColumn) {
                $dataSetExamplesRows[$dataSetExampleIndex][$i] = match ($csvColumn) {
                    self::COLUMN_MAIN_EVENT => $dataSetExample['event_name'],
                    self::COLUMN_MAIN_HIGHLIGHT => $mainHighlight,
                    self::COLUMN_MAIN_EN_HIGHLIGHT => $mainEnHighlight,
                    self::COLUMN_TEXT => $dataSetExample['text'],
                    self::COLUMN_EN_TEXT => $dataSetExample['en_text'],
                    self::COLUMN_LANGUAGE => $dataSetExample['language'],
                    self::COLUMN_STATUS => $dataSetExample['status'],
                };
            }

            // Add additional events to rows
            foreach (range(0, self::ADDITIONAL_EVENTS_COUNT - 1) as $additionalEventIndex) {
                $dataSetExamplesRows[$dataSetExampleIndex][] = isset($additionalEvents[$additionalEventIndex])
                    ? $additionalEvents[$additionalEventIndex]['event_name']
                    : ''
                ;
                $dataSetExamplesRows[$dataSetExampleIndex][] = isset($additionalEvents[$additionalEventIndex])
                    ? $additionalEvents[$additionalEventIndex]['highlight']
                    : ''
                ;
                $dataSetExamplesRows[$dataSetExampleIndex][] = isset($additionalEvents[$additionalEventIndex])
                    ? $additionalEvents[$additionalEventIndex]['en_highlight']
                    : ''
                ;
            }
        }

        $csvColumns = self::CSV_COLUMNS;

        // Add columns for additional events
        foreach (range(0, self::ADDITIONAL_EVENTS_COUNT - 1) as $index) {
            $csvColumns[] = self::COLUMN_ADDITIONAL_EVENT;
            $csvColumns[] = self::COLUMN_ADDITIONAL_EVENT_HIGHLIGHT;
            $csvColumns[] = self::COLUMN_ADDITIONAL_EVENT_EN_HIGHLIGHT;
        }

        return  $this->convertArrayToCsv($dataSetExamplesRows, $csvColumns);
    }
}
