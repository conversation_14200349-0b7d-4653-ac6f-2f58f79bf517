<?php

declare(strict_types=1);

namespace STEms\Data;

use STApi\Entity\Exception\NotFoundApiException;

class EmsDataSetExamplesTable extends \STClickhouse\Entity\BaseTable
{
    use \STClickhouse\Data\QueriesTrait;

    public const STATUS_UNSORTED = 'unsorted';
    public const STATUS_CONFIRMED = 'confirmed';
    public const STATUS_NEUTRAL = 'neutral';
    public const STATUS_POTENTIAL = 'potential';

    /**
     * @param string $dataSetExampleId
     * @param int $companyId
     * @param bool $isRemoved
     * @return array|null
     * @throws NotFoundApiException
     */
    public function getDataSetExample(string $dataSetExampleId, int $companyId, bool $isRemoved = false): ?array
    {
        // phpcs:disable
        $sql = '
            SELECT
                *
            FROM 
                (
                    ' . $this->getFinalTableSqlUsingGroupBy(
                        'ems_data_set_examples',
                        [
                            'data_set_example_id',
                        ],
                        'updated_at',
                        [
                            'data_set_id',
                            'text',
                            'en_text',
                            'language',
                            'call_id',
                            'paragraph_number',
                            'paragraph_start_time',
                            'example_source',
                            'status',
                            'created_at',
                            'updated_at',
                            'is_deleted',
                        ],
                        [
                            'data_set_example_id' => $dataSetExampleId,
                        ]
                    ) . '
                ) edse
                INNER JOIN (
                    ' . $this->getFinalTableSqlUsingGroupBy(
                        'ems_data_sets',
                        [
                            'data_set_id',
                        ],
                        'updated_at',
                        [],
                        [
                            'company_id' => $companyId,
                        ]
                    ) . '
                ) eds ON eds.data_set_id = edse.data_set_id
            WHERE 
                edse.is_deleted = ' . (int) $isRemoved
        ;
        // phpcs:enable

        $dataSetExampleData = $this->getClient()->selectOne($sql);

        if ($dataSetExampleData === null) {
            throw new NotFoundApiException('Data Set Example not found');
        }

        return $dataSetExampleData;
    }

    /**
     * @param array $dataSetsIds
     * @param array|null $statuses
     * @return array
     */
    public function exportDataSetExamples(array $dataSetsIds, ?array $statuses = null): array
    {
        // phpcs:disable
        $sql = '
            SELECT
                events.event_name event_name,
                eds.event_id event_id,
                edse.text text,
                edse.en_text en_text,
                edse.language language,
                edse.status status,
                dsee.additional_events additional_events
            FROM 
                (
                    ' . $this->getFinalTableSqlUsingGroupBy(
                        'ems_data_set_examples',
                        [
                            'data_set_example_id',
                        ],
                        'updated_at',
                        [
                            'data_set_id',
                            'text',
                            'en_text',
                            'language',
                            'call_id',
                            'paragraph_number',
                            'paragraph_start_time',
                            'example_source',
                            'status',
                            'created_at',
                            'updated_at',
                            'is_deleted',
                        ],
                        [
                            'data_set_id' => $dataSetsIds,
                        ]
                    ) . '
                ) edse
                INNER JOIN (
                    ' . $this->getFinalTableSqlUsingGroupBy(
                        'ems_data_sets',
                        [
                            'data_set_id',
                            'event_id',
                        ],
                        'updated_at',
                        [],
                        [
                            'is_deleted' => 0,
                            [
                                'type' => 'compare',
                                'column' => 'data_set_id',
                                'value' => $dataSetsIds,
                                'compare' => 'IN',
                            ],
                        ]
                    ) . '
                ) eds ON eds.data_set_id = edse.data_set_id
                INNER JOIN (
                    SELECT data_set_example_id,
                        groupArray(
                            map
                            (
                                 \'data_set_example_event_id\', toString(data_set_example_event_id),
                                 \'data_set_example_id\', toString(data_set_example_id),
                                 \'event_id\', toString(event_id),
                                 \'highlight\', highlight,
                                 \'en_highlight\', en_highlight,
                                 \'created_at\', toString(created_at),
                                 \'updated_at\', toString(updated_at),
                                 \'event_name\', events.event_name,
                                 \'is_deleted\', toString(is_deleted)
                            )
                        ) additional_events
                    FROM (
                        ' . $this->getFinalTableSqlUsingGroupBy(
                            'ems_data_set_examples_events',
                            [
                                'data_set_example_event_id',
                                'data_set_example_id',
                            ],
                            'updated_at',
                            [
                                'event_id',
                                'highlight',
                                'en_highlight',
                                'is_deleted',
                                'created_at',
                                'updated_at',
                            ],
                            [
                                'is_deleted' => 0,
                            ]
                        ) . '
                    ) dsee 
                    INNER JOIN
                        (
                            SELECT
                                event_id,
                                any(event_name) event_name
                            FROM
                                dictionary(events)
                            GROUP BY
                                event_id
                        ) events
                    ON events.event_id = dsee.event_id
                    GROUP BY data_set_example_id
                ) dsee ON dsee.data_set_example_id = edse.data_set_example_id
                INNER JOIN
                    (
                        SELECT
                            event_id,
                            any(event_name) event_name
                        FROM
                            dictionary(events)
                        GROUP BY
                            event_id
                    ) events
                    ON events.event_id = eds.event_id
                WHERE
                    edse.is_deleted = 0
        ';
        if (is_array($statuses)) {
            $sql .= '
                    AND edse.status IN (\'' . implode('\',\'', $statuses) . '\')
            ';
        }
        // phpcs:enable

        return $this->getClient()->selectAll($sql);
    }

    /**
     * @param string $dataSetId
     * @param string|array|null $status
     * @param bool $isRemoved
     * @return array
     */
    public function getDataSetExamples(string $dataSetId, string|array $status = null, bool $isRemoved = false): array
    {
        $statuses = is_array($status) ? $status : [$status];
        // phpcs:disable
        $sql = '
            SELECT
                dse.*,
                dsee.events
            FROM 
            (
                ' . $this->getFinalTableSqlUsingGroupBy(
                    'ems_data_set_examples',
                    [
                        'data_set_example_id',
                    ],
                    'updated_at',
                    [
                        'data_set_id',
                        'text',
                        'en_text',
                        'language',
                        'call_id',
                        'paragraph_number',
                        'paragraph_start_time',
                        'example_source',
                        'status',
                        'created_at',
                        'updated_at',
                        'is_deleted',
                    ],
                    [
                        'data_set_id' => $dataSetId,
                    ]
                ) . '
            ) dse
            INNER JOIN (
                SELECT data_set_example_id,
                    groupArray(
                        map
                        (
                             \'data_set_example_event_id\', toString(data_set_example_event_id),
                             \'data_set_example_id\', toString(data_set_example_id),
                             \'event_id\', toString(event_id),
                             \'highlight\', highlight,
                             \'en_highlight\', en_highlight,
                             \'created_at\', toString(created_at),
                             \'updated_at\', toString(updated_at),
                             \'event_fill_color_hex\', events.event_fill_color_hex,
                             \'event_outline_color_hex\', events.event_outline_color_hex,
                             \'event_icon\', events.event_icon,
                             \'event_name\', events.event_name,
                             \'is_deleted\', toString(is_deleted)
                        )
                    ) events
                FROM (
                    ' . $this->getFinalTableSqlUsingGroupBy(
                        'ems_data_set_examples_events',
                        [
                            'data_set_example_event_id',
                            'data_set_example_id',
                        ],
                        'updated_at',
                        [
                            'event_id',
                            'highlight',
                            'en_highlight',
                            'is_deleted',
                            'created_at',
                            'updated_at',
                        ],
                    ) . '
                ) dsee
                INNER JOIN
                    (
                        SELECT
                            event_id,
                            any(event_name) event_name,
                            any(icon) event_icon,
                            any(fill_color_hex) event_fill_color_hex,
                            any(outline_color_hex) event_outline_color_hex
                        FROM
                            dictionary(events)
                        GROUP BY
                            event_id
                    ) events
                    ON events.event_id = dsee.event_id
                GROUP BY data_set_example_id
            ) dsee ON dsee.data_set_example_id = dse.data_set_example_id
            WHERE is_deleted = ' . (int) $isRemoved
        ;
        // phpcs:enable

        if ($status !== null) {
            $sql .= '
                AND status IN (\'' . implode('\',\'', $statuses) . '\')
            ';
        }

        return $this->getClient()->selectAll($sql);
    }

    /**
     * @param string $dataSetId
     * @param string $status
     * @return int
     */
    public function getExamplesCount(string $dataSetId, string $status): int
    {
        // phpcs:disable
        $sql = '
            SELECT
                COUNT(*) count
            FROM
            (
                SELECT
                    data_set_example_id
                FROM 
                (
                    ' . $this->getFinalTableSqlUsingGroupBy(
                        'ems_data_set_examples',
                        [
                            'data_set_example_id',
                        ],
                        'updated_at',
                        [
                            'status',
                        ],
                        [
                            'data_set_id' => $dataSetId,
                        ]
                    ) . '
                )
                WHERE
                    status = \'' . $status . '\'
            )
        ';
        // phpcs:enable
        return (int) $this->getClient()->selectValue($sql);
    }

    public function saveDataSetExample(\STEms\Entity\DataSetExample $dataSetExample): void
    {
        $dataSetExampleArray = $dataSetExample->toArray();
        $dataSetExampleArray['is_deleted'] = (int) $dataSetExampleArray['is_deleted'];
        unset($dataSetExampleArray['events']);

        $this->getClient()->insert(
            $this->getTableName(),
            [
                $dataSetExampleArray,
            ],
            array_keys($dataSetExampleArray)
        );
    }

    /**
     * @param \STEms\Entity\DataSetExampleCollection $dataSetExamples
     * @return void
     */
    public function saveDataSetExamples(\STEms\Entity\DataSetExampleCollection $dataSetExamples): void
    {
        if ($dataSetExamples->isEmpty()) {
            return;
        }

        $dataSetExamplesArray = $dataSetExamples->toArray();

        foreach ($dataSetExamplesArray as &$dataSetExampleArray) {
            $dataSetExampleArray['is_deleted'] = (int) $dataSetExampleArray['is_deleted'];
            unset($dataSetExampleArray['events']);
        }
        unset($dataSetExampleArray);

        $this->getClient()->insert(
            $this->getTableName(),
            $dataSetExamplesArray,
            array_keys($dataSetExamplesArray[0])
        );
    }
}
