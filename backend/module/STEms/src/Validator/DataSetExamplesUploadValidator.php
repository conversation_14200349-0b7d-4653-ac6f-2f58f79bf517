<?php

declare(strict_types=1);

namespace STEms\Validator;

use STLib\Validator\Validator;

class DataSetExamplesUploadValidator extends Validator
{
    /**
     *
     * @see \STLib\Validator\ValidatorInterface::run()
     */
    public function run(): void
    {
        /** @var array $dataSet */
        $dataSetExample = $this->getInstance();

        if (empty($dataSetExample['file_content'])) {
            $this->addError('file_content', 'file_content is required');
        }
    }
}
