<?php

declare(strict_types=1);

namespace STEms\Validator;

use STEms\Service\EmsDataSetService;
use STLib\Validator\Validator;

class DataSetValidator extends Validator
{
    /**
     *
     * @see \STLib\Validator\ValidatorInterface::run()
     */
    public function run(): void
    {
        /** @var \STEms\Entity\DataSet $dataSet */
        $dataSet = $this->getInstance();

        if ($this->hasCheck('name')) {
            $nameValidator = new \Laminas\Validator\StringLength([
                'min' => 1,
                'max' => 511,
            ]);
            if (!$nameValidator->isValid($dataSet->getName())) {
                $this->addError('name', 'Data set name length must be between 1 and 511 characters');
            }
        }

        if ($this->hasCheck('status')) {
            $statusValidator = new \Laminas\Validator\InArray([
                'haystack' => [
                    \STEms\Data\EmsDataSetsTable::NOT_SENT_TO_TRAIN,
                    \STEms\Data\EmsDataSetsTable::SENT_TO_TRAIN,
                ],
                'strict' => true,
            ]);
            if (!$statusValidator->isValid($dataSet->getStatus())) {
                $this->addError('status', 'Invalid status value');
            }
        }

        if ($this->hasCheck('guideline')) {
            $nameValidator = new \Laminas\Validator\StringLength([
                'max' => 4096,
            ]);
            if (!$nameValidator->isValid($dataSet->getGuideline())) {
                $this->addError('guideline', 'Guideline data set length must be less than or equal to 4096 characters');
            }
        }
    }
}
