<?php

declare(strict_types=1);

namespace STEms\Entity;

use Carbon\Carbon;
use STEms\Data\EmsDataSetsTable;

class DataSet
{
    use \STLib\Mvc\Hydrator\BaseHydratorTrait;

    /**
     * @var string
     */
    public string $dataSetId;

    /**
     * @var int
     */
    public int $companyId;

    /**
     * @var int
     */
    public int $userId;

    /**
     * @var int
     */
    public int $eventId;

    /**
     * @var string|null
     */
    public ?string $name = null;

    /**
     * @var string
     */
    public string $guideline = '';

    /**
     * @var string
     */
    public string $positiveExampleText = '';

    /**
     * @var string
     */
    public string $positiveExampleHighlight = '';

    /**
     * @var string
     */
    public string $negativeExampleText = '';

    /**
     * @var string
     */
    public string $status = EmsDataSetsTable::NOT_SENT_TO_TRAIN;

    /**
     * @var Carbon|null
     */
    public ?Carbon $reviewedCallsExamplesLastUpdate = null;

    /**
     * @var Carbon|null
     */
    public ?Carbon $analyzedCallsExamplesLastUpdate = null;

    /**
     * @var bool
     */
    public bool $isDeleted = false;

    /**
     *
     * @var Carbon|null
     */
    protected ?Carbon $createdAt = null;

    /**
     *
     * @var ?Carbon
     */
    protected ?Carbon $updatedAt = null;

    /**
     * @return string
     */
    public function getDataSetId(): string
    {
        return $this->dataSetId;
    }

    /**
     * @param string $dataSetId
     * @return DataSet
     */
    public function setDataSetId(string $dataSetId): DataSet
    {
        $this->dataSetId = $dataSetId;

        return $this;
    }

    /**
     * @return int
     */
    public function getCompanyId(): int
    {
        return $this->companyId;
    }

    /**
     * @param int $companyId
     * @return DataSet
     */
    public function setCompanyId(int $companyId): DataSet
    {
        $this->companyId = $companyId;

        return $this;
    }

    /**
     * @return int
     */
    public function getUserId(): int
    {
        return $this->userId;
    }

    /**
     * @param int $userId
     * @return DataSet
     */
    public function setUserId(int $userId): DataSet
    {
        $this->userId = $userId;

        return $this;
    }

    /**
     * @return int
     */
    public function getEventId(): int
    {
        return $this->eventId;
    }

    /**
     * @param int $eventId
     * @return DataSet
     */
    public function setEventId(int $eventId): DataSet
    {
        $this->eventId = $eventId;

        return $this;
    }

    /**
     * @return string|null
     */
    public function getName(): ?string
    {
        return $this->name;
    }

    /**
     * @param string|null $name
     * @return $this
     */
    public function setName(?string $name): DataSet
    {
        $this->name = $name;

        return $this;
    }

    /**
     * @return string
     */
    public function getGuideline(): string
    {
        return $this->guideline;
    }

    /**
     * @param string $guideline
     * @return DataSet
     */
    public function setGuideline(string $guideline): DataSet
    {
        $this->guideline = $guideline;

        return $this;
    }

    /**
     * @return string
     */
    public function getPositiveExampleText(): string
    {
        return $this->positiveExampleText;
    }

    /**
     * @param string $positiveExampleText
     * @return $this
     */
    public function setPositiveExampleText(string $positiveExampleText): DataSet
    {
        $this->positiveExampleText = $positiveExampleText;

        return $this;
    }

    /**
     * @return string
     */
    public function getNegativeExampleText(): string
    {
        return $this->negativeExampleText;
    }

    /**
     * @param string $negativeExampleText
     * @return $this
     */
    public function setNegativeExampleText(string $negativeExampleText): DataSet
    {
        $this->negativeExampleText = $negativeExampleText;

        return $this;
    }

    /**
     * @return string
     */
    public function getPositiveExampleHighlight(): string
    {
        return $this->positiveExampleHighlight;
    }

    /**
     * @param string $positiveExampleHighlight
     * @return $this
     */
    public function setPositiveExampleHighlight(string $positiveExampleHighlight): DataSet
    {
        $this->positiveExampleHighlight = $positiveExampleHighlight;

        return $this;
    }

    /**
     * @return string
     */
    public function getStatus(): string
    {
        return $this->status;
    }

    /**
     * @param string $status
     * @return DataSet
     */
    public function setStatus(string $status): DataSet
    {
        $this->status = $status;

        return $this;
    }

    /**
     * @return Carbon|null
     */
    public function getReviewedCallsExamplesLastUpdate(): ?Carbon
    {
        return $this->reviewedCallsExamplesLastUpdate;
    }

    /**
     * @param Carbon|string|null $reviewedCallsExamplesLastUpdate
     * @return DataSet
     */
    public function setReviewedCallsExamplesLastUpdate(null|Carbon|string $reviewedCallsExamplesLastUpdate): DataSet
    {
        $this->reviewedCallsExamplesLastUpdate = is_string($reviewedCallsExamplesLastUpdate)
            ? Carbon::parse($reviewedCallsExamplesLastUpdate)
            : $reviewedCallsExamplesLastUpdate;
        return $this;
    }

    /**
     * @return Carbon|null
     */
    public function getAnalyzedCallsExamplesLastUpdate(): ?Carbon
    {
        return $this->analyzedCallsExamplesLastUpdate;
    }

    /**
     * @param Carbon|string|null $analyzedCallsExamplesLastUpdate
     * @return DataSet
     */
    public function setAnalyzedCallsExamplesLastUpdate(null|Carbon|string $analyzedCallsExamplesLastUpdate): DataSet
    {
        $this->analyzedCallsExamplesLastUpdate = is_string($analyzedCallsExamplesLastUpdate)
            ? Carbon::parse($analyzedCallsExamplesLastUpdate)
            : $analyzedCallsExamplesLastUpdate;
        return $this;
    }

    /**
     * @return bool
     */
    public function getIsDeleted(): bool
    {
        return $this->isDeleted;
    }

    /**
     * @param bool $isDeleted
     * @return DataSet
     */
    public function setIsDeleted(bool $isDeleted): DataSet
    {
        $this->isDeleted = $isDeleted;

        return $this;
    }

    /**
     * @param bool|null $isDeleted
     * @return bool|DataSet
     */
    public function isDeleted(bool $isDeleted = null): DataSet|bool
    {
        if (is_null($isDeleted)) {
            return $this->isDeleted;
        }
        $this->isDeleted = $isDeleted;
        return $this;
    }

    /**
     * @return ?Carbon
     */
    public function getCreatedAt(): ?Carbon
    {
        return $this->createdAt;
    }

    /**
     * @param string|Carbon $createdAt
     * @return DataSet
     */
    public function setCreatedAt(string|Carbon $createdAt): DataSet
    {
        $this->createdAt = is_string($createdAt) ? Carbon::parse($createdAt) : $createdAt;

        return $this;
    }

    /**
     * @return ?Carbon
     */
    public function getUpdatedAt(): ?Carbon
    {
        return $this->updatedAt;
    }

    /**
     * @param string|Carbon $updatedAt
     * @return DataSet
     */
    public function setUpdatedAt(string|Carbon $updatedAt): DataSet
    {
        $this->updatedAt = is_string($updatedAt) ? Carbon::parse($updatedAt) : $updatedAt;

        return $this;
    }

    /**
     *
     * @return array
     */
    public function toArray(): array
    {
        return $this->extract($this);
    }
}
