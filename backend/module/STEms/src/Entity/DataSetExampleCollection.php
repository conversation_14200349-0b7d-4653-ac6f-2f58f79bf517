<?php

declare(strict_types=1);

namespace STEms\Entity;

use STLib\Expand\Collection;

class DataSetExampleCollection extends \STLib\Expand\Collection
{
    /**
     *
     * @param DataSetExample $dataSetExample
     * @param string|int|null $key
     * @return Collection
     */
    public function add(mixed $dataSetExample, string|int|null $key = null): \STLib\Expand\Collection
    {
        if (!($dataSetExample instanceof DataSetExample)) {
            throw new \RuntimeException('Call must be an instance of "\STEms\Entity\DataSetExample"');
        }
        parent::add($dataSetExample, $key ?? $dataSetExample->getDataSetExampleId());
        return $this;
    }

    /**
     *
     * @return array
     */
    public function toArray(): array
    {
        $result = [];
        foreach ($this as $dataSetExample) {
            $result[] = $dataSetExample->toArray();
        }
        return $result;
    }
}
