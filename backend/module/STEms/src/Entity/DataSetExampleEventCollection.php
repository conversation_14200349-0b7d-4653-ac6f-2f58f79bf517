<?php

declare(strict_types=1);

namespace STEms\Entity;

use STLib\Expand\Collection;

class DataSetExampleEventCollection extends \STLib\Expand\Collection
{
    /**
     *
     * @param mixed $dataSetExampleEvent
     * @param string|int|null $key
     * @return Collection
     */
    public function add(mixed $dataSetExampleEvent, string|int|null $key = null): \STLib\Expand\Collection
    {
        if (!($dataSetExampleEvent instanceof DataSetExampleEvent)) {
            throw new \RuntimeException('Object must be an instance of "\STEms\Entity\DataSetExampleEvent"');
        }
        parent::add($dataSetExampleEvent, $key ?? $dataSetExampleEvent->getDataSetExampleEventId());
        return $this;
    }

    /**
     *
     * @param array $attributes
     * @return array
     */
    public function toArray(array $attributes = []): array
    {
        $result = [];

        /** @var DataSetExampleEvent $dataSetExampleEvent */
        foreach ($this as $dataSetExampleEvent) {
            $result[] = $dataSetExampleEvent->toArray($attributes);
        }
        return $result;
    }
}
