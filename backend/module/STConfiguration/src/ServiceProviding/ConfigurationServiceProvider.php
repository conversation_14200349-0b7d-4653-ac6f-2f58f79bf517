<?php

declare(strict_types=1);

namespace STConfiguration\ServiceProviding;

use STAlgo\Service\Interfaces\ConfigurationInterface as AlgoConfigurationInterface;
use STOnboarding\Service\Interfaces\ConfigurationInterface as OnboardingConfigurationInterface;
use STSlack\Service\Interfaces\ConfigurationInterface as SlackConfigurationInterface;
use STCall\Service\Interfaces\ConfigurationInterface as CallConfigurationInterface;
use STConfiguration\Service\ConfigurationService;
use STTranslation\Service\Interfaces\ConfigurationInterface;
use STUser\Service\Interfaces\ConfigurationInterface as UserConfigurationInterface;

final readonly class ConfigurationServiceProvider implements
    AlgoConfigurationInterface,
    OnboardingConfigurationInterface,
    SlackConfigurationInterface,
    CallConfigurationInterface,
    ConfigurationInterface,
    UserConfigurationInterface
{
    public function __construct(private ConfigurationService $configuration)
    {
    }

    public function get(string $name): array
    {
        return $this->configuration->get($name);
    }
}
